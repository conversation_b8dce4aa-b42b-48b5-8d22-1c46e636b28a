import config from '@/config'
import storage from '@/utils/storage'
import constant from '@/utils/constant'
import { login, logout, getInfo } from '@/api/login'
import { getUserProfile } from '@/api/system/user'
import { getToken, setToken, removeToken,setPassWord,setUserName, getUserName } from '@/utils/auth'

const baseUrl = config.baseUrl

const user = {
  state: {
    token: getToken(),
    name: storage.get(constant.name),
    avatar: storage.get(constant.avatar),
    roles: storage.get(constant.roles),
    permissions: storage.get(constant.permissions)
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      // 同时更新store状态和本地存储，确保同步
      state.token = token
      if (token && token.trim() !== '') {
        setToken(token)
        console.log('SET_TOKEN: Token已设置到store和本地存储')
      } else {
        removeToken()
        console.log('SET_TOKEN: Token已从store和本地存储中清除')
      }
    },
    SET_NAME: (state, name) => {
      state.name = name
      storage.set(constant.name, name)
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
      storage.set(constant.avatar, avatar)
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
      storage.set(constant.roles, roles)
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions
      storage.set(constant.permissions, permissions)
    }
  },

  actions: {
    // 登录
    Login({ commit }, userInfo) {
      // const company = userInfo.company.trim()
      // const phonenumber = userInfo.phonenumber.trim()
      const username = userInfo.username.trim()
      const password = userInfo.password
      const code = userInfo.code
      const uuid = userInfo.uuid
	  
	  if(username===getUserName()){
      
	  }else{
		const userInformations = {
			name:'',//病患姓名
			sex:'',//性别
			dateBirth:'',//出生年月
			phonenumber:'',//手机号
			occupation:'',//职业
			maritalStatus:'',//婚姻状况
			height:'',//身高
			weight:'',//体重
			diastolicPressure:'',//舒张压
			systolicPressure:'',//收缩压
			allergy:'',//过敏史
			medicalHistory:'',//病史
			age:'',//年龄
			TimeStamp: new Date().getTime(),//时间戳
			UserseeionType: 3//用户会话类型
		};
		uni.setStorageSync('User-Informations', userInformations)
	  }
	  
      return new Promise((resolve, reject) => {
        login(/* company, phonenumber, */ username, password, code, uuid).then(res => {
          // 只调用commit，SET_TOKEN mutation会自动同步到本地存储
          commit('SET_TOKEN', res.tokenInfo.token)
          // 保存用户名和密码
		  setUserName(username)
		  setPassWord(password)
          console.log('Login: Token已通过mutation同步设置')
          resolve()
        }).catch(error => {
          console.error('Login: 登录失败', error)
          reject(error)
        })
      })
    },

    // 获取用户信息
    GetInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        // 使用getUserProfile API获取完整的用户信息，包含昵称
        getUserProfile().then(res => {
          const user = res.data
          const avatar = (user == null || user.avatar == "" || user.avatar == null) ? require("@/static/images/profile.jpg") : baseUrl + user.avatar
          // 优先显示昵称，如果昵称为空则显示用户名
          const displayName = (user == null || user.nickName == "" || user.nickName == null)
            ? (user == null || user.userName == "" || user.userName == null) ? "" : user.userName
            : user.nickName

          console.log('GetInfo - 用户数据:', user)
          console.log('GetInfo - 显示名称:', displayName)

          // 使用getInfo获取角色权限信息
          getInfo().then(infoRes => {
            if (infoRes.roles && infoRes.roles.length > 0) {
              commit('SET_ROLES', infoRes.roles)
              commit('SET_PERMISSIONS', infoRes.permissions)
            } else {
              commit('SET_ROLES', ['ROLE_DEFAULT'])
            }
            commit('SET_NAME', displayName)
            commit('SET_AVATAR', avatar)
            resolve(res)
          }).catch(infoError => {
            // 即使获取角色权限失败，也要设置用户基本信息
            console.warn('获取角色权限失败:', infoError)
            commit('SET_ROLES', ['ROLE_DEFAULT'])
            commit('SET_NAME', displayName)
            commit('SET_AVATAR', avatar)
            resolve(res)
          })
        }).catch(error => {
          console.error('GetInfo - 获取用户信息失败:', error)
          reject(error)
        })
      })
    },

    // 退出系统
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        logout(state.token).then(() => {
          // 使用SET_TOKEN mutation清理token，会自动同步本地存储
          commit('SET_TOKEN', '')
          commit('SET_ROLES', [])
          commit('SET_PERMISSIONS', [])
          commit('SET_NAME', '')
          commit('SET_AVATAR', '')
          // 清理其他存储数据
          storage.clean()
          console.log('LogOut: 完全退出，所有数据已清理')
          resolve()
        }).catch(error => {
          console.error('LogOut: 退出登录失败', error)
          reject(error)
        })
      })
    },

    // 退出系统但保留用户名和密码
    LogOutKeepCredentials({ commit, state }) {
      return new Promise((resolve, reject) => {
        logout(state.token).then(() => {
          // 使用SET_TOKEN mutation清理token，会自动同步本地存储
          commit('SET_TOKEN', '')
          commit('SET_ROLES', [])
          commit('SET_PERMISSIONS', [])
          commit('SET_NAME', '')
          commit('SET_AVATAR', '')
          // 不调用 storage.clean()，保留用户名和密码
          console.log('LogOutKeepCredentials: 退出登录但保留用户名密码')
          resolve()
        }).catch(error => {
          console.error('LogOutKeepCredentials: 退出登录失败', error)
          reject(error)
        })
      })
    }
  }
}

export default user
