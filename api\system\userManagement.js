/**
 * 用户信息管理API接口
 *
 * 接口说明：
 * - 新增用户：/common/insertPersonnalApp (PUT)
 * - 修改用户：/common/updateApp (PUT)
 * - 删除用户：/common/deleteApp (DELETE, 参数: UserseeionType)
 *
 *
 * api数据字段说明：
 * - UserseeionType: 用户唯一标识符
 * - person_name: 姓名  //与本机字段不同，需要转化 本机为name
 * - sex: 性别
 * - dateBirth: 出生年月
 * - phone: 手机号  //与本机不同，需要转化 本机为phonenumber
 * - occupation: 职业
 * - maritalStatus: 婚姻状况
 * - height: 身高
 * - weight: 体重
 * - age: 年龄
 * - diastolicPressure: 舒张压
 * - systolicPressure: 收缩压
 * - allergy: 过敏史
 * - medicalHistory: 病史
 */
import request from '@/utils/request'

// 新增用户信息
export function addUser(data) {
  return request({
    url: '/common/insertPersonnalApp',
    method: 'put',
    data: data
  })
}

// 修改用户信息
export function updateUser(data) {
  return request({
    url: '/common/updateApp',
    method: 'put',
    data: data
  })
}

// 查询用户信息
export function getUser() {
  return request({
    url: '/common/selectPersonnalApp',
    method: 'get',
  })
}