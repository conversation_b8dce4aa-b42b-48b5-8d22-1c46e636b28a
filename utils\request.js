import store from '@/store'
import config from '@/config'
import { getToken, handleTokenExpired, getTokenExpiredHandlingStatus } from '@/utils/auth'
import errorCode from '@/utils/errorCode'
import { toast, showConfirm, tansParams } from '@/utils/common'

let timeout = 60000
const baseUrl = config.baseUrl

const request = config => {
  // 是否需要设置 token
  const isToken = (config.headers || {}).isToken === false
  config.header = config.header || {}
  if (getToken() && !isToken) {
    config.header['Authorization'] = 'Bearer ' + getToken()
  }
  // get请求映射params参数
  if (config.params) {
    let url = config.url + '?' + tansParams(config.params)
    url = url.slice(0, -1)
    config.url = url
  }
  return new Promise((resolve, reject) => {
    // 添加请求日志
    console.log('=== HTTP请求详情 ===')
    console.log('请求方法:', config.method || 'get')
    console.log('请求URL:', config.baseUrl || baseUrl + config.url)
    console.log('请求数据:', JSON.stringify(config.data, null, 2))
    console.log('请求头:', config.header)

    uni.request({
        method: config.method || 'get',
        timeout: config.timeout ||  timeout,
        url: config.baseUrl || baseUrl + config.url,
        data: config.data,
        header: config.header,
        dataType: 'json'
      }).then(response => {
        let [error, res] = response

        // 添加响应日志
        console.log('=== HTTP响应详情 ===')
        console.log('响应错误:', error)
        console.log('响应数据:', res)

        if (error) {
          console.error('请求失败:', error)
          toast('后端接口连接异常')
          reject('后端接口连接异常')
          return
        }
        const code = res.data.code || 200
        // 过滤掉无意义的错误消息
        let msg = errorCode[code]
        if (!msg) {
          const serverMsg = res.data.msg
          // 检查服务器返回的消息是否有意义
          if (serverMsg && serverMsg !== '0' && serverMsg !== 'null' && serverMsg !== 'undefined' && String(serverMsg).trim() !== '') {
            msg = serverMsg
          } else {
            msg = errorCode['default']
          }
        }

        console.log('响应状态码:', code)
        console.log('响应消息:', msg)
        if (code === 401) {
          // Token过期或无效，检查是否已在处理中
          if (!getTokenExpiredHandlingStatus()) {
            console.log('检测到401错误，调用token过期处理')
            handleTokenExpired()
          } else {
            console.log('Token过期处理已在进行中，跳过重复处理')
          }
          reject('无效的会话，或者会话已过期，请重新登录。')
        } else if (code === 500) {
          toast(msg)
          reject(msg)
        } else if (code === 601) {
          // 601错误不显示toast，让页面自己处理错误信息
          reject(code)
        } else if (code !== 200) {
          toast(msg)
          reject(code)
        }
        resolve(res.data)
      })
      .catch(error => {
        let { message } = error
        if (message === 'Network Error') {
          message = '后端接口连接异常'
        } else if (message.includes('timeout')) {
          message = '系统接口请求超时'
        } else if (message.includes('Request failed with status code')) {
          message = '系统接口' + message.substr(message.length - 3) + '异常'
        }
        toast(message)
        reject(error)
      })
  })
}

export default request
