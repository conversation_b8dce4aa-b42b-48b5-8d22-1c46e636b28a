import { newAppVersionAndUrl } from '@/api/system/upVersion.js'

// 版本信息缓存相关常量
const VERSION_CACHE_KEY = 'app_version_cache';
const CACHE_EXPIRE_TIME = 2 * 60 * 60 * 1000; // 2小时过期

// Token检测相关常量
const TOKEN_CHECK_KEY = 'token_check_status';
let tokenCheckTimer = null;

/**
 * 比较两个版本号
 * @param {string} version1 当前版本
 * @param {string} version2 新版本
 * @returns {number} -1: version1 < version2, 0: 相等, 1: version1 > version2
 */
export function compareVersions(version1, version2) {
  const toVersionArray = (ver) =>
    ver.split('.').map(part => {
      const num = parseInt(part, 10);
      return isNaN(num) ? 0 : num;
    });

  const v1Parts = toVersionArray(version1);
  const v2Parts = toVersionArray(version2);

  const maxLength = Math.max(v1Parts.length, v2Parts.length);

  for (let i = 0; i < maxLength; i++) {
    const v1Part = v1Parts[i] || 0;
    const v2Part = v2Parts[i] || 0;

    if (v1Part < v2Part) return -1;
    if (v1Part > v2Part) return 1;
  }

  return 0;
}

/**
 * 缓存版本信息
 * @param {string} version 版本号
 * @param {string} downloadUrl 下载地址
 */
export function cacheVersionInfo(version, downloadUrl) {
  const cacheData = {
    version,
    downloadUrl,
    timestamp: Date.now()
  };
  uni.setStorageSync(VERSION_CACHE_KEY, cacheData);
  console.log('版本信息已缓存:', cacheData);
}

/**
 * 获取缓存的版本信息
 * @returns {object|null} 缓存的版本信息或null
 */
export function getCachedVersionInfo() {
  try {
    const cached = uni.getStorageSync(VERSION_CACHE_KEY);
    if (cached && cached.timestamp) {
      const now = Date.now();
      if (now - cached.timestamp < CACHE_EXPIRE_TIME) {
        console.log('使用缓存的版本信息:', cached);
        return cached;
      } else {
        console.log('版本信息缓存已过期');
        uni.removeStorageSync(VERSION_CACHE_KEY);
      }
    }
  } catch (error) {
    console.error('读取版本缓存失败:', error);
  }
  return null;
}

/**
 * 统一的版本检查函数
 * @param {boolean} showNoUpdateToast 是否显示"已是最新版本"提示
 * @param {boolean} forceRefresh 是否强制刷新，跳过缓存直接调用API
 * @returns {Promise}
 */
export async function checkForUpdates(showNoUpdateToast = false, forceRefresh = false) {
  try {
    console.log('开始检查版本更新...', forceRefresh ? '(强制刷新)' : '(使用缓存)');

    // 检查网络状态
    const networkType = await checkNetworkStatus();
    if (!networkType || networkType === 'none') {
      console.log('网络不可用，跳过版本检查');
      if (showNoUpdateToast) {
        uni.showToast({
          title: '网络不可用，请检查网络连接',
          icon: 'none'
        });
      }
      return false;
    }

    // 根据forceRefresh参数决定是否使用缓存
    let versionInfo = null;
    if (forceRefresh) {
      console.log('强制刷新：清除现有缓存，直接请求API获取版本信息');
      // 清除现有缓存
      uni.removeStorageSync(VERSION_CACHE_KEY);
    } else {
      versionInfo = getCachedVersionInfo();
    }

    if (!versionInfo) {
      console.log(forceRefresh ? '强制刷新：请求API获取版本信息' : '缓存不存在或已过期，请求API获取版本信息');

      try {
        const response = await newAppVersionAndUrl();
        console.log('版本检查API响应:', response);
        console.log('响应类型:', typeof response);
        console.log('响应结构:', JSON.stringify(response, null, 2));

        // 更详细的响应验证
        if (!response) {
          throw new Error('API无响应');
        }

        // 使用统一的版本信息解析函数
        versionInfo = parseVersionFromResponse(response);
        console.log('解析后的版本信息:', versionInfo);

        // 缓存版本信息
        cacheVersionInfo(versionInfo.version, versionInfo.downloadUrl);

      } catch (apiError) {
        console.error('API请求失败:', apiError);

        // 尝试使用过期的缓存作为降级方案
        const expiredCache = getExpiredCacheVersionInfo();
        if (expiredCache) {
          console.log('使用过期缓存作为降级方案');
          versionInfo = expiredCache;
        } else {
          throw apiError;
        }
      }
    }

    if (versionInfo) {
      const currentVersion = getApp().globalData.config.appInfo.version;
      const comparison = compareVersions(currentVersion, versionInfo.version);

      console.log(`版本比较: ${currentVersion} vs ${versionInfo.version}, 结果: ${comparison}`);

      if (comparison < 0) {
        // 有新版本可用，但需要检查是否已有安装提醒
        const updatePending = uni.getStorageSync('update_pending');
        if (updatePending && (updatePending.status === 'downloaded' || updatePending.status === 'ready_to_install')) {
          console.log('检测到有待安装的更新包，跳过显示新版本弹窗');
          // 如果是手动检查更新，提示用户有待安装的更新
          if (showNoUpdateToast) {
            uni.showToast({
              title: '有安装包待安装，请先完成安装',
              icon: 'none'
            });
          }
          return true;
        }

        // 没有待安装的更新包，显示新版本弹窗
        showUpdateDialog(versionInfo.version, versionInfo.downloadUrl);
        return true;
      } else if (showNoUpdateToast) {
        // 手动检查时显示已是最新版本
        uni.showToast({
          title: '当前已是最新版本',
          icon: 'none'
        });
      }
      return false;
    }
  } catch (error) {
    console.error('版本检查失败:', error);

    // 根据错误类型提供不同的用户提示
    let errorMessage = '检查更新失败';

    if (error.message) {
      if (error.message.includes('网络')) {
        errorMessage = '网络连接异常，请检查网络';
      } else if (error.message.includes('API')) {
        errorMessage = '服务器异常，请稍后重试';
      } else if (error.message.includes('超时')) {
        errorMessage = '请求超时，请稍后重试';
      }
    }

    if (showNoUpdateToast) {
      uni.showToast({
        title: errorMessage,
        icon: 'none'
      });
    }

    return false;
  }
}

/**
 * 检查网络状态
 * @returns {Promise<string>} 网络类型
 */
function checkNetworkStatus() {
  return new Promise((resolve) => {
    uni.getNetworkType({
      success: (res) => {
        console.log('网络类型:', res.networkType);
        resolve(res.networkType);
      },
      fail: (err) => {
        console.error('获取网络状态失败:', err);
        resolve('unknown');
      }
    });
  });
}

/**
 * 解析API响应中的版本信息
 * @param {Object} response API响应
 * @returns {Object|null} 解析后的版本信息
 */
function parseVersionFromResponse(response) {
  if (!response) return null;

  let versionData = null;

  // 格式1: 直接包含 version 和 url 字段
  if (response.version && response.url) {
    versionData = {
      version: response.version,
      url: response.url
    };
    console.log('使用格式1: 直接字段');
  }
  
  if (!versionData || !versionData.version || !versionData.url) {
    throw new Error('API响应缺少必要字段(version/url)');
  }

  return {
    version: versionData.version,
    downloadUrl: versionData.url
  };
}

/**
 * 获取过期的缓存版本信息（降级方案）
 * @returns {object|null}
 */
function getExpiredCacheVersionInfo() {
  try {
    const cached = uni.getStorageSync(VERSION_CACHE_KEY);
    if (cached && cached.version && cached.downloadUrl) {
      console.log('找到过期缓存:', cached);
      return {
        version: cached.version,
        downloadUrl: cached.downloadUrl
      };
    }
  } catch (error) {
    console.error('读取过期缓存失败:', error);
  }
  return null;
}

/**
 * 显示版本更新对话框
 * @param {string} newVersion 新版本号
 * @param {string} downloadUrl 下载地址
 */
export function showUpdateDialog(newVersion, downloadUrl) {
  const currentVersion = getApp().globalData.config.appInfo.version;

  uni.showModal({
    title: '发现新版本',
    content: `检测到新版本 ${newVersion}\n当前版本 ${currentVersion}\n\n新版本包含功能改进和问题修复，建议您及时更新以获得更好的使用体验。`,
    cancelText: '稍后提醒',
    confirmText: '立即更新',
    success: function(res) {
      if (res.confirm) {
        startDownload(newVersion, downloadUrl);
      }
    }
  });
}

/**
 * 开始下载更新包
 * @param {string} newVersion 新版本号
 * @param {string} downloadUrl 下载地址
 */
export function startDownload(newVersion, downloadUrl) {
  console.log('开始下载更新包:', downloadUrl);

  // 尝试通过事件通知 pop 组件显示进度下载
  // 发送全局事件，让 pop 组件监听并处理
  uni.$emit('startDownloadWithProgress', {
    newVersion,
    downloadUrl
  });

  // 给 pop 组件一点时间来响应事件，然后检查是否被处理
  setTimeout(() => {
    const popHandled = uni.getStorageSync('pop_download_handled');
    if (popHandled) {
      console.log('Pop 组件已处理下载请求');
      uni.removeStorageSync('pop_download_handled');
      return;
    }

    // 如果没有被处理，使用简单下载方式
    startSimpleDownload(newVersion, downloadUrl);
  }, 100);
}

/**
 * 简单下载方式（不显示详细进度）
 * @param {string} newVersion 新版本号
 * @param {string} downloadUrl 下载地址
 */
function startSimpleDownload(newVersion, downloadUrl) {
  console.log('使用简单 loading 显示下载进度');

  // 记录更新状态，用于安装后检查
  uni.setStorageSync('update_pending', {
    version: newVersion,
    timestamp: Date.now(),
    status: 'downloading'
  });

  uni.showLoading({
    title: "正在下载更新包...",
    mask: true
  });

  const downloadTask = uni.downloadFile({
    url: downloadUrl,
    success: (res) => {
      uni.hideLoading();
      if (res.statusCode === 200) {
        console.log("更新包下载完成");
        // 更新下载状态，保存安装包路径
        uni.setStorageSync('update_pending', {
          version: newVersion,
          timestamp: Date.now(),
          status: 'downloaded',
          tempFilePath: res.tempFilePath
        });
        showInstallDialog(newVersion, res.tempFilePath);
      } else {
        // 清除更新状态
        uni.removeStorageSync('update_pending');
        uni.showModal({
          title: '下载失败',
          content: `下载失败，状态码: ${res.statusCode}`,
          showCancel: false
        });
      }
    },
    fail: (err) => {
      uni.hideLoading();
      console.error('下载更新包失败:', err);
      // 清除更新状态
      uni.removeStorageSync('update_pending');
      uni.showModal({
        title: '下载失败',
        content: '更新包下载失败，请检查网络连接或稍后重试。',
        confirmText: '重试',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            startDownload(newVersion, downloadUrl);
          }
        }
      });
    }
  });

  // 下载进度监听
  downloadTask.onProgressUpdate((res) => {
    const progress = res.progress || 0;
    const downloaded = (res.totalBytesWritten / 1024 / 1024).toFixed(2);
    const total = (res.totalBytesExpectedToWrite / 1024 / 1024).toFixed(2);

    console.log(`下载进度: ${progress}%, ${downloaded}MB/${total}MB`);

    // 更新loading文案显示进度
    if (progress > 0) {
      uni.showLoading({
        title: `下载中 ${progress}%`,
        mask: true
      });
    }
  });
}

/**
 * 显示安装确认对话框
 * @param {string} newVersion 新版本号
 * @param {string} tempFilePath 临时文件路径
 */
export function showInstallDialog(newVersion, tempFilePath) {
  // 更新安装状态，保存安装包路径
  uni.setStorageSync('update_pending', {
    version: newVersion,
    timestamp: Date.now(),
    status: 'ready_to_install',
    tempFilePath: tempFilePath
  });

  uni.showModal({
    title: '安装提示',
    content: `数智舌诊 ${newVersion} 安装包下载完成！\n\n点击"立即安装"将跳转到系统安装界面\n\n安装完成后请手动重新打开应用`,
    cancelText: '稍后安装',
    confirmText: '立即安装',
    success: function(res) {
      if (res.confirm) {
        installUpdate(newVersion, tempFilePath);
      }
    }
  });
}

/**
 * 安装更新包
 * @param {string} newVersion 新版本号
 * @param {string} tempFilePath 临时文件路径
 */
export function installUpdate(newVersion, tempFilePath) {
  console.log('开始安装更新包:', tempFilePath);

  // #ifdef APP-PLUS
  // 更新安装状态
  uni.setStorageSync('update_pending', {
    version: newVersion,
    timestamp: Date.now(),
    status: 'installing'
  });

  // 设置本地通知，提醒用户重新打开应用
  try {
    plus.push.createMessage(
      `数智舌诊已更新到${newVersion}，点击重新打开应用`,
      "update_complete",
      {
        when: new Date(Date.now() + 10000) // 10秒后提醒
      }
    );
  } catch(e) {
    console.log('设置通知失败:', e);
  }

  plus.runtime.install(tempFilePath, {
    force: false // 不强制安装，让用户确认
  }, () => {
    // 这个回调可能不会执行，因为应用进程可能被终止
    console.log('安装成功回调执行');
    try {
      // 标记安装完成，版本验证将在应用重启后进行
      uni.setStorageSync('update_pending', {
        version: newVersion,
        timestamp: Date.now(),
        status: 'installed'
      });

      // 显示安装完成提示，但不显示更新成功，因为需要重启后验证版本
      uni.showToast({
        title: '安装完成，即将重启',
        icon: 'success'
      });

      // 尝试重启，重启后会在App.vue中验证版本并显示最终结果
      setTimeout(() => {
        plus.runtime.restart();
      }, 1000);
    } catch(e) {
      console.log('安装成功回调处理失败:', e);
    }
  }, (error) => {
    console.error('安装失败:', error);
    // 清除更新状态
    uni.removeStorageSync('update_pending');

    uni.showModal({
      title: '安装失败',
      content: error.message || '安装过程中发生错误，请重试。',
      showCancel: false
    });
  });
  // #endif

  // #ifndef APP-PLUS
  uni.showModal({
    title: '提示',
    content: '当前环境不支持自动安装，请手动安装下载的安装包。',
    showCancel: false
  });
  // #endif
}

/**
 * 清理更新相关的缓存数据
 */
export function clearUpdateCache() {
  try {
    uni.removeStorageSync('update_pending');
    uni.removeStorageSync('version_check_cache');
    console.log('更新缓存已清理');
  } catch(e) {
    console.error('清理更新缓存失败:', e);
  }
}

/**
 * 获取当前更新状态
 */
export function getUpdateStatus() {
  try {
    return uni.getStorageSync('update_pending');
  } catch(e) {
    console.error('获取更新状态失败:', e);
    return null;
  }
}

/**
 * 检查是否有待处理的更新
 */
export function hasPendingUpdate() {
  const updateStatus = getUpdateStatus();
  if (!updateStatus) return false;

  const timeDiff = Date.now() - updateStatus.timestamp;
  // 如果超过2小时，认为无效
  if (timeDiff > 2 * 60 * 60 * 1000) {
    clearUpdateCache();
    return false;
  }

  return ['downloaded', 'ready_to_install', 'installing'].includes(updateStatus.status);
}

/**
 * 验证版本更新是否成功
 * @param {string} expectedVersion 期望的版本号
 * @returns {boolean} 是否更新成功
 */
export function verifyUpdateSuccess(expectedVersion) {
  try {
    const currentVersion = getApp().globalData.config.appInfo.version;
    console.log(`验证版本更新: 当前版本 ${currentVersion}, 期望版本 ${expectedVersion}`);

    const comparison = compareVersions(currentVersion, expectedVersion);
    const isSuccess = comparison >= 0; // 当前版本大于等于期望版本表示更新成功

    console.log(`版本验证结果: ${isSuccess ? '成功' : '失败'}`);
    return isSuccess;
  } catch (error) {
    console.error('验证版本更新失败:', error);
    return false;
  }
}

// 保持向后兼容的原有函数
export function checkVersion(newVersion, newVersionUrl) {
  const currentVersion = getApp().globalData.config.appInfo.version;
  console.log('使用旧版本检查函数:', currentVersion, 'vs', newVersion);

  const comparison = compareVersions(currentVersion, newVersion);
  if (comparison >= 0) {
    uni.showToast({
      title: '当前已是最新版本',
      icon: 'none'
    });
    return;
  }

  // 检查是否已有安装提醒
  const updatePending = uni.getStorageSync('update_pending');
  if (updatePending && (updatePending.status === 'downloaded' || updatePending.status === 'ready_to_install')) {
    console.log('旧版本检查函数发现有待安装的更新包，跳过显示新版本弹窗');
    uni.showToast({
      title: '有安装包待安装，请先完成安装',
      icon: 'none'
    });
    return;
  }

  showUpdateDialog(newVersion, newVersionUrl);
}

/**
 * ==================== Token检测功能 ====================
 */

/**
 * 通过版本更新接口检测Token是否过期，并更新版本缓存
 * @param {boolean} showNoUpdateToast 是否显示"已是最新版本"提示
 * @returns {Promise<boolean>} Token是否有效
 */
export async function checkTokenByVersionAPI(showNoUpdateToast = false) {
  try {
    console.log('通过版本更新接口检测Token状态...')

    // Token检测每次都直接请求接口，不使用任何缓存和频率限制
    console.log('Token检测：每次都直接请求接口，确保及时发现token过期')

    // 检查网络状态
    const networkType = await checkNetworkStatus()
    if (!networkType || networkType === 'none') {
      console.log('网络不可用，跳过Token检查')
      return true
    }

    // 直接调用版本更新API来检测token（不使用任何缓存）
    console.log('直接调用版本更新API检测Token状态...')
    const response = await newAppVersionAndUrl()

    // 如果接口调用成功，说明Token有效
    console.log('Token检测成功，Token状态正常')
    updateTokenCheckStatus(true)

    // 解析API响应获取版本信息并更新缓存
    try {
      const versionInfo = parseVersionFromResponse(response)
      if (versionInfo) {
        console.log('Token检测成功，同时更新版本缓存:', versionInfo)
        cacheVersionInfo(versionInfo.version, versionInfo.downloadUrl)
      }
    } catch (parseError) {
      console.log('Token检测时解析版本信息失败，但不影响Token检测:', parseError.message)
    }

    // 如果需要显示版本检查结果，处理版本比较逻辑
    if (showNoUpdateToast) {
      const currentVersion = getApp().globalData.config.appInfo.version
      
      try {
        const versionInfo = parseVersionFromResponse(response)
        if (versionInfo) {
          const comparison = compareVersions(currentVersion, versionInfo.version)
          if (comparison < 0) {
            // 有新版本可用，但需要检查是否已有安装提醒
            const updatePending = uni.getStorageSync('update_pending');
            if (updatePending && (updatePending.status === 'downloaded' || updatePending.status === 'ready_to_install')) {
              console.log('Token检测时发现有待安装的更新包，跳过显示新版本弹窗');
              uni.showToast({
                title: '有安装包待安装，请先完成安装',
                icon: 'none'
              });
            } else {
              // 没有待安装的更新包，显示新版本弹窗
              showUpdateDialog(versionInfo.version, versionInfo.downloadUrl)
            }
          } else {
            // 已是最新版本
            uni.showToast({
              title: '当前已是最新版本',
              icon: 'none'
            })
          }
        }
      } catch (parseError) {
        console.log('显示版本检查结果时解析失败:', parseError.message)
        uni.showToast({
          title: '版本信息解析失败',
          icon: 'none'
        })
      }
    }

    return true
  } catch (error) {
    console.error('Token检测失败:', error)

    // 检查是否是401错误（Token过期）
    const is401Error = isTokenExpiredError(error)

    if (is401Error) {
      console.log('检测到Token已过期')
      updateTokenCheckStatus(false)

      // 注意：这里不再调用handleTokenExpired()
      // 因为网络请求拦截器(utils/request.js)已经会处理401错误
      // 避免重复弹窗，让request.js统一处理token过期
      console.log('Token已过期，由网络请求拦截器统一处理，避免重复弹窗')

      return false
    }

    // 其他错误不认为是Token过期
    console.log('Token检测遇到其他错误，不认为是Token过期:', error.message || error)
    return true
  }
}

/**
 * 判断是否为Token过期错误
 * @param {Object} error 错误对象
 * @returns {boolean} 是否为Token过期错误
 */
function isTokenExpiredError(error) {
  return (
    (error.code === 401) ||
    (error.statusCode === 401) ||
    (error.message && error.message.includes('401')) ||
    (error.data && error.data.code === 401) ||
    (typeof error === 'object' && error.errMsg && error.errMsg.includes('401')) ||
    (error.data && error.data.msg && error.data.msg.includes('登录')) ||
    (error.data && error.data.msg && error.data.msg.includes('token')) ||
    (error.data && error.data.msg && error.data.msg.includes('认证'))
  )
}


/**
 * 更新Token检测状态到缓存（仅用于记录检测历史，不用于频率控制）
 * @param {boolean} isValid Token是否有效
 */
function updateTokenCheckStatus(isValid) {
  const status = {
    isValid,
    lastCheckTime: Date.now(),
    checkResult: isValid ? 'Token有效' : 'Token已过期'
  }

  try {
    uni.setStorageSync(TOKEN_CHECK_KEY, status)
    console.log('Token检测状态已记录:', status)
  } catch (error) {
    console.error('记录Token检测状态失败:', error)
  }
}

/**
 * 获取Token检测信息（用于调试）
 * @returns {Object} Token检测信息
 */
export function getTokenCheckInfo() {
  try {
    const status = uni.getStorageSync(TOKEN_CHECK_KEY)
    return {
      strategy: '每次都直接请求接口，不使用缓存和频率限制，同时更新版本缓存',
      lastStatus: status,
      lastCheckTime: status ? new Date(status.lastCheckTime).toLocaleString() : '未检测',
      checkResult: status ? status.checkResult : '未检测',
      versionCache: getCachedVersionInfo()
    }
  } catch (error) {
    return {
      error: error.message,
      strategy: '每次都直接请求接口，不使用缓存和频率限制，同时更新版本缓存'
    }
  }
}
