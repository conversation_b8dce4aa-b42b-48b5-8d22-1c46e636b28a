<template>
	<view class="video-page">
		<!-- 顶部搜索栏 -->
		<view class="search-bar">
			<view class="search-input-wrapper" @click="navigateToSearch">
				<text class="cuIcon-search search-icon"></text>
				<input class="search-input" type="text" placeholder="舌诊" />
			</view>

		</view>

		<!-- 页面内容（支持下拉刷新） -->
		<scroll-view
			class="content-scroll"
			scroll-y
			refresher-enabled
			:refresher-triggered="refreshing"
			@refresherrefresh="handleRefresh"
		>
		<view class="main-content">
				<view class="banner-section">
					<swiper class="screen-swiper round-dot" :indicator-dots="true" :circular="true" :autoplay="true" interval="5000" duration="500">
						<swiper-item v-for="(item, index) in swiperList" :key="index">
							<image :src="item.url" mode="aspectFill" class="banner-image"></image>
							<view class="banner-overlay" v-if="item.id === 0">
								<text class="banner-tip">♦ 千年古法养生长寿操 ♦</text>
								<text class="banner-title">五禽戏</text>
								<view class="banner-tags">
									<text class="cu-tag sm round bg-green light">活脊柔筋</text>
									<text class="cu-tag sm round bg-green light">血脉流通</text>
								</view>
								<view class="banner-tags">
									<text class="cu-tag sm round bg-orange light">安神调心</text>
									<text class="cu-tag sm round bg-orange light">延年益寿</text>
								</view>
							</view>
						</swiper-item>
					</swiper>
				</view>

				<!-- 重磅上新 -->
				<view class="new-release-section">
					<view class="section-header">
						<text class="cu-tag round bg-red light margin-right-sm">重磅上新</text>
						<swiper class="new-release-swiper" :vertical="true" :autoplay="true" :circular="true" :interval="3000" :duration="500" :display-multiple-items="1" :disable-touch="true">
							<swiper-item v-for="(item, index) in newReleases" :key="index">
								<text class="section-title">{{ truncateTitle(item) }}</text>
							</swiper-item>
						</swiper>
						<text class="more-link" @click="newMore">更多 ></text>
					</view>
				</view>

				<!-- 本周热门课程 -->
				<view class="hot-courses-section">
					<view class="section-header">
						<text class="section-title">本周热门视频</text>
						<!-- <text class="cu-tag round bg-gray light margin-right-xs">TOP 10</text> -->
						<text class="more-link" @click="navigateToCourses">更多 ></text>
					</view>

					<!-- 加载状态 -->
					<view v-if="isLoading" class="loading-state">
						<text class="loading-text">加载中...</text>
					</view>

					<!-- 课程列表 -->
					<view v-else class="course-list grid col-2">
						<view class="course-card" v-for="course in hotCourses" :key="course.id" @click="goToVideoDetail(course.id)">
							<view class="image-wrapper">
								<image :src="course.image" mode="heightFix" class="course-image" @error="handleImageError"></image>
								<text class="cu-tag top-badge bg-orange">{{course.top}}</text>
								<!-- <text class="cu-tag free-tag bg-green light" v-if="course.free">免费</text> -->
							</view>
							<view class="course-info">
								<text class="course-title">{{course.title}}</text>
								<view class="course-meta">
									<text class="course-author">{{course.author}}</text>
									<view class="course-stats">
										<!-- <text class="course-views">{{course.views}}</text> -->
										<text class="course-date" v-if="course.time">{{course.time}}</text>
									</view>
								</view>
							</view>
						</view>
					</view>

					<!-- 空状态 -->
					<view v-if="!isLoading && hotCourses.length === 0" class="empty-state">
						<text class="empty-text">暂无视频数据</text>
					</view>
				</view>



		</view>
		</scroll-view>

	</view>
</template>

<script>
	import { videoList, videoViews } from '@/api/system/video.js'

	export default {
		data() {
			return {
				searchKeyword: '',
				TabCur: 0,
				isLoading: false,
				refreshing: false, // 下拉刷新状态
				swiperList: [{
					id: 0,
					url: 'http://www.aigather.katlot.cn/sourcePic/2eee29a981771349239328453918f38.jpg'
				}, {
					id: 1,
					url: 'http://www.aigather.katlot.cn/sourcePic/bd1ff779db3b03cc25225060162ac31.jpg'
				}],
				hotCourses: [],
				newReleases: []
			}
		},
		onLoad() {
			this.loadVideoData()
		},
		onShow() {
			// 页面显示时加载数据
			this.loadVideoData()
		},
		methods: {
			// 加载视频数据
			async loadVideoData() {
				try {
					this.isLoading = true
					uni.showLoading({ title: '加载中...' })

					const response = await videoList()

					if (response && response.rows) {
						this.parseVideoData(response.rows)
					} else {
						console.warn('API返回数据格式异常:', response)
						this.hotCourses = []
						uni.showToast({
							title: '加载失败',
							icon: 'none'
						})
					}
				} catch (error) {
					console.error('加载视频数据失败:', error)
					uni.showToast({
						title: '网络错误',
						icon: 'none',
						duration: 2000
					})
					this.hotCourses = []
				} finally {
					this.isLoading = false
					uni.hideLoading()
				}
			},

			// 解析API返回的视频数据
			parseVideoData(data) {
				// 处理热门课程数据 - 取前4个作为热门视频
				if (Array.isArray(data)) {
					// 按观看次数排序
					const sortedData = [...data].sort((a, b) => {
						const viewsA = this.parseViewsToNumber(a.views || 0)
						const viewsB = this.parseViewsToNumber(b.views || 0)
						return viewsB - viewsA
					})

					// 热门课程数据（前4个，已排序）
					this.hotCourses = sortedData.slice(0, 4).map((item, index) => ({
						id: item.id,
						image: item.image,
						top: `${index + 1} TOP`,
						free: item.free,
						title: (item.title || '').trim(),
						author: item.author,
						views: this.formatViews(item.views),
						time: item.time,
						duration: item.duration,
						vedio: item.vedio,
						text: item.text,
						originalData: item
					}))

					// 新发布数据 - 提取所有数据的title字段用于轮播
					this.newReleases = sortedData.map(item => {
						return item.title || item.name || '未知课程'
					}).filter(title => title && title !== '未知课程')
				} else {
					this.hotCourses = []
					this.newReleases = []
				}
			},

			// 解析观看次数为数字
			parseViewsToNumber(views) {
				if (typeof views === 'number') {
					return views
				}

				if (typeof views === 'string') {
					const numStr = views.replace(/[^\d]/g, '')
					return parseInt(numStr) || 0
				}

				return 0
			},

			// 格式化观看次数显示
			formatViews(views) {
				const num = this.parseViewsToNumber(views)

				if (num >= 100000000) {
					return (num / 100000000).toFixed(1) + '亿次观看'
				} else if (num >= 10000) {
					return (num / 10000).toFixed(1) + '万次观看'
				} else if (num >= 1000) {
					return (num / 1000).toFixed(1) + 'k次观看'
				} else {
					return num + '次观看'
				}
			},

			handleSearch() {
				// 搜索功能待实现
			},
			handleScan() {
				// 扫描功能待实现
			},
			newMore(){
				uni.navigateTo({
					url:'/pages/gather/new/new'
				})
			},
			navigateToCourses() {
				uni.navigateTo({
					url: '/pages/gather/courses/courses'
				})
			},
			navigateToSearch() {
				// 搜索页面现在从缓存加载数据，不需要传递数据
				uni.navigateTo({
					url: '/pages/gather/search/search'
				})
			},
			truncateTitle(title, maxLength = 14) {
				if (title.length > maxLength) {
					return title.substring(0, maxLength) + '...';
				} else {
					return title;
				}
			},
		
			// 跳转到视频详情页面，传递完整数据
			async goToVideoDetail(videoId) {
				// 查找对应的课程数据
				const courseData = this.hotCourses.find(course => course.id === videoId)
				if (!courseData) {
					uni.showToast({
						title: '课程数据不存在',
						icon: 'none'
					})
					return
				}

				// 从courseData中获取原始的观看次数（数字格式）
				const originalViews = courseData.originalData ? courseData.originalData.views : 0

				// 调用观看次数自增接口
				await this.incrementVideoViews(videoId, originalViews)

				// 将完整的课程数据编码后传递
				const encodedData = encodeURIComponent(JSON.stringify(courseData))

				uni.navigateTo({
					url: `/pages/gather/video_a/detail?videoId=${videoId}&courseData=${encodedData}`,
					fail: function(err) {
						uni.showToast({
							title: '页面跳转失败',
							icon: 'none'
						});
					}
				});
			},

			// 视频观看次数自增
			async incrementVideoViews(videoId, currentViews) {
				try {
					const numericViews = this.parseViewsToNumber(currentViews);
					const newViews = numericViews + 1;

					// 调用API更新观看次数
					const updateData = {
						id: videoId,
						views: newViews
					};

					await videoViews(updateData);

					// 更新本地热门课程数据
					const courseIndex = this.hotCourses.findIndex(course => course.id === videoId);
					if (courseIndex !== -1) {
						this.hotCourses[courseIndex].views = this.formatViews(newViews);
						// 同时更新原始数据
						if (this.hotCourses[courseIndex].originalData) {
							this.hotCourses[courseIndex].originalData.views = newViews;
						}
					}

				} catch (error) {
					console.error('视频观看次数自增失败:', error);
				}
			},

			// 处理图片加载错误
			handleImageError(e) {
				console.log('图片加载失败:', e)
				
			},

			// 下拉刷新处理
			handleRefresh() {
				this.refreshing = true

				this.loadVideoData().then(() => {
					uni.showToast({
						title: '刷新成功',
						icon: 'success',
						duration: 1500
					})
				}).catch((error) => {
					console.error('下拉刷新失败:', error)
					uni.showToast({
						title: '刷新失败',
						icon: 'none'
					})
				}).finally(() => {
					this.refreshing = false
				})
			}
		},

		// 页面生命周期
		onHide() {
			// 页面隐藏时的处理
		}
	}
</script>

<style lang="scss">
	page {
		background-color: #f8f8f8;
	}

	.video-page {
		background-color: #f8f8f8;
		height: 100vh;
		display: flex;
		flex-direction: column;
	}

	/* 滚动容器 */
	.content-scroll {
		flex: 1;
		height: 0; /* 配合flex使用 */
	}

	.search-bar {
		display: flex;
		align-items: center;
		padding: 8px 15px;
		background-color: #fff;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

		.search-input-wrapper {
			flex: 1;
			display: flex;
			align-items: center;
			background-color: #f0f0f0;
			border-radius: 20px;
			padding: 4px 10px;
			margin-right: 10px;

			.search-icon {
				font-size: 20px;
				color: #999;
				margin-right: 5px;
			}

			.search-input {
				flex: 1;
				height: 26px;
				font-size: 14px;
				color: #333;
			}
		}

		.scan-icon {
			font-size: 24px;
			color: #666;
			display: none; /* Hide the scan icon */
		}
	}

	.main-content {
		padding: 0px 15px 15px 15px; /* Adjust padding for content sections */
	}

	.banner-section {
		position: relative;
		width: 100%;
		height: 370rpx; /* Fixed height for the banner */
		border-radius: 10px;
		overflow: hidden;
		margin-bottom: 20px;
		background-color: #f0f0f0; /* Placeholder background */

		.banner-image {
			width: 100%;
			height: 100%;
			object-fit: cover;
		}

		.banner-overlay {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: flex-start;
			padding: 20px;
			color: white;
			text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
			background: linear-gradient(to right, rgba(0,0,0,0.4) 0%, rgba(0,0,0,0) 100%); /* Gradient overlay */
		}

		.banner-tip {
			font-size: 14px;
			margin-bottom: 5px;
		}

		.banner-title {
			font-size: 40px;
			font-weight: bold;
			margin-bottom: 10px;
			color: #c2e9fb; /* Light blue color from the image */
		}

		.banner-tags {
			display: flex;
			margin-bottom: 5px;

			.cu-tag {
				margin-right: 5px;
				padding: 2px 8px;
				font-size: 12px;
				opacity: 0.9;
			}
		}
	}

	.test-section {
		background-color: #fff;
		border-radius: 10px;
		padding: 15px;
		margin-bottom: 20px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
	}

	.test-button {
		width: 100%;
		height: 40px;
		background: linear-gradient(90deg, #ff6b6b 0%, #ee5a52 100%);
		color: #fff;
		border: none;
		border-radius: 20px;
		font-size: 16px;
		font-weight: bold;
	}

	.new-release-section {
		background-color: #fff;
		border-radius: 10px;
		padding: 15px;
		margin-bottom: 20px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

		.section-header {
			display: flex;
			align-items: center;
			justify-content: space-between;

			.cu-tag {
				font-size: 13px;
				padding: 2px 5px;
			}

			.new-release-swiper {
				flex: 1;
				height: 22px; /* Height for one line of text, adjust as needed */
				margin-left: 3px;
				overflow: hidden;
			}

			.section-title {
				font-size: 15px;
				font-weight: bold;
				color: #333;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
				line-height: 22px; /* Ensure text is centered vertically in swiper-item */
			}

			.more-link {
				font-size: 13px;
				color: #666;
			}
		}
	}

	.hot-courses-section {
		background-color: #fff;
		border-radius: 10px;
		padding: 15px;
		margin-bottom: 20px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

		.section-header {
			display: flex;
			align-items: center;
			justify-content: flex-start; /* Changed to flex-start */
			margin-bottom: 15px;

			.section-title {
				font-size: 16px;
				font-weight: bold;
				color: #333;
				/* margin-right: 10px; */ /* Removed as TOP50 moved */
			}

			.cu-tag {
				font-size: 11px;
				padding: 2px 6px;
				background-color: #f0f0f0;
				color: #999;
				border-radius: 10px;
				margin-right: 10px; /* Adjusted to 10px for spacing after TOP50 */
			}

			.more-link {
				font-size: 13px;
				color: #666;
				margin-left: auto; /* Push to the right */
			}
		}

		.course-list {
			display: flex;
			flex-wrap: wrap;
			justify-content: space-between;

			.course-card {
				width: 48%; /* Adjust width for two columns with spacing */
				margin-bottom: 20px;
				background-color: #fff;
				border-radius: 10px;
				overflow: hidden;
				box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

				.image-wrapper {
					position: relative;
					width: 100%;
					height: 90px; /* Fixed height for course image */
					overflow: hidden;
					border-top-left-radius: 10px;
					border-top-right-radius: 10px;

					.course-image {
						width: 100%;
						height: 100%;
						object-fit: cover;
					}

					.top-badge {
						position: absolute;
						top: 5px;
						left: 5px;
						font-size: 10px;
						padding: 2px 5px;
						border-radius: 5px;
						color: #fff;
						background-color: rgba(255, 165, 0, 0.8); /* Orange with opacity */
					}

					.free-tag {
						position: absolute;
						top: 5px;
						right: 5px;
						font-size: 10px;
						padding: 2px 5px;
						border-radius: 5px;
						background-color: rgba(0, 128, 0, 0.8); /* Green with opacity */
						color: #fff;
					}
				}

				.course-info {
					padding: 10px;
					display: flex;
					flex-direction: column;

					.course-title {
						font-size: 14px;
						font-weight: bold;
						color: #333;
						margin-bottom: 5px;
						line-height: 1.3; /* Adjust line height for multiline text */
						display: -webkit-box;
						-webkit-line-clamp: 2; /* Limit to 2 lines */
						-webkit-box-orient: vertical;
						overflow: hidden;
						text-overflow: ellipsis;
					}

					.course-meta {
						display: flex;
						justify-content: space-between;
						align-items: center;
						font-size: 11px;
						color: #999;

						.course-author {
							flex: 1;
							white-space: nowrap;
							overflow: hidden;
							text-overflow: ellipsis;
							margin-right: 5px;
						}

						.course-stats {
							display: flex;
							flex-direction: column;
							align-items: flex-end;
							gap: 2px;
						}

						.course-views {
							background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
							color: white;
							padding: 2px 8px;
							border-radius: 10px;
							font-size: 10px;
							font-weight: 500;
							white-space: nowrap;
						}

						.course-date {
							white-space: nowrap;
							font-size: 10px;
						}
					}
				}
			}
		}

		/* 加载状态 */
		.loading-state {
			text-align: center;
			padding: 40rpx;
			color: #999;
		}

		.loading-text {
			font-size: 28rpx;
		}

		/* 空状态 */
		.empty-state {
			text-align: center;
			padding: 60rpx 40rpx;
			color: #999;
		}

		.empty-text {
			font-size: 28rpx;
		}
	}
</style>
