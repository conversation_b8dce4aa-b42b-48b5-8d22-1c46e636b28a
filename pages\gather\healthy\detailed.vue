<template>
  <view class="detailed-container">
    <!-- 头部进度区域 -->
    <view class="header-section">
      <view class="progress-info">
        <text class="progress-text">{{ currentQuestionIndex + 1 }}/{{ questions.length }}</text>
        <view class="progress-bar">
          <view class="progress-fill" :style="{ width: progressPercent + '%' }"></view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-section">
      <view class="loading-content">
        <text class="loading-text">正在从服务器加载问卷数据...</text>
      </view>
    </view>

    <!-- 问题区域 -->
    <view v-else-if="questions.length > 0" class="question-section">
      <view class="question-card">
        <text class="question-text">{{ currentQuestion.text }}</text>

        <!-- 主要选项 -->
        <view class="options-list">
          <view
            class="option-item"
            v-for="(option, index) in currentQuestion.options"
            :key="index"
            :class="{ 'selected': selectedAnswers[currentQuestionIndex] === index }"
            @click="selectOption(index)"
          >
            <view class="option-radio">
              <view class="radio-inner" v-if="selectedAnswers[currentQuestionIndex] === index"></view>
            </view>
            <text class="option-text">{{ option.text }}</text>
          </view>
        </view>

        <!-- 后续问题（症状持续时间） -->
        <view class="followup-section" v-if="selectedAnswers[currentQuestionIndex] !== undefined && selectedAnswers[currentQuestionIndex] !== 0 && currentQuestion.followups && currentQuestion.followups[0]">
          <text class="followup-title">{{ currentQuestion.followups[0].text }}</text>
          <view class="followup-options">
            <view
              class="followup-option"
              v-for="(option, index) in currentQuestion.followups[0].options"
              :key="index"
              :class="{ 'selected': selectedFollowups[currentQuestionIndex] === index }"
              @click="selectFollowup(index)"
            >
              <view class="followup-radio">
                <view class="radio-inner" v-if="selectedFollowups[currentQuestionIndex] === index"></view>
              </view>
              <text class="followup-text">{{ option.text }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部按钮区域 -->
    <view class="bottom-section">
      <button
        class="prev-btn"
        :disabled="currentQuestionIndex === 0"
        @click="prevQuestion"
        v-if="currentQuestionIndex > 0"
      >
        上一题
      </button>
      <button
        class="next-btn"
        @click="nextQuestion"
        :class="{ 'btn-disabled': !canProceed }"
      >
        {{ isLastQuestion ? '查看结果' : '下一题' }}
      </button>
    </view>

  </view>
</template>

<script>
import { constitutionDetection, constitutionDetectionJson, selectRecordsApplastone } from '@/api/system/constitution.js'
import { submitComprehensiveDetection } from '@/api/system/comprehensive'
import detectionManager from '@/utils/detectionManager'

export default {
  data() {
    return {
      questions: [], // 从服务器加载问卷数据
      currentQuestionIndex: 0,
      selectedAnswers: {}, // 主要答案 {questionIndex: optionIndex}
      selectedFollowups: {}, // 后续答案 {questionIndex: optionIndex}
      // 综合检测相关状态
      fromTongue: false, // 是否来自舌诊页面
      isComprehensive: false, // 是否为综合检测模式
      skipUserSelection: false, // 是否跳过用户选择弹窗
      loading: true, // 问卷数据加载状态
      // 历史记录相关状态
      hasHistoryRecord: false, // 是否有历史记录
      historyRecord: null, // 历史记录数据
      isLoadingFromHistory: false // 是否正在从历史记录加载数据
    }
  },

  async onLoad(options) {
    // 检查是否来自舌诊页面
    if (options.from === 'tongue' && options.comprehensive === 'true') {
      this.fromTongue = true
      this.isComprehensive = true
      this.skipUserSelection = true
      console.log('从舌诊页面跳转而来，开启综合检测模式')
    }

    // 加载问卷数据
    await this.loadQuestionnaire()

    // 检查历史记录（只有在非综合检测模式下才检查）
    if (!this.isComprehensive) {
      await this.checkHistoryRecords()
    }

    // 检查用户信息是否完善
    this.checkUserInfo()

    // 如果是综合检测模式，检查是否有舌诊数据
    if (this.isComprehensive) {
      this.checkTongueData()
    }
  },

  // 处理返回按钮点击
  onBackPress() {
    console.log('用户点击返回按钮，准备清除数据')

    // 无论是单独的体质检测还是综合检测，都清除所有相关数据
    this.clearAllData()

    // 返回上一页
    return false // 让系统继续执行默认的返回操作
  },

  computed: {
    currentQuestion() {
      return this.questions[this.currentQuestionIndex] || {}
    },

    progressPercent() {
      return ((this.currentQuestionIndex + 1) / this.questions.length) * 100
    },

    isLastQuestion() {
      // 如果当前是第10题（索引9）且前10题都选择了平和体质，则第10题就是最后一题
      if (this.currentQuestionIndex === 9) {
        // 检查前10题是否都选择了平和体质（选项0）
        const allPinghe = this.checkIfAllPinghe()
        if (allPinghe) {
          console.log('第10题是最后一题（前10题都是平和体质）')
          return true
        }
      }

      // 如果当前是第11题（索引10），则是最后一题
      if (this.currentQuestionIndex === 10) {
        return true
      }

      // 否则按正常逻辑判断
      return this.currentQuestionIndex === this.questions.length - 1
    },

    canProceed() {
      const hasMainAnswer = this.selectedAnswers[this.currentQuestionIndex] !== undefined

      // 如果选择了A选项（平和体质），不需要后续问题
      if (this.selectedAnswers[this.currentQuestionIndex] === 0) {
        return hasMainAnswer
      }

      // 如果当前问题没有followups（如第11题），只需要主答案
      if (!this.currentQuestion.followups || !this.currentQuestion.followups[0]) {
        return hasMainAnswer
      }

      // 如果选择了其他选项且有后续问题，需要回答后续问题
      const hasFollowupAnswer = this.selectedFollowups[this.currentQuestionIndex] !== undefined
      return hasMainAnswer && hasFollowupAnswer
    }
  },

  methods: {
    // 加载服务器问卷数据
    async loadQuestionnaire() {
      try {
        this.loading = true
        console.log('开始从服务器加载体质检测问卷数据...')

        // 调用API获取服务器上的JSON文件
        const response = await constitutionDetectionJson()
        console.log('服务器问卷数据响应:', response)

        // 处理响应数据
        console.log('响应数据类型:', typeof response)
        console.log('响应数据结构:', response)

        if (response && response.response && Array.isArray(response.response)) {
          // API返回格式: {response: Array, questionversion: '1.0.0'}
          this.questions = response.response
          console.log('使用 response.response 格式，版本:', response.questionversion)
        } else {
          console.error('无法识别的数据格式:', response)
          throw new Error('服务器返回的问卷数据格式不正确')
        }

        // 添加第11题
        this.addQuestion11()

       

      } catch (error) {
        console.error('从服务器加载问卷数据失败:', error)

        // 显示错误提示
        uni.showToast({
          title: '加载问卷失败，请检查网络',
          icon: 'none',
          duration: 3000
        })

        // 加载失败后返回上一页
        setTimeout(() => {
          // 清除所有数据
          this.clearAllData()
          uni.navigateBack()
        }, 2000)

      } finally {
        this.loading = false
        console.log('=== 数据加载完成 ===')
        console.log('loading状态已设置为:', this.loading)
      }
    },

    // 添加第11题
    addQuestion11() {
      const question11 = {
        "text": "11.上述异常项中哪项最为明显？",
        "options": [
          {"text": "1. 精神状态"},
          {"text": "2. 体力状态"},
          {"text": "3. 寒热适应"},
          {"text": "4. 出汗情况"},
          {"text": "5. 情绪状态"},
          {"text": "6. 饮食消化"},
          {"text": "7. 睡眠障碍"},
          {"text": "8. 过敏反应"},
          {"text": "9. 大便情况"},
          {"text": "10. 小便情况"}
        ]
      }

      // 将第11题添加到问卷数组中
      this.questions.push(question11)
      console.log('已添加第11题到问卷中')
    },

    // 检查历史记录
    async checkHistoryRecords() {
      try {
        console.log('开始检查体质检测历史记录...')

        // 调用接口获取最新的体质检测记录
        const response = await selectRecordsApplastone()
        console.log('历史记录API响应:', response)

        // 检查是否有有效的历史记录
        // 根据实际API响应结构：{msg: "操作成功", code: 200, data: {...}}
        if (response && response.code === 200 && response.data) {
          const latestRecord = response.data
          console.log('发现历史记录:', latestRecord)

          // 检查历史记录是否包含有效的答案数据
          const hasValidAnswers = latestRecord.answers &&
                                 latestRecord.answers !== '{}' &&
                                 latestRecord.answers.trim() !== ''

          if (hasValidAnswers) {
            console.log('历史记录包含有效的答案数据')
            this.hasHistoryRecord = true
            this.historyRecord = latestRecord

            // 显示历史记录确认弹窗
            await this.showHistoryConfirmDialog(latestRecord)
          } else {
            console.log('历史记录存在但无有效答案数据，继续正常流程')
            this.hasHistoryRecord = false
            this.proceedWithNormalFlow()
          }
        } else {
          console.log('未发现历史记录或API调用失败，继续正常流程')
          console.log('API响应:', response)
          this.hasHistoryRecord = false
          this.proceedWithNormalFlow()
        }
      } catch (error) {
        console.error('检查历史记录失败:', error)
        // 出错时继续正常流程
        this.hasHistoryRecord = false
        this.proceedWithNormalFlow()
      }
    },

    // 显示历史记录确认弹窗
    showHistoryConfirmDialog(historyRecord) {
      return new Promise((resolve) => {
        // 获取历史记录的基本信息用于显示
        const consultation = historyRecord.consultation || '体质检测'
        const createTime = historyRecord.createtime || historyRecord.creatime || ''

        let contentText = '检测到您之前已完成过体质检测'
        if (consultation && consultation !== 'null') {
          contentText += `，结果为：${consultation}`
        }
        if (createTime) {
          const timeStr = createTime.split('T')[0] // 只显示日期部分
          contentText += `（${timeStr}）`
        }
        contentText += '。是否需要修改原来的检测数据？'

        uni.showModal({
          title: '发现历史记录',
          content: contentText,
          confirmText: '修改数据',
          cancelText: '直接出报告',
          success: (res) => {
            if (res.confirm) {
              // 用户选择修改数据
              console.log('用户选择修改历史数据')
              this.loadHistoryData(historyRecord)
            } else {
              // 用户选择直接出报告
              console.log('用户选择直接出报告')
              this.generateReportFromHistory(historyRecord)
            }
            resolve(res)
          },
          fail: () => {
            // 弹窗失败时继续正常流程
            console.log('历史记录弹窗显示失败，继续正常流程')
            this.proceedWithNormalFlow()
            resolve({ confirm: false, cancel: true })
          }
        })
      })
    },

    // 从历史记录加载数据到当前页面
    loadHistoryData(historyRecord) {
      try {
        console.log('开始加载历史数据到当前页面:', historyRecord)
        this.isLoadingFromHistory = true

        // 解析历史答案数据
        const historyAnswers = this.parseHistoryAnswers(historyRecord)
        console.log('解析后的历史答案:', historyAnswers)

        // 检查是否成功解析到答案数据
        const hasMainAnswers = historyAnswers.main && Object.keys(historyAnswers.main).length > 0
        const hasFollowupAnswers = historyAnswers.followup && Object.keys(historyAnswers.followup).length > 0

        if (!hasMainAnswers && !hasFollowupAnswers) {
          console.warn('未能解析到有效的历史答案数据')
          uni.showModal({
            title: '数据加载提示',
            content: '历史记录中的答案数据格式不兼容，建议重新开始检测以获得最佳体验。',
            confirmText: '重新开始',
            cancelText: '继续',
            success: (res) => {
              if (res.confirm) {
                // 用户选择重新开始
                this.proceedWithNormalFlow()
              } else {
                // 用户选择继续，但数据为空
                this.selectedAnswers = {}
                this.selectedFollowups = {}
                this.currentQuestionIndex = 0
                this.$forceUpdate()
              }
            }
          })
          return
        }

        // 验证答案数据的有效性
        if (hasMainAnswers) {
          const questionCount = this.questions ? this.questions.length : 11 // 默认11题
          const answerKeys = Object.keys(historyAnswers.main)
          const validAnswers = answerKeys.filter(key => {
            const questionIndex = parseInt(key)
            const answerValue = historyAnswers.main[key]
            return questionIndex >= 0 && questionIndex < questionCount &&
                   typeof answerValue === 'number' && answerValue >= 0
          })

          console.log(`历史答案验证: 总共 ${answerKeys.length} 个答案，有效 ${validAnswers.length} 个`)

          if (validAnswers.length < answerKeys.length) {
            console.warn('发现部分无效的历史答案数据')
          }
        }

        // 回填到当前页面
        this.selectedAnswers = historyAnswers.main || {}
        this.selectedFollowups = historyAnswers.followup || {}

        // 设置当前题目为第一题（用户可以从头开始修改）
        this.currentQuestionIndex = 0

        // 更新页面显示
        this.$forceUpdate()

        console.log('历史数据加载完成')

        // 显示加载成功提示，包含数据统计
        const answerCount = Object.keys(this.selectedAnswers).length
        const followupCount = Object.keys(this.selectedFollowups).length

        let toastMessage = `已加载历史数据: ${answerCount}个答案`
        if (followupCount > 0) {
          toastMessage += `, ${followupCount}个附加答案`
        }

        uni.showToast({
          title: toastMessage,
          icon: 'success',
          duration: 2000
        })

        // 如果有答案数据，可以提示用户当前的进度
        if (answerCount > 0) {
          console.log('历史答案加载完成，用户可以从任意题目开始修改')
        }

      } catch (error) {
        console.error('加载历史数据失败:', error)
        uni.showModal({
          title: '加载失败',
          content: '加载历史数据时出现错误，是否重新开始检测？',
          confirmText: '重新开始',
          cancelText: '返回',
          success: (res) => {
            if (res.confirm) {
              this.proceedWithNormalFlow()
            } else {
              uni.navigateBack()
            }
          }
        })
      } finally {
        this.isLoadingFromHistory = false
      }
    },

    // 解析历史答案数据
    parseHistoryAnswers(historyRecord) {
      try {
        console.log('开始解析历史记录数据:', historyRecord)

        let mainAnswers = {}
        let followupAnswers = {}

        // 根据实际数据结构解析 answers 字段
        // 实际格式: "{\"0\":0,\"1\":0,\"2\":0,\"3\":0,\"4\":0,\"5\":0,\"6\":0,\"7\":0,\"8\":0,\"9\":0}"
        if (historyRecord.answers) {
          try {
            if (typeof historyRecord.answers === 'string' && historyRecord.answers.trim() !== '') {
              // 检查是否为空对象字符串
              if (historyRecord.answers === '{}') {
                console.log('answers 字段为空对象')
                mainAnswers = {}
              } else {
                mainAnswers = JSON.parse(historyRecord.answers)
                console.log('从 answers 字段解析到主要答案:', mainAnswers)

                // 验证解析结果的格式
                if (typeof mainAnswers === 'object' && mainAnswers !== null) {
                  console.log('answers 数据格式验证通过')
                } else {
                  console.warn('answers 解析结果格式异常:', typeof mainAnswers)
                  mainAnswers = {}
                }
              }
            } else if (typeof historyRecord.answers === 'object') {
              mainAnswers = historyRecord.answers
              console.log('从 answers 字段获取到主要答案:', mainAnswers)
            }
          } catch (parseError) {
            console.error('解析 answers 字段失败:', parseError)
            console.log('原始 answers 数据:', historyRecord.answers)
            console.log('数据类型:', typeof historyRecord.answers)
            mainAnswers = {}
          }
        }

        // 根据实际数据结构解析 followups 字段
        // 实际格式: "{}" 或包含附加题答案的JSON字符串
        if (historyRecord.followups) {
          try {
            if (typeof historyRecord.followups === 'string' && historyRecord.followups.trim() !== '') {
              // 检查是否为空对象字符串
              if (historyRecord.followups === '{}') {
                console.log('followups 字段为空对象')
                followupAnswers = {}
              } else {
                followupAnswers = JSON.parse(historyRecord.followups)
                console.log('从 followups 字段解析到附加答案:', followupAnswers)

                // 验证解析结果的格式
                if (typeof followupAnswers === 'object' && followupAnswers !== null) {
                  console.log('followups 数据格式验证通过')
                } else {
                  console.warn('followups 解析结果格式异常:', typeof followupAnswers)
                  followupAnswers = {}
                }
              }
            } else if (typeof historyRecord.followups === 'object') {
              followupAnswers = historyRecord.followups
              console.log('从 followups 字段获取到附加答案:', followupAnswers)
            }
          } catch (parseError) {
            console.error('解析 followups 字段失败:', parseError)
            console.log('原始 followups 数据:', historyRecord.followups)
            console.log('数据类型:', typeof historyRecord.followups)
            followupAnswers = {}
          }
        }

        // 验证解析结果
        const mainAnswerCount = Object.keys(mainAnswers).length
        const followupAnswerCount = Object.keys(followupAnswers).length

        console.log(`解析结果统计: 主要答案 ${mainAnswerCount} 个，附加答案 ${followupAnswerCount} 个`)

        // 验证主要答案数据的格式
        if (mainAnswerCount > 0) {
          console.log('主要答案数据详情:')
          Object.keys(mainAnswers).forEach(questionIndex => {
            const answerValue = mainAnswers[questionIndex]
            console.log(`  题目 ${questionIndex}: 选择了选项 ${answerValue}`)
          })

          // 验证答案值是否为有效的数字
          const invalidAnswers = Object.keys(mainAnswers).filter(key => {
            const value = mainAnswers[key]
            return typeof value !== 'number' || value < 0
          })

          if (invalidAnswers.length > 0) {
            console.warn('发现无效的答案数据:', invalidAnswers)
          } else {
            console.log('所有主要答案数据格式验证通过')
          }
        } else {
          console.log('没有主要答案数据')
        }

        // 验证附加答案数据的格式
        if (followupAnswerCount > 0) {
          console.log('附加答案数据详情:')
          Object.keys(followupAnswers).forEach(questionIndex => {
            const answerValue = followupAnswers[questionIndex]
            console.log(`  附加题 ${questionIndex}: 选择了选项 ${answerValue}`)
          })
        }

        // 如果没有解析到任何答案数据
        if (mainAnswerCount === 0 && followupAnswerCount === 0) {
          console.warn('未能解析到任何历史答案数据')

          // 检查是否有其他可用的数据字段
          if (historyRecord.consultation) {
            console.log('发现 consultation 字段:', historyRecord.consultation)
            console.log('但无法从中恢复原始答案选择')
          }
        }

        const result = {
          main: mainAnswers,
          followup: followupAnswers
        }

        console.log('最终解析结果:', result)
        return result

      } catch (error) {
        console.error('解析历史答案数据失败:', error)
        console.log('错误详情:', error.message)
        return {
          main: {},
          followup: {}
        }
      }
    },

    // 使用历史数据直接生成报告
    async generateReportFromHistory(historyRecord) {
      try {
        console.log('使用历史数据直接生成报告:', historyRecord)

        uni.showLoading({
          title: '正在生成报告...',
          mask: true
        })

        // 解析历史数据并构建API数据
        const historyAnswers = this.parseHistoryAnswers(historyRecord)

        // 临时设置答案数据用于构建API数据
        const originalAnswers = this.selectedAnswers
        const originalFollowups = this.selectedFollowups

        this.selectedAnswers = historyAnswers.main || {}
        this.selectedFollowups = historyAnswers.followup || {}

        // 构建API数据
        const apiData = this.buildApiData()
        console.log('基于历史数据构建的API数据:', apiData)

        // 调用后端API进行体质检测
        const response = await constitutionDetection(apiData)
        console.log('历史数据分析结果:', response)

        // 恢复原始答案数据
        this.selectedAnswers = originalAnswers
        this.selectedFollowups = originalFollowups

        uni.hideLoading()

        // 跳转到结果页面
        this.navigateToResult(response)

      } catch (error) {
        uni.hideLoading()
        console.error('使用历史数据生成报告失败:', error)

        uni.showModal({
          title: '生成报告失败',
          content: '使用历史数据生成报告时出现错误，是否重新开始检测？',
          confirmText: '重新检测',
          cancelText: '返回',
          success: (res) => {
            if (res.confirm) {
              // 重新开始检测
              this.proceedWithNormalFlow()
            } else {
              // 返回上一页
              uni.navigateBack()
            }
          }
        })
      }
    },

    // 继续正常流程（无历史记录或处理失败时）
    proceedWithNormalFlow() {
      console.log('继续正常的体质检测流程')
      this.hasHistoryRecord = false
      this.historyRecord = null
      // 这里不需要做特殊处理，页面会正常显示问卷
    },

    // 检查用户信息是否完善
    checkUserInfo() {
      try {
        const userInfo = uni.getStorageSync('userInfo') || {}

        // 检查必填字段（职业和婚姻状况改为选填）
        const requiredFields = [
          { key: 'name', label: '姓名' },
          { key: 'sex', label: '性别' },
          { key: 'dateBirth', label: '出生年月' },
          { key: 'phonenumber', label: '手机号' },
          { key: 'height', label: '身高' },
          { key: 'weight', label: '体重' }
        ]

        const missingFields = []
        requiredFields.forEach(field => {
          if (!userInfo[field.key] || userInfo[field.key].toString().trim() === '') {
            missingFields.push(field.label)
          }
        })

        if (missingFields.length > 0) {
          this.showUserInfoModal(missingFields)
          return false
        }

        return true
      } catch (error) {
        console.error('检查用户信息失败:', error)
        this.showUserInfoModal(['基本信息'])
        return false
      }
    },

    // 显示用户信息完善提示弹窗
    showUserInfoModal(missingFields) {
      const fieldText = missingFields.length > 3
        ? '基本信息'
        : missingFields.join('、')

      uni.showModal({
        title: '信息不完整',
        content: `为了更好的体质检测，请先完善${fieldText}等信息`,
        confirmText: '去完善',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 记录来源页面，跳转到信息填写页面
            uni.navigateTo({
              url: '/pages/gather/diagnosis/analyse?from=constitution'
            })
          } else {
            // 用户选择取消，返回上一页
            // 清除所有数据
            this.clearAllData()
            uni.navigateBack({
              delta: 1
            })
          }
        }
      })
    },

    // 选择主要选项
    selectOption(index) {
      this.$set(this.selectedAnswers, this.currentQuestionIndex, index)

      // 如果选择A选项，清除后续答案
      if (index === 0) {
        this.$delete(this.selectedFollowups, this.currentQuestionIndex)
      }
    },

    // 选择后续选项
    selectFollowup(index) {
      this.$set(this.selectedFollowups, this.currentQuestionIndex, index)
    },

    // 上一题
    prevQuestion() {
      if (this.currentQuestionIndex > 0) {
        this.currentQuestionIndex--
      }
    },

    // 下一题或查看结果
    nextQuestion() {
      if (!this.canProceed) {
        uni.showToast({
          title: '请先选择答案',
          icon: 'none'
        });
        return;
      }

      // 如果当前是第10题（索引9），需要判断是否显示第11题
      if (this.currentQuestionIndex === 9) {
        // 检查前10题是否都选择了A选项
        let allAnswersA = true
        for (let i = 0; i <= 9; i++) {
          if (this.selectedAnswers[i] !== 0) { // 0表示A选项
            allAnswersA = false
            break
          }
        }

        // 如果前10题都选择了A选项，直接计算结果，不显示第11题
        if (allAnswersA) {
          this.calculateResult()
          return
        }

        // 否则显示第11题
        this.currentQuestionIndex++
        return
      }

      if (this.isLastQuestion) {
        this.calculateResult()
      } else {
        this.currentQuestionIndex++
      }
    },



    // 计算结果
    async calculateResult() {
      try {
        // 检查用户信息是否完整
        const userInfo = uni.getStorageSync('userInfo') || {}
        if (!userInfo.name || !userInfo.sex || !userInfo.dateBirth) {
          uni.showModal({
            title: '提示',
            content: '为了更准确的体质检测，请先完善个人信息',
            confirmText: '去完善',
            success: (res) => {
              if (res.confirm) {
                uni.navigateTo({
                  url: '/pages/my/healthInput/analyse'
                })
              }
            }
          })
          return
        }

        // 保存当前体质检测数据
        this.saveCurrentConstitutionData()

        // 检查是否需要进行综合检测
        if (this.fromTongue) {
          // 来自舌诊页面，直接进行综合检测
          await this.submitComprehensiveDetection()
          return
        }

        // 检查是否有舌诊数据
        const hasTongueData = detectionManager.hasTongueData()

        if (hasTongueData) {
          // 有舌诊数据，询问是否进行综合检测
          const result = await detectionManager.showComprehensiveDialog('tongue')

          if (result.confirm) {
            // 用户选择进行综合检测
            await this.submitComprehensiveDetection()
            return
          }
        } else {
          // 没有舌诊数据，询问是否要做舌诊
          const result = await detectionManager.showComprehensiveDialog('tongue')

          if (result.confirm) {
            // 跳转到舌诊页面
            this.navigateToTongue()
            return
          }
        }

        // 继续单独体质检测

        // 显示加载提示
        uni.showLoading({
          title: '正在分析体质...',
          mask: true
        })

        // 构建传递给后端的数据
        const apiData = this.buildApiData()
        console.log('=== 准备发送给后端的数据 ===')
        console.log('API数据:', JSON.stringify(apiData, null, 2))
        console.log('用户选择的答案:', this.selectedAnswers)
        console.log('用户选择的附加题答案:', this.selectedFollowups)

        // 调用后端API进行体质检测
        console.log('=== 开始调用后端API ===')
        const response = await constitutionDetection(apiData)
        console.log('=== 后端API响应 ===')
        console.log('响应数据字段:', Object.keys(response || {}))

        // 隐藏加载提示
        uni.hideLoading()

        // 构建结果数据
        const resultData = {
          id: Date.now().toString(),
          constitution: response.primary || response.msg || '体质检测完成',
          testTime: this.getCurrentTime(),
          answers: JSON.stringify(this.selectedAnswers),
          followups: JSON.stringify(this.selectedFollowups),
          apiResponse: response, // 保存API返回的完整数据
          userInfo: { // 保存用户信息到结果中
            name: userInfo.name || '未知',
            sex: userInfo.sex || '未知',
            age: userInfo.age || 0,
            dateBirth: userInfo.dateBirth || '',
            height: userInfo.height || '',
            weight: userInfo.weight || ''
          },
          details: {
            totalQuestions: this.questions.length
          }
        }


        // 跳转到结果页面
        const dataStr = encodeURIComponent(JSON.stringify(resultData))
        uni.redirectTo({
          url: `/pages/gather/healthy/result?data=${dataStr}`
        })

      } catch (error) {
        // 隐藏加载提示
        uni.hideLoading()

        console.error('体质检测失败:', error)

        // 显示错误提示
        if(code!=401){
          const errorMsg = error?.message || error?.msg || '体质检测失败，请重试'
        uni.showToast({
          title: errorMsg,
          icon: 'none',
          duration: 3000
        })
        }
        
      }
    },

    // 构建传递给后端API的数据
    buildApiData() {
      // 获取用户信息
      const userInfo = uni.getStorageSync('userInfo') || {}

      // 初始化计数器
      const counts = {
        pinghe: 0,        // 平和质
        qixu_zhi: 0,      // 气虚质
        qixu_zheng: 0,    // 气虚证
        yangxu_zhi: 0,    // 阳虚质
        yangxu_zheng: 0,  // 阳虚证
        yinxu_zhi: 0,     // 阴虚质
        yinxu_zheng: 0,   // 阴虚证
        tanshi_zhi: 0,    // 痰湿质
        tanshi_zheng: 0,  // 痰湿证
        xueyu_zhi: 0,     // 血瘀质
        xueyu_zheng: 0,   // 血瘀证
        shire_zhi: 0,     // 湿热质
        shire_zheng: 0    // 湿热证
      }

      // 统计用户的选择
      this.calculateCounts(counts)

      // 计算总分验证
      const totalScore = Object.values(counts).reduce((sum, count) => sum + count, 0)
      console.log('=== 体质计分统计 ===')
      console.log('各体质计分:', counts)
      console.log('总分:', totalScore)
      console.log('答题总数:', Object.keys(this.selectedAnswers).length)

      // 构建包含用户信息和体质计分的完整数据
      const apiData = {
        // 体质计分数据（后端实体类字段）
        ...counts,

        // 用户答题数据（JSON字符串格式）
        answers: JSON.stringify(this.selectedAnswers),
        followups: JSON.stringify(this.selectedFollowups),

        // 用户身份信息（扁平化结构，便于后端接收）
        TimeStamp: userInfo.TimeStamp || new Date().getTime(),
        person_name: userInfo.name || '未知',
        sex: userInfo.sex || '未知',
        dateBirth: userInfo.dateBirth || '',
        phone: userInfo.phonenumber || '',
        occupation: userInfo.occupation || '',
        maritalStatus: userInfo.maritalStatus || '',
        height: userInfo.height || '',
        weight: userInfo.weight || '',
        age: userInfo.age || 0,
        diastolicPressure: userInfo.diastolicPressure || '',
        systolicPressure: userInfo.systolicPressure || '',
        allergy: userInfo.allergy || '',
        medicalHistory: userInfo.medicalHistory || '',
        UserseeionType: 3
      }

      console.log('=== 包含用户信息的API数据 ===')
      console.log('完整API数据:', JSON.stringify(apiData, null, 2))

      return apiData
    },

    // 统计用户选择的体质和证次数
    calculateCounts(counts) {
      // 第一步：处理前10题的答题
      for (let questionIndex = 0; questionIndex <= 9; questionIndex++) {
        this.processQuestion(questionIndex, counts, 1) // 基础分数为1
      }

      // 第二步：判断是否需要处理第11题
      // 如果前10题都选择了平和体质（选项0），则不处理第11题
      const allPinghe = this.checkIfAllPinghe()

      if (allPinghe) {
        console.log('前10题都选择了平和体质，跳过第11题处理')
      } else {
        console.log('前10题存在非平和体质选择，处理第11题')
        this.processQuestion11(counts)
      }
    },

    // 检查前10题是否都选择了平和体质（选项0）
    checkIfAllPinghe() {
      for (let questionIndex = 0; questionIndex <= 9; questionIndex++) {
        const answerIndex = this.selectedAnswers[questionIndex]

        // 如果有题目没有答案，或者答案不是0，则不是全部平和
        if (answerIndex === undefined || answerIndex !== 0) {
          console.log(`题目${questionIndex + 1}的答案不是平和体质: ${answerIndex}`)
          return false
        }
      }

      console.log('前10题都选择了平和体质（选项0）')
      return true
    },

    // 处理单个问题的计分
    processQuestion(questionIndex, counts, multiplier = 1) {
      const question = this.questions[questionIndex]
      const answerIndex = this.selectedAnswers[questionIndex]

      if (answerIndex === undefined || !question) return

      if (answerIndex === 0) {
        // 选择A选项：平和体质+multiplier
        counts.pinghe += multiplier
        console.log(`问题${questionIndex + 1}: 平和体质 +${multiplier}`)
      } else {
        // 选择其他选项：需要根据附加题判断
        const selectedOption = question.options[answerIndex]
        if (!selectedOption) return

        // 检查是否有后续问题
        if (!question.followups || !question.followups[0]) {
          // 没有附加题的情况，直接按体质计分
          this.addToCount(counts, selectedOption.type, 'zhi', multiplier)
        } else {
          // 有附加题的情况，根据附加题答案判断
          const followupIndex = this.selectedFollowups[questionIndex]

          if (followupIndex === 0) {
            // 附加题选择a：对应体质+multiplier
            this.addToCount(counts, selectedOption.type, 'zhi', multiplier)
          } else if (followupIndex !== undefined) {
            // 附加题选择其他：对应证+multiplier
            this.addToCount(counts, selectedOption.type, 'zheng', multiplier)
          }
        }
      }
    },

    // 处理第11题
    processQuestion11(counts) {
      const question11Index = 10 // 第11题的索引
      const answerIndex = this.selectedAnswers[question11Index]

      if (answerIndex === undefined || !this.questions[question11Index]) {
        return
      }

      // 第11题的答案对应题号指向的体质/证次数算两次
      const selectedQuestionNumber = answerIndex + 1 // 选择的题号（1-10）
      const targetQuestionIndex = selectedQuestionNumber - 1 // 对应的题目索引（0-9）

      console.log(`第11题选择: 问题${selectedQuestionNumber}，将对应题目分数×2`)

      // 对选中的题目进行双倍计分
      this.processQuestion(targetQuestionIndex, counts, 2)
    },

    // 根据类型添加计数
    addToCount(counts, type, suffix, multiplier = 1) {
      const typeMapping = {
        '气虚': 'qixu',
        '阳虚': 'yangxu',
        '阴虚': 'yinxu',
        '痰湿': 'tanshi',
        '湿热': 'shire',
        '血瘀': 'xueyu'
        // 注意：后端字段中没有气郁和特禀对应的字段，暂时忽略
      }

      const mappedType = typeMapping[type]
      if (mappedType) {
        const key = `${mappedType}_${suffix}`
        if (counts.hasOwnProperty(key)) {
          counts[key] += multiplier
          console.log(`${type} -> ${key}: +${multiplier}`)
        }
      } else {
        console.log(`未找到映射的体质类型: ${type}`)
      }
    },



    // 计算年龄
    calculateAge(birthDate) {
      if (!birthDate) return 0

      const birth = new Date(birthDate)
      const now = new Date()
      let age = now.getFullYear() - birth.getFullYear()
      const monthDiff = now.getMonth() - birth.getMonth()

      if (monthDiff < 0 || (monthDiff === 0 && now.getDate() < birth.getDate())) {
        age--
      }

      return age
    },

    // 获取当前时间
    getCurrentTime() {
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      const hours = String(now.getHours()).padStart(2, '0')
      const minutes = String(now.getMinutes()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}`
    },

    // 获取答题情况摘要
    getAnswerSummary() {
      const total = this.questions.length
      const answered = Object.keys(this.selectedAnswers).length
      return `${answered}/${total} 题已完成`
    },



    // 检查舌诊数据
    checkTongueData() {
      const tongueData = detectionManager.getTongueData()
      if (!tongueData) {
        console.warn('综合检测模式但未找到舌诊数据')
        this.isComprehensive = false
        this.fromTongue = false
      }
    },

    // 保存当前体质检测数据
    saveCurrentConstitutionData() {
      const userInfo = uni.getStorageSync('userInfo') || {}

      const constitutionData = {
        answers: JSON.stringify(this.selectedAnswers),
        followups: JSON.stringify(this.selectedFollowups),
        apiData: this.buildApiData(),
        userInfo: userInfo,
        timestamp: Date.now()
      }

      // 保存用户信息和体质检测数据
      detectionManager.saveUserInfo(userInfo)
      detectionManager.saveConstitutionData(constitutionData)

      console.log('已保存体质检测数据:', constitutionData)
    },

    // 跳转到舌诊页面
    navigateToTongue() {
      const url = detectionManager.generateNavigateUrl('tongue', 'constitution')
      uni.redirectTo({ url })
    },

    // 跳转到结果页面
    navigateToResult(response) {
      try {
        const userInfo = uni.getStorageSync('userInfo') || {}

        // 构建结果数据
        const resultData = {
          id: Date.now().toString(),
          constitution: response.primary || response.msg || '体质检测完成',
          testTime: this.getCurrentTime(),
          answers: JSON.stringify(this.selectedAnswers),
          followups: JSON.stringify(this.selectedFollowups),
          apiResponse: response, // 保存API返回的完整数据
          userInfo: { // 保存用户信息到结果中
            name: userInfo.name || '未知',
            sex: userInfo.sex || '未知',
            age: userInfo.age || 0,
            dateBirth: userInfo.dateBirth || '',
            height: userInfo.height || '',
            weight: userInfo.weight || ''
          },
          details: {
            totalQuestions: this.questions.length
          }
        }

        // 跳转到结果页面
        const dataStr = encodeURIComponent(JSON.stringify(resultData))
        uni.redirectTo({
          url: `/pages/gather/healthy/result?data=${dataStr}`
        })

      } catch (error) {
        console.error('跳转到结果页面失败:', error)
        uni.showToast({
          title: '跳转失败',
          icon: 'none'
        })
      }
    },

    // 提交综合检测数据
    async submitComprehensiveDetection() {
      try {
        uni.showLoading({
          title: '正在进行综合分析...',
          mask: true
        })

        // 获取综合检测数据
        const comprehensiveData = detectionManager.getComprehensiveData()

        if (!detectionManager.hasCompleteData()) {
          throw new Error('综合检测数据不完整')
        }

        // 构建发送给后端的数据
        const requestData = {
          userInfo: comprehensiveData.userInfo,
          tongueData: comprehensiveData.tongueData,
          constitutionData: comprehensiveData.constitutionData
        }

        console.log('发送综合检测数据:', requestData)

        // 调用综合检测接口
        const response = await submitComprehensiveDetection(requestData)

        uni.hideLoading()

        // 处理综合检测结果
        this.handleComprehensiveResult(response)

      } catch (error) {
        uni.hideLoading()
        console.error('综合检测失败:', error)

        uni.showModal({
          title: '综合检测失败',
          content: error.message || '未能检测到舌体',
          confirmText: '重新拍摄',
          cancelText: '返回',
          showCancel: true,
          success: (res) => {
            if (res.confirm) {
              // 重新拍摄：只清除舌诊数据，跳转到AI舌诊页面
              this.handleRetakePhoto()
            } else {
              // 返回：清除数据并回到首页
              this.handleReturn()
            }
          }
        })
      }
    },

    // 处理综合检测结果
    handleComprehensiveResult(response) {
      console.log('综合检测结果:', response)

      // 获取综合检测数据（包含舌诊图片信息）
      const comprehensiveData = detectionManager.getComprehensiveData()

      // 构建结果数据
      const resultData = {
        type: 'comprehensive',
        data: response,
        userInfo: comprehensiveData.userInfo, // 用户信息
        tongueData: comprehensiveData.tongueData, // 舌诊数据
        constitutionData: comprehensiveData.constitutionData, // 体质检测数据
        tongueImageUrl: comprehensiveData.tongueData?.photoPath || '', // 直接传递舌诊图片URL
        timestamp: Date.now()
      }

      // 清除临时数据
      detectionManager.clearData('all')

      // 跳转到综合结果页面
      uni.redirectTo({
        url: '/pages/gather/comprehensive/result?result=' + encodeURIComponent(JSON.stringify(resultData))
      })
    },

    // 处理重新拍摄操作
    handleRetakePhoto() {
      console.log('用户选择重新拍摄，只清除舌诊数据，保留体质检测结果')

      // 只清除舌诊相关数据，保留体质检测结果
      this.clearTongueDataOnly()

      // 跳转到AI舌诊页面，携带综合检测标识
      uni.redirectTo({
        url: '/pages/gather/diagnosis/diagnosis?from=constitution&comprehensive=true'
      })
    },

    // 处理返回操作
    handleReturn() {
      console.log('用户选择返回，清除数据并回到首页')

      // 清除所有数据
      this.clearAllData()

      // 返回首页
      uni.switchTab({
        url: '/pages/index'
      })
    },

    // 清除检测相关数据
    clearDetectionData() {
      // 清除答题数据
      this.selectedAnswers = {}
      this.selectedFollowups = {}
      this.currentQuestionIndex = 0

      // 清除检测管理器中的数据
      detectionManager.clearData('all')

      // 清除本地存储中的相关数据
      uni.removeStorageSync('tongueAnalysisResult')
      uni.removeStorageSync('capturedImage')
      uni.removeStorageSync('constitutionResult')

      console.log('检测数据已清除')
    },

    // 只清除舌诊相关数据，保留体质检测结果
    clearTongueDataOnly() {
      // 清除舌诊相关的本地存储数据
      uni.removeStorageSync('tongueAnalysisResult')
      uni.removeStorageSync('capturedImage')

      // 清除检测管理器中的舌诊数据，但保留体质检测数据
      detectionManager.clearData('tongue')

      console.log('舌诊数据已清除，体质检测结果已保留')
    },

    // 清除所有数据
    clearAllData() {
      // 清除检测数据
      this.clearDetectionData()

      // 可以根据需要清除更多数据，但保留用户基本信息
      // uni.removeStorageSync('userInfo') // 如果需要也清除用户信息，取消注释

      console.log('所有相关数据已清除')
    }
  }
}
</script>

<style scoped>
.detailed-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f6fcfd 0%, #eafcff 100%);
  display: flex;
  flex-direction: column;
}

/* 头部进度区域 */
.header-section {
  padding: 40rpx 32rpx 20rpx;
  background: white;
  border-bottom: 1rpx solid #f0f0f0;
}

.progress-info {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.progress-text {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
  min-width: 80rpx;
}

.progress-bar {
  flex: 1;
  height: 8rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3ec6c6 0%, #2ea5a5 100%);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

/* 问题区域 */
.question-section {
  flex: 1;
  padding: 32rpx;
}

.question-card {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.question-text {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  line-height: 1.6;
  margin-bottom: 40rpx;
  display: block;
}

/* 选项列表 */
.options-list {
  margin-bottom: 40rpx;
}

.option-item {
  display: flex;
  align-items: flex-start;
  padding: 24rpx 20rpx;
  margin-bottom: 16rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.option-item.selected {
  background: #e8f8f8;
  border-color: #3ec6c6;
}

.option-item:active {
  transform: scale(0.98);
}

.option-radio {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  margin-right: 20rpx;
  margin-top: 4rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.option-item.selected .option-radio {
  border-color: #3ec6c6;
}

.radio-inner {
  width: 20rpx;
  height: 20rpx;
  background: #3ec6c6;
  border-radius: 50%;
}

.option-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

/* 后续问题区域 */
.followup-section {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 32rpx;
  margin-top: 32rpx;
}

.followup-title {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
  margin-bottom: 24rpx;
  display: block;
}

.followup-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.followup-option {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  background: #f5f5f5;
  border-radius: 8rpx;
  border: 1rpx solid transparent;
  transition: all 0.3s ease;
  min-width: 200rpx;
}

.followup-option.selected {
  background: #e8f8f8;
  border-color: #3ec6c6;
}

.followup-radio {
  width: 28rpx;
  height: 28rpx;
  border: 1rpx solid #ddd;
  border-radius: 50%;
  margin-right: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.followup-option.selected .followup-radio {
  border-color: #3ec6c6;
}

.followup-option .radio-inner {
  width: 14rpx;
  height: 14rpx;
}

.followup-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}



/* 底部按钮区域 */
.bottom-section {
  padding: 24rpx 32rpx 40rpx;
  background: white;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  gap: 20rpx;
}

.prev-btn, .next-btn {
  height: 88rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  transition: all 0.3s ease;
}

.prev-btn {
  flex: 1;
  background: #f5f5f5;
  color: #666;
}

.prev-btn:disabled {
  opacity: 0.5;
}

.next-btn {
  flex: 2;
  background: linear-gradient(90deg, #3ec6c6 0%, #2ea5a5 100%);
  color: white;
}

.next-btn.btn-disabled {
  background: #f5f5f5;
  color: #666;
  pointer-events: auto;
}

.prev-btn:active, .next-btn:active {
  transform: scale(0.98);
}



/* 响应式调整 */
@media (max-width: 750rpx) {
  .followup-options {
    flex-direction: column;
  }

  .followup-option {
    min-width: auto;
    width: 100%;
  }
}

/* 加载状态样式 */
.loading-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
}

.loading-content {
  text-align: center;
}

.loading-text {
  font-size: 32rpx;
  color: #666;
  margin-top: 20rpx;
}
</style>