<template>
	<view class="feedback-container">
		<view class="feedback-form">
			<textarea
				class="feedback-textarea"
				v-model="feedbackContent"
				placeholder="请输入您的宝贵意见和建议..."
				auto-height
				placeholder-style="color:#a0a0a0"
				:disabled="submitting"
			/>
			<button
				class="submit-button"
				:class="{ 'submitting': submitting }"
				@click="submitFeedback"
				:disabled="submitting"
			>
				{{ submitting ? '提交中...' : '提交反馈' }}
			</button>
		</view>
	</view>
</template>

<script>
	import { feedback } from '@/api/system/feedback'

	export default {

		data() {
			return {
				feedbackContent: '',
				submitting: false // 添加提交状态
			}
		},
		methods: {

			async submitFeedback() {
				if (!this.feedbackContent.trim()) {
					uni.showModal({
						title: '提示',
						content: '请输入您的宝贵意见和建议',
						showCancel: false,
						confirmText: '确定',
						confirmColor: '#3ec6c6'
					});
					return;
				}

				if (this.submitting) {
					return; // 防止重复提交
				}

				try {
					this.submitting = true;
					uni.showLoading({
						title: '提交中...'
					});


					// 准备提交数据
					const feedbackData = {
						content: this.feedbackContent.trim(),
					};

					console.log('提交反馈数据:', feedbackData);

					// 调用反馈API
					const response = await feedback(feedbackData);
					console.log('反馈提交响应:', response);

					uni.hideLoading();

					// 提交成功 - 使用模态框确保完整显示
					uni.showModal({
						title: '提交成功',
						content: '反馈已提交，感谢您的宝贵意见！我们会认真考虑您的建议。',
						showCancel: false,
						confirmText: '确定',
						confirmColor: '#3ec6c6',
						success: () => {
							this.feedbackContent = ''; // 清空输入框
							uni.navigateBack(); // 提交成功后返回上一页
						}
					});

				} catch (error) {
					console.error('提交反馈失败:', error);
					uni.hideLoading();

					// 获取具体错误信息
					let errorMessage = '提交失败，请稍后重试'
					if (error && error.message) {
						errorMessage = error.message
					} else if (error && error.msg) {
						errorMessage = error.msg
					} else if (error && error.data && error.data.msg) {
						errorMessage = error.data.msg
					}

					// 使用模态框显示错误信息，确保完整显示
					uni.showModal({
						title: '提交失败',
						content: errorMessage,
						showCancel: false,
						confirmText: '确定',
						confirmColor: '#3ec6c6'
					});
				} finally {
					this.submitting = false;
				}
			}
		}
	}
</script>

<style lang="scss">
	.feedback-container {
		min-height: 100vh;
		background-color: #f8f8f8;
	}

	.feedback-form {
		padding: 20rpx;
	}

	.feedback-textarea {
		width:100% ;/* Adjust for padding */
		min-height: 300rpx;
		background-color: #fff;
		border-radius: 15rpx; /* 增加圆角 */
		padding: 25rpx; /* 调整填充 */
		font-size: 32rpx;
		
		box-sizing: border-box;
		margin-bottom: 40rpx; /* 增加与按钮的间距 */
		box-shadow: 0 4px 10px rgba(0,0,0,0.05); /* 更柔和的阴影 */
		border: 1px solid #f0f0f0; /* 添加细边框 */
	}

	.submit-button {
		background-color: #3ec6c6;
		color: #fff;
		border-radius: 20rpx; /* 更圆润的按钮 */
		font-size: 30rpx; /* 增大字体 */
		padding: 20rpx 0;
		box-shadow: 0 4px 10px rgba(62, 198, 198, 0.2); /* 按钮阴影 */
		transition: all 0.3s ease;
		border: none;
		outline: none;
	}

	.submit-button:active {
		transform: translateY(2px); /* 点击效果 */
	}

	.submit-button.submitting {
		background-color: #ccc;
		box-shadow: none;
		transform: none;
		cursor: not-allowed;
	}

	.submit-button:disabled {
		background-color: #ccc;
		box-shadow: none;
		transform: none;
		cursor: not-allowed;
	}

	.feedback-textarea:disabled {
		background-color: #f5f5f5;
		color: #999;
	}
</style> 