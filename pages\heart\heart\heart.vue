<template>
	<view class="container">
		<!-- 新增的温馨提示 -->
		<view class="slogan-text">阳光生活，关爱心理健康</view>

		<view class="description">请根据过去一周的实际感受，选择最符合的选项。</view>

		<!-- 加载状态 -->
		<view v-if="loading" class="loading-container">
			<view class="loading-text">正在加载评测问题...</view>

		</view>

		<!-- 问题列表 -->
		<view v-else class="question-list">
			<view v-for="(question, index) in questions" :key="question.id" class="question-item">
				<view class="question-text">{{ question.text }}</view>
				<radio-group @change="radioChange($event, question.id)" class="options-group">
					<label v-for="(option, optIndex) in question.options" :key="optIndex" class="uni-list-cell uni-list-cell-pd">
						<view>
							<radio :value="(option.value || 0).toString()" :checked="option.value === question.selected" />
						</view>
						<view>{{ option.label || '' }}</view>
					</label>
				</radio-group>
			</view>
		</view>

		<button @click="submitAnswers" class="submit-button">提交评测</button>


	</view>
</template>

<script>
	import uniNavBar from '@/uni_modules/uni-nav-bar/components/uni-nav-bar/uni-nav-bar.vue';
	import { psychologyDetection, psychologyQuestion } from '@/api/system/heart.js';

	export default {
		components: {
			uniNavBar
		},
		data() {
			return {
				questions: [], // 从服务器加载问题数据
				loading: true, // 数据加载状态
				questionsLoaded: false, // 题库是否加载完成

				result: null
			};
		},

		async onLoad() {
			// 加载心理评测问题数据
			await this.loadQuestions()
			// 页面加载时检查用户信息
			this.checkAndShowUserInfo()
		},

		methods: {
			// 加载心理评测问题数据
			async loadQuestions() {
				try {
					this.loading = true
					console.log('开始从服务器加载心理评测问题数据...')

					// 调用API获取服务器上的JSON文件
					const response = await psychologyQuestion()
					console.log('服务器问题数据响应:', response)

					// 处理响应数据
					if (response && Array.isArray(response)) {
						// 直接是数组格式
						this.questions = this.validateAndFixQuestions(response)
						console.log('使用直接数组格式，共', response.length, '个问题')

						// 验证题库是否有效加载（至少有20道题）
						if (this.questions.length >= 20) {
							this.questionsLoaded = true
							console.log('题库加载完成，共', this.questions.length, '道题')
						} else {
							console.warn('题库数量不足，只有', this.questions.length, '道题')
						}
					} else {
						console.error('无法识别的数据格式:', response)
						throw new Error('服务器返回的问题数据格式不正确')
					}
					console.log('成功加载问题数据:', this.questions)

				} catch (error) {
					console.error('从服务器加载问题数据失败:', error)
					
					if(code!=401){
						// 显示错误提示
					uni.showToast({
						title: '加载问题失败，请检查网络连接',
						icon: 'none',
						duration: 3000
					})
					}
					

					// 设置空数组，避免渲染错误
					this.questions = []

				} finally {
					this.loading = false
					console.log('问题数据加载完成')
				}
			},

			// 验证和修复问题数据格式
			validateAndFixQuestions(questions) {
				if (!Array.isArray(questions)) {
					return []
				}

				return questions.map((question, index) => {
					// 确保问题有基本结构
					const fixedQuestion = {
						id: question.id || (index + 1),
						text: question.text || '',
						options: [],
						selected: question.selected || 0,
						reverseScored: question.reverseScored || false
					}

					// 验证和修复选项数据
					if (Array.isArray(question.options)) {
						fixedQuestion.options = question.options.map((option, optIndex) => {
							// 处理不同的选项数据格式
							if (typeof option === 'string') {
								// 如果选项是字符串
								return {
									label: option,
									value: optIndex + 1
								}
							} else if (typeof option === 'object' && option !== null) {
								// 如果选项是对象
								return {
									label: option.label || option.text || option.title || `选项${optIndex + 1}`,
									value: option.value !== undefined ? option.value : (optIndex + 1)
								}
							} else {
								// 其他情况使用默认值
								return {
									label: `选项${optIndex + 1}`,
									value: optIndex + 1
								}
							}
						})
					} else {
						// 如果没有选项，设置为空数组
						fixedQuestion.options = []
					}

					return fixedQuestion
				})
			},

			radioChange(evt, questionId) {
				const question = this.questions.find(q => q.id === questionId);
				if (question) {
					question.selected = parseInt(evt.detail.value);
				}
			},

			// 页面加载时检查并显示用户信息
			checkAndShowUserInfo() {
				console.log('心理评测页面 - 检查用户信息')
				// 首先检查用户信息是否完整
				if (this.checkUserInfo()) {
					// 用户信息完整，直接进行评测
					console.log('用户信息完整，可以开始评测')
				}
			},

			// 检查用户信息是否完善
			checkUserInfo() {
				try {
					const userInfo = uni.getStorageSync('userInfo') || {}

					// 检查必填字段
					const requiredFields = [
						{ key: 'name', label: '姓名' },
						{ key: 'sex', label: '性别' },
						{ key: 'dateBirth', label: '出生年月' },
						{ key: 'phonenumber', label: '手机号' },
						{ key: 'height', label: '身高' },
						{ key: 'weight', label: '体重' }
					]

					const missingFields = []
					requiredFields.forEach(field => {
						if (!userInfo[field.key] || userInfo[field.key].toString().trim() === '') {
							missingFields.push(field.label)
						}
					})

					if (missingFields.length > 0) {
						this.showUserInfoModal(missingFields)
						return false
					}

					return true
				} catch (error) {
					console.error('检查用户信息失败:', error)
					this.showUserInfoModal(['基本信息'])
					return false
				}
			},

			// 显示用户信息完善提示弹窗
			showUserInfoModal(missingFields) {
				const fieldText = missingFields.length > 3
					? '基本信息'
					: missingFields.join('、')

				uni.showModal({
					title: '信息不完整',
					content: `为了更好的心理评测，请先完善${fieldText}等信息`,
					confirmText: '去完善',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							// 记录来源页面，跳转到信息填写页面
							uni.navigateTo({
								url: '/pages/my/healthInput/analyse?from=psychology'
							})
						} else {
							// 用户选择取消，返回上一页
							uni.navigateBack({
								delta: 1
							})
						}
					}
				})
			},

			submitAnswers() {
				// 直接进行评测
				this.proceedWithEvaluation()
			},



			// 计算年龄
			calculateAge(birthDate) {
				if (!birthDate) return null

				const birth = new Date(birthDate)
				const today = new Date()
				let age = today.getFullYear() - birth.getFullYear()
				const monthDiff = today.getMonth() - birth.getMonth()

				if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
					age--
				}

				return age
			},

			// 继续评测流程
			async proceedWithEvaluation() {
				let totalScore = 0;
				let allAnswered = true;

				// 反序题号列表（题号从1开始）
				const reverseQuestions = [2, 5, 6, 11, 12, 14, 16, 17, 18, 20];

				console.log('=== 心理评测计分详情 ===');

				for (const question of this.questions) {
					if (question.selected === 0) { // Check if any question is unanswered
						allAnswered = false;
						break;
					}

					// 计算该题的分数
					let questionScore = question.selected;
					const isReverse = reverseQuestions.includes(question.id);

					// 如果是反序题，需要反转分数 (A=4, B=3, C=2, D=1)
					if (isReverse) {
						questionScore = 5 - question.selected; // 1->4, 2->3, 3->2, 4->1
					}

					console.log(`题目${question.id}: 选择=${question.selected}, ${isReverse ? '反序' : '正序'}, 得分=${questionScore}`);

					totalScore += questionScore;
				}

				console.log(`总分: ${totalScore}, 严重度指数: ${totalScore / 80}`);
				console.log('=== 计分详情结束 ===');

				if (!allAnswered) {
					uni.showToast({
						title: '请回答所有问题！',
						icon: 'none'
					});
					this.result = null; // Clear previous results if not all questions are answered
					return;
				}

				// 防止题库未加载完成时误提交
				if (!this.questionsLoaded) {
					console.log('题库未加载完成，阻止提交');
					return;
				}

				try {
					// 显示加载提示
					uni.showLoading({
						title: '正在分析心理状态...',
						mask: true
					});

					// 构建API数据
					const apiData = this.buildApiData(totalScore);
					console.log('=== 心理评测API数据 ===');
					console.log('发送数据:', JSON.stringify(apiData, null, 2));

					// 调用后端API
					const response = await psychologyDetection(apiData);
					console.log('=== 后端API响应 ===');
					console.log('响应数据:', JSON.stringify(response, null, 2));

					// 隐藏加载提示
					uni.hideLoading();

					// 处理本地计算结果
					// 抑郁严重度指数 = 各条目累计分 / 80
					const severityIndex = totalScore / 80;
					let level = '';
					let description = '';

					if (severityIndex < 0.5) {
						level = '无抑郁';
						description = '您的心理状态良好，没有抑郁症状。';
					} else if (severityIndex >= 0.5 && severityIndex < 0.6) {
						level = '轻微至轻度抑郁';
						description = '您可能存在轻微的抑郁情绪，建议关注心理健康。';
					} else if (severityIndex >= 0.6 && severityIndex < 0.7) {
						level = '中至重度抑郁';
						description = '您可能存在较为明显的抑郁症状，建议寻求专业帮助。';
					} else { // severityIndex >= 0.7
						level = '重度抑郁';
						description = '您可能存在严重的抑郁症状，强烈建议及时寻求专业医疗帮助。';
					}

					this.result = {
						totalScore,
						severityIndex: Math.round(severityIndex * 1000) / 1000, // 保留3位小数
						level,
						description,
						apiResponse: response, // 保存API响应数据
						testTime: this.getCurrentTime()
					};

					// 跳转到报告页面
					this.navigateToReport();

				} catch (error) {
					// 隐藏加载提示
					uni.hideLoading();

					console.error('心理评测API调用失败:', error);

					// 显示错误提示
					const errorMsg = error?.message || error?.msg || '心理评测失败，请重试';
					uni.showToast({
						title: errorMsg,
						icon: 'none',
						duration: 3000
					});

					// 即使API失败，也显示本地计算结果
					const severityIndex = totalScore / 80;
					let level = '';
					let description = '';

					if (severityIndex < 0.5) {
						level = '无抑郁';
						description = '您的心理状态良好，没有抑郁症状。';
					} else if (severityIndex >= 0.5 && severityIndex < 0.6) {
						level = '轻微至轻度抑郁';
						description = '您可能存在轻微的抑郁情绪，建议关注心理健康。';
					} else if (severityIndex >= 0.6 && severityIndex < 0.7) {
						level = '中至重度抑郁';
						description = '您可能存在较为明显的抑郁症状，建议寻求专业帮助。';
					} else { // severityIndex >= 0.7
						level = '重度抑郁';
						description = '您可能存在严重的抑郁症状，强烈建议及时寻求专业医疗帮助。';
					}

					this.result = {
						totalScore,
						severityIndex: Math.round(severityIndex * 1000) / 1000, // 保留3位小数
						level,
						description,
						apiError: true, // 标记API调用失败
						testTime: this.getCurrentTime()
					};

					// 跳转到报告页面
					this.navigateToReport();
				}
			},

			// 构建API数据
			buildApiData(totalScore) {
				// 获取用户信息
				const userInfo = uni.getStorageSync('userInfo') || {};
				console.log('获取的用户信息:', userInfo);

				// 构建包含用户信息和心理评测数据的完整数据
				const apiData = {
					// 心理评测字段
					psychology: totalScore, // 心理评测总分

					// 用户身份信息（扁平化结构，便于后端接收）
					TimeStamp: userInfo.TimeStamp || new Date().getTime(),
					person_name: userInfo.name || '未知',
					sex: userInfo.sex || '未知',
					dateBirth: userInfo.dateBirth || '',
					phone: userInfo.phonenumber || '',
					occupation: userInfo.occupation || '',
					maritalStatus: userInfo.maritalStatus || '',
					height: userInfo.height || '',
					weight: userInfo.weight || '',
					age: userInfo.age || this.calculateAge(userInfo.dateBirth) || 0,
					diastolicPressure: userInfo.diastolicPressure || '',
					systolicPressure: userInfo.systolicPressure || '',
					allergy: userInfo.allergy || '',
					medicalHistory: userInfo.medicalHistory || '',
					UserseeionType: 3
				};

				console.log('=== 包含用户信息的心理评测API数据 ===');
				console.log('完整API数据:', JSON.stringify(apiData, null, 2));

				return apiData;
			},


			onClickLeft() {
				uni.navigateBack();
			},

			// 跳转到报告页面
			navigateToReport() {
				if (!this.result) {
					uni.showToast({
						title: '评测结果异常',
						icon: 'none'
					})
					return
				}

				try {
					// 构造报告数据
					const reportData = {
						totalScore: this.result.totalScore,
						severityIndex: this.result.severityIndex,
						level: this.result.level,
						testTime: this.result.testTime,
						apiResponse: this.result.apiResponse || null
					}

					// 清除可能的心理评测缓存数据（可选）
					uni.removeStorageSync('psychologyAnswers')
					uni.removeStorageSync('psychologyResult')
					uni.removeStorageSync('psychologyTempData')

					// 跳转到报告页面并关闭当前页面（自动清除数据）
					const dataStr = encodeURIComponent(JSON.stringify(reportData))
					uni.redirectTo({
						url: `/pages/heart/heart/report?data=${dataStr}`
					})
				} catch (error) {
					console.error('跳转报告页面失败:', error)
					uni.showToast({
						title: '跳转失败',
						icon: 'none'
					})
				}
			},

			// 获取当前时间
			getCurrentTime() {
				const now = new Date()
				const year = now.getFullYear()
				const month = String(now.getMonth() + 1).padStart(2, '0')
				const day = String(now.getDate()).padStart(2, '0')
				const hours = String(now.getHours()).padStart(2, '0')
				const minutes = String(now.getMinutes()).padStart(2, '0')
				return `${year}-${month}-${day} ${hours}:${minutes}`
			}

		}
	}
</script>

<style>
	.container {
		padding: 15rpx;
		background-color: #f8f8f8;
		min-height: 100vh;
	}

	.title {
		display: none; /* Hide the old title */
	}

	.description {
		font-size: 28rpx;
		color: #666;
		text-align: center;
		margin-bottom: 40rpx;
		margin-top: 20rpx; /* Add some space after nav-bar or slogan */
	}

	/* 新增的slogan样式 */
	.slogan-text {
		font-size: 30rpx;
		color: #3ec6c6; /* 使用主题色，使之更和谐 */
		text-align: center;
		margin-top: 15rpx; /* 与导航栏的间距 */
		font-weight: bold;

	}

	.question-list {
		background-color: #fff;
		border-radius: 20rpx;
		padding: 30rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
		margin-bottom: 40rpx;
	}

	.question-item {
		margin-bottom: 40rpx;
		border-bottom: 1rpx solid #eee;
		padding-bottom: 30rpx;
	}

	.question-item:last-child {
		border-bottom: none;
		margin-bottom: 0;
		padding-bottom: 0;
	}

	.question-text {
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 20rpx;
		color: #333;
	}

	.options-group {
		display: flex;
		flex-direction: column;
	}

	.uni-list-cell {
		display: flex;
		align-items: center;
		margin-bottom: 15rpx;
		font-size: 28rpx;
		color: #555;
	}

	.uni-list-cell:last-child {
		margin-bottom: 0;
	}

	radio {
		transform: scale(0.8);
		margin-right: 15rpx;
	}

	.submit-button {
		background-color: #3ec6c6;
		color: #fff;
		border-radius: 20rpx;
		font-size: 30rpx;
		padding: 15rpx 0;
		margin-bottom: 40rpx;
	}

	.result-card {
		background-color: #e0f7fa;
		border-radius: 20rpx;
		padding: 40rpx;
		text-align: center;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
	}

	.result-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #00796b;
		margin-bottom: 20rpx;
	}

	.result-text {
		font-size: 30rpx;
		color: #333;
		margin-bottom: 10rpx;
	}

	.result-level {
		font-size: 32rpx;
		font-weight: bold;
		color: #d32f2f; /* 可以根据严重程度调整颜色 */
		margin-top: 20rpx;
	}

	/* Popup specific styles */
	.popup-content {
		background-color: #fff;
		padding: 40rpx;
		border-radius: 20rpx;
		text-align: center;
		width: 600rpx; /* Adjust as needed */
		box-sizing: border-box;
	}

	.popup-close-button {
		margin-top: 40rpx;
		background-color: #3ec6c6;
		color: #fff;
		border-radius: 10rpx;
		font-size: 32rpx;
	}

	/* 加载状态样式 */
	.loading-container {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 100rpx 0;
		background-color: #fff;
		border-radius: 20rpx;
		margin-bottom: 40rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
	}

	.loading-text {
		font-size: 32rpx;
		color: #666;
	}
</style>