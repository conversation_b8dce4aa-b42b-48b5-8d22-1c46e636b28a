<template>
  <view class="digital-health-container">
    <!-- 主要功能区域 -->
    <view class="main-content">
      <!-- AI舌诊卡片 -->
      <view class="function-card ai-tongue-card" @click="navigateToTongue">
        <view class="card-content">
          <view class="icon-wrapper icon-scan">
				<uni-icons type="scan" size="28" color="#fff"></uni-icons>
			</view>
          <view class="card-text">
            <view class="card-title">AI舌诊</view>
            <view class="card-subtitle">拍摄舌面照片，AI辅助诊断</view>
          </view>
        </view>
      </view>

      <!-- AI体质检测卡片 -->
      <view class="function-card ai-constitution-card" @click="navigateToConstitution">
        <view class="card-content">
          <view class="icon-wrapper icon-constitution ">
            <text class="iconfontA icon-tizhijiance card-icon"></text>
          </view>
          <view class="card-text">
            <view class="card-title">AI体质检测</view>
            <view class="card-subtitle">回答问诊问题，检测自身体质</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部说明文字 -->
    <view class="bottom-description">
      <text class="description-text">
        为诊疗和科研，采集您的四诊信息。数据将匿名化处理并加密，仅用于医疗分析，助力精准诊断，提升治疗效果。
      </text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {

    }
  },
  methods: {
    // 跳转到AI舌诊页面
    navigateToTongue() {
      console.log('跳转到AI舌诊')
      uni.navigateTo({
        url: '/pages/gather/diagnosis/diagnosis'
      })
    },

    // 跳转到体质检测页面
    navigateToConstitution() {
      console.log('跳转到体质检测')
      uni.navigateTo({
        url: '/pages/gather/healthy/healthy'
      })
    },

  }
}
</script>

<style lang="scss" scoped>
.digital-health-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f6fcfd 60%, #eafcff 100%);
  display: flex;
  flex-direction: column;
  padding: 60rpx 30rpx 60rpx;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;

  gap: 50rpx;
}

.function-card {
  background: #fff;
  border-radius: 32rpx;
  padding: 48rpx 36rpx;
  box-shadow: 0 8rpx 32rpx 0 rgba(62,198,198,0.10);
  transition: all 0.3s ease;
  border: 2rpx solid transparent;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 12rpx 40rpx 0 rgba(62,198,198,0.15);
  }
}

.card-content {
  display: flex;
  align-items: center;
  gap: 36rpx;
}

.icon-container {
  width: 88rpx;
  height: 88rpx;
  background: linear-gradient(135deg, #6fd6e8 0%, #3ec6c6 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 4rpx 16rpx 0 rgba(62,198,198,0.20);
}

.icon-wrapper {
  width: 88rpx;
  height: 88rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 4rpx 16rpx 0 rgba(62,198,198,0.20);
}

.icon-scan {
  background: linear-gradient(135deg, #6fd6e8 0%, #3ec6c6 100%);
}
.icon-constitution{
  background: linear-gradient(135deg, #6fd6e8 0%, #3ec6c6 100%);
}

.card-icon {
  font-size: 58rpx;
  color: #ffffff !important;
  filter: brightness(0) invert(1);
}

.card-icon::before {
  color: #ffffff !important;
}

.card-text {
  flex: 1;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #222;
  margin-bottom: 8rpx;
  letter-spacing: 0.5rpx;
}

.card-subtitle {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.bottom-description {
//   margin-top: auto;
  padding: 10rpx 30rpx 30rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20rpx;
  margin: 10rpx 20rpx 20rpx;
  backdrop-filter: blur(8rpx);
  box-shadow: 0 4rpx 20rpx 0 rgba(62,198,198,0.08);
}

.description-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
  text-align: center;
  display: block;
  font-weight: 400;
//   letter-spacing: 0.3rpx;
}

</style>