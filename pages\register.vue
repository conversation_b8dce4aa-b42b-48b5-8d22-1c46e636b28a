<template>
  <view class="register-container">
    <!-- 头部logo区域 -->
    <view class="logo-content">
      <image class="logo-image" :src="globalConfig.appInfo.logo" mode="widthFix"></image>
      <text class="title">数智舌诊移动端注册</text>
    </view>

    <!-- 注册表单 -->
    <view class="register-form">
      <!-- 单位名称输入 -->
      <!-- <view class="input-item">
        <uni-icons type="home" size="20" color="#999" class="input-icon"></uni-icons>
        <input v-model="registerForm.company" class="input" type="text" placeholder="请输入单位名称" maxlength="30" />
      </view> -->

      <!-- 手机号输入 -->
      <view class="input-wrapper">
        <view class="input-item" :class="{ 'error': errors.phonenumber }">
          <uni-icons type="phone" size="20" color="#999" class="input-icon"></uni-icons>
          <input v-model="registerForm.phonenumber" @input="handlePhoneInput" @blur="validatePhone" class="input" type="number" placeholder="请输入手机号" />
        </view>
        <view v-if="errors.phonenumber" class="error-message">{{ errors.phonenumber }}</view>
      </view>

      <!-- 账号输入 -->
      <view class="input-wrapper">
        <view class="input-item" :class="{ 'error': errors.username }">
          <uni-icons type="person" size="20" color="#999" class="input-icon"></uni-icons>
          <input v-model="registerForm.username" @input="clearUsernameError" @blur="validateUsername" class="input" type="text" placeholder="请输入账号" maxlength="30" />
        </view>
        <view v-if="errors.username" class="error-message">{{ errors.username }}</view>
      </view>

      <!-- 密码输入 -->
      <view class="input-wrapper">
        <view class="input-item" :class="{ 'error': errors.password }">
          <uni-icons type="locked" size="20" color="#999" class="input-icon"></uni-icons>
          <input v-model="registerForm.password" @input="clearPasswordError" @blur="validatePassword" type="password" class="input" placeholder="请输入密码" maxlength="20" />
        </view>
        <view v-if="errors.password" class="error-message">{{ errors.password }}</view>
      </view>

      <!-- 确认密码输入 -->
      <view class="input-wrapper">
        <view class="input-item" :class="{ 'error': errors.confirmPassword }">
          <uni-icons type="locked" size="20" color="#999" class="input-icon"></uni-icons>
          <input v-model="registerForm.confirmPassword" @input="clearConfirmPasswordError" @blur="validateConfirmPassword" type="password" class="input" placeholder="请输入重复密码" maxlength="20" />
        </view>
        <view v-if="errors.confirmPassword" class="error-message">{{ errors.confirmPassword }}</view>
      </view>

      <!-- 验证码输入 -->
      <!-- <view class="input-item captcha-item" v-if="captchaEnabled">
        <uni-icons type="chatboxes" size="20" color="#999" class="input-icon"></uni-icons>
        <input v-model="registerForm.code" type="number" class="input captcha-input" placeholder="请输入验证码" maxlength="4" />
        <view class="captcha-image" @click="getCode">
          <image :src="codeUrl" class="captcha-img"></image>
        </view>
      </view> -->

      <!-- 注册按钮 -->
      <view class="action-btn">
        <button @click="handleRegister()" class="register-btn">注册</button>
      </view>

      <!-- 登录链接 -->
      <view class="login-link">
        <text @click="handleUserLogin" class="link-text">使用已有账号登录</text>
      </view>
    </view>
  </view>
</template>

<script>
  import { getCodeImg, register } from '@/api/login'
  import { setUserName, setPassWord } from '@/utils/auth'

  export default {
    data() {
      return {
        codeUrl: "",
        // captchaEnabled: true,
        globalConfig: getApp().globalData.config,
        registerForm: {
		  // company: "",
		  phonenumber: "",
          username: "",
          password: "",
          confirmPassword: "",
          code: "",
          uuid: ''
        },
        errors: {
          phonenumber: '',
          username: '',
          password: '',
          confirmPassword: ''
        }
      }
    },

    computed: {
      // 确保 registerForm 始终存在
      safeRegisterForm() {
        return this.registerForm || {
     
          phonenumber: "",
          username: "",
          password: "",
          confirmPassword: "",
          code: "",
          uuid: ''
        }
      }
    },

    beforeCreate() {
      // 确保数据对象正确初始化
      console.log('Register page beforeCreate')
    },

    created() {
      console.log('注册表单数据:', this.registerForm)
      // console.log('初始 captchaEnabled:', this.captchaEnabled)
      // this.getCode()
    },
    methods: {
      // 处理手机号输入，自动去除空格
      handlePhoneInput(e) {
        // 去除所有空格
        const cleanValue = e.detail.value.replace(/\s/g, '');
        this.registerForm.phonenumber = cleanValue;
        // 清除错误状态
        this.errors.phonenumber = '';
      },

      // 清除各字段错误状态
      clearUsernameError() {
        this.errors.username = '';
      },

      clearPasswordError() {
        this.errors.password = '';
      },

      clearConfirmPasswordError() {
        this.errors.confirmPassword = '';
      },

      // 验证手机号
      validatePhone() {
        const phone = this.registerForm.phonenumber;
        if (!phone) {
          this.errors.phonenumber = '请输入手机号';
        } else if (!/^1[3-9]\d{9}$/.test(phone)) {
          this.errors.phonenumber = '请输入正确的手机号格式';
        } else {
          this.errors.phonenumber = '';
        }
      },

      // 验证账号
      validateUsername() {
        const username = this.registerForm.username;
        if (!username) {
          this.errors.username = '请输入账号';
        } else if (username.length < 2) {
          this.errors.username = '账号长度不能少于2位';
        } else {
          this.errors.username = '';
        }
      },

      // 验证密码
      validatePassword() {
        const password = this.registerForm.password;
        if (!password) {
          this.errors.password = '请输入密码';
        } else if (password.length < 6) {
          this.errors.password = '密码长度不能少于6位';
        } else {
          this.errors.password = '';
        }
        // 如果确认密码已输入，重新验证确认密码
        if (this.registerForm.confirmPassword) {
          this.validateConfirmPassword();
        }
      },

      // 验证确认密码
      validateConfirmPassword() {
        const confirmPassword = this.registerForm.confirmPassword;
        const password = this.registerForm.password;
        if (!confirmPassword) {
          this.errors.confirmPassword = '请再次输入密码';
        } else if (password !== confirmPassword) {
          this.errors.confirmPassword = '两次输入的密码不一致';
        } else {
          this.errors.confirmPassword = '';
        }
      },
      // 用户登录
      handleUserLogin() {
        this.$tab.navigateTo(`/pages/login`)
      },
      // 获取图形验证码
      // getCode() {
      //   console.log('开始获取验证码...')
      //   getCodeImg().then(res => {
      //     console.log('验证码API返回结果:', res)
      //     this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled
      //     console.log('captchaEnabled设置为:', this.captchaEnabled)
      //     if (this.captchaEnabled) {
      //       this.codeUrl = 'data:image/gif;base64,' + res.img
      //       this.registerForm.uuid = res.uuid
      //       console.log('验证码图片已设置')
      //     } else {
      //       console.log('验证码被禁用')
      //     }
      //   }).catch(error => {
      //     console.error('获取验证码失败:', error)
      //     // 如果获取验证码失败，默认启用验证码但不显示图片
      //     this.captchaEnabled = true
      //   })
      // },
      // 注册方法
      async handleRegister() {
        // 执行所有验证
        this.validatePhone();
        this.validateUsername();
        this.validatePassword();
        this.validateConfirmPassword();

        // 检查是否有错误
        if (this.errors.phonenumber || this.errors.username || this.errors.password || this.errors.confirmPassword) {
          return; // 有错误时不提交
        }

        this.$modal.loading("注册中，请耐心等待...")
        this.register()
      },


      // 用户注册
      async register() {
        register(this.registerForm).then(() => {
          this.$modal.closeLoading()

          // 注册成功后保存用户名和密码到本地存储
          setUserName(this.registerForm.username)
          setPassWord(this.registerForm.password)

          // 注册成功后写入手机号到User-Informations
          let userInfo = uni.getStorageSync('User-Informations') || {};
          userInfo.phonenumber = this.registerForm.phonenumber;
          uni.setStorageSync('User-Informations', userInfo);

          uni.showModal({
            title: "注册成功",
            content: "恭喜您，账号 " + this.registerForm.username + " 注册成功！请使用新账号登录。",
            showCancel: false,
            confirmText: '去登录',
            confirmColor: '#3ec6c6',
            success: (modalRes) => {
              if (modalRes.confirm) {
                // 跳转到登录页面，用户名和密码会自动填充
                uni.redirectTo({ url: `/pages/login` });
              }
            }
          })
        }).catch(() => {
          this.$modal.closeLoading()
          // 让系统默认的错误处理机制处理错误信息
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  page {
    background-color: #f8f9fa;
  }

  .register-container {
    min-height: 100vh;
    background: linear-gradient(180deg, #f6fcfd 60%, #eafcff 100%);
    padding: 0 40rpx;
    display: flex;
    flex-direction: column;
  }

  /* 头部logo区域 */
  .logo-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100rpx 0 60rpx;
    text-align: center;
  }

  .logo-image {
    width: 120rpx;
    height: 120rpx;
    border-radius: 20rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
  }

  .title {
    font-size: 48rpx;
    font-weight: bold;
    color: #4e4e4e;
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  }

  /* 注册表单 */
  .register-form {
    background: #fff;
    border-radius: 24rpx;
    padding: 60rpx 40rpx;
    margin-bottom: 40rpx;
    box-shadow: 0 16rpx 40rpx rgba(0, 0, 0, 0.1);
  }

  /* 输入框样式 */
  .input-wrapper {
    position: relative;
    margin-bottom: 40rpx; /* 固定间距，为错误信息预留空间 */
  }

  .input-item {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border-radius: 16rpx;
    padding: 0 30rpx;
    height: 100rpx;
    border: 2rpx solid transparent;
    transition: all 0.3s ease;
  }

  .input-item:focus-within {
    border-color: #3ec6c6;
    background: #fff;
    box-shadow: 0 0 0 4rpx rgba(62, 198, 198, 0.1);
  }

  .input-item.error {
    border-color: #ff4757;
    background: #fff5f5;
  }

  .input-item.error:focus-within {
    border-color: #ff4757;
    box-shadow: 0 0 0 4rpx rgba(255, 71, 87, 0.1);
  }

  .error-message {
    position: absolute;
    top: 100rpx; /* 紧贴输入框下方 */
    left: 30rpx;
    color: #ff4757;
    font-size: 22rpx;
    line-height: 1.4;
    white-space: nowrap; /* 防止换行 */
  }

  .input-icon {
    margin-right: 20rpx;
    flex-shrink: 0;
  }

  .input {
    flex: 1;
    font-size: 32rpx;
    color: #333;
    height: 100%;
    border: none;
    outline: none;
  }

  .input::placeholder {
    color: #999;
  }

  /* 验证码特殊样式 */
  .captcha-item {
    width: 100%;
  }

  .captcha-input {
    flex: 1;
  }

  .captcha-image {
    margin-left: 20rpx;
    flex-shrink: 0;
  }

  .captcha-img {
    width: 160rpx;
    height: 60rpx;
    border-radius: 8rpx;
    border: 2rpx solid #e0e0e0;
  }

  /* 注册按钮 */
  .action-btn {
    margin: 60rpx 0 40rpx;
  }

  .register-btn {
    width: 100%;
    height: 100rpx;
    background: #3ec6c6;
    color: #fff;
    border: none;
    border-radius: 50rpx;
    font-size: 32rpx;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8rpx 20rpx rgba(62, 198, 198, 0.3);
    transition: all 0.3s ease;
  }

  .register-btn:active {
    transform: translateY(2rpx);
    box-shadow: 0 4rpx 12rpx rgba(62, 198, 198, 0.3);
  }

  .register-btn::after {
    border: none;
  }

  /* 登录链接 */
  .login-link {
    text-align: center;
    margin-top: 40rpx;
  }

  .link-text {
    color: #3ec6c6;
    font-size: 28rpx;
    text-decoration: underline;
  }

  /* 通用样式 */
  .flex {
    display: flex;
  }

  .align-center {
    align-items: center;
  }

  .justify-center {
    justify-content: center;
  }

  .text-center {
    text-align: center;
  }
</style>
