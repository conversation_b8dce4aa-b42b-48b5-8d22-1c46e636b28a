import request from '@/utils/request'

// 体质检测API - 后端使用实体类接收
export function constitutionDetection(data) {
    return request({
        url: '/Moudles/ConsultationApp',
        method: 'post',
        data: data
    })
}

// 获取服务器上的体质检测问卷JSON文件
export function constitutionDetectionJson() {
    return request({
        url: '/external/questionnaire.json',
        method: 'get'
    })
}

// 查询体质检测数据接口（最新的一条）
export function selectRecordsApplastone() {
    return request({
        url: '/common/selectRecordsApplastone',
        method: 'get'
    })
}