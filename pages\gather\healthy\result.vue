<template>
  <view class="result-container">
    <!-- 结果展示区域 -->
    <view class="result-section">
      <view class="result-card">
        <view class="result-header">
          <text class="result-title">体质检测结果</text>
          <text class="result-constitution">{{ getMainConstitution }}</text>
        </view>

        <view class="result-details">
          <view class="detail-item">
            <text class="detail-label">检测时间：</text>
            <text class="detail-value">{{ formattedTestTime }}</text>
          </view>
          <view class="detail-item" v-if="getPrimaryConstitution">
            <text class="detail-label">主要体质：</text>
            <text class="detail-value">{{ getPrimaryConstitution }}</text>
          </view>
          <view class="detail-item" v-if="getSecondaryConstitution">
            <text class="detail-label">次要体质：</text>
            <text class="detail-value">{{ getSecondaryConstitution }}</text>
          </view>
        </view>

        <!-- 体质表现 -->
        <view class="result-description" v-if="getPerformanceText">
          <text class="desc-title">体质表现：</text>
          <text class="desc-content">{{ getPerformanceText }}</text>
        </view>
      </view>
    </view>

    <!-- 养生建议 -->
    <view class="advice-section" v-if="getWellnessAdvice">
      <view class="advice-card">
        <text class="advice-title">养生建议</text>
        <text class="advice-content">{{ getWellnessAdvice }}</text>
      </view>
    </view>

    <!-- 节气信息 -->
    <view class="solar-section" v-if="hasSolarData">
      <view class="solar-card">
        <text class="solar-title">节气养生</text>

        <!-- 节气信息 -->
        <view class="solar-info" v-if="solarTermsInfo">
         
          <text class="solar-days">{{ solarTermsInfo }}</text>
        </view>

        <!-- 节气养生建议 -->
        <view class="solar-advice" v-if="solarAdvice">
          <text class="solar-content">{{ solarAdvice }}</text>
        </view>
      </view>
    </view>


  </view>
</template>

<script>
export default {
  data() {
    return {
      resultData: {},
      apiData: {} // 后端返回的API数据
    }
  },

  computed: {
    // 获取主要体质显示
    getMainConstitution() {
      if (this.apiData.primary && this.apiData.primary.trim()) {
        return this.apiData.primary
      }
      return '体质检测完成'
    },

    // 获取体质表现文本
    getPerformanceText() {
      if (this.apiData.constitution && this.apiData.constitution.Performance && this.apiData.constitution.Performance.trim()) {
        return this.apiData.constitution.Performance
      }
      //历史报告
      if (this.apiData.constitution_tizhi && this.apiData.constitution_tizhi.Performance && this.apiData.constitution_tizhi.Performance.trim()) {
        return this.apiData.constitution_tizhi.Performance
      }
      return null
    },

    // 获取养生建议文本
    getWellnessAdvice() {
      if (this.apiData.constitution && this.apiData.constitution.Wellness_advice && this.apiData.constitution.Wellness_advice.trim()) {
        return this.apiData.constitution.Wellness_advice
      }
        if (this.apiData.constitution_tizhi && this.apiData.constitution_tizhi.Wellness_advice && this.apiData.constitution_tizhi.Wellness_advice.trim()) {
        return this.apiData.constitution_tizhi.Wellness_advice
      }
      return null
    },

    // 获取主要体质
    getPrimaryConstitution() {
      if (this.apiData.primary && this.apiData.primary.trim()) {
        return this.apiData.primary
      }
      return null
    },

    // 获取次要体质
    getSecondaryConstitution() {
      if (this.apiData.secondary && this.apiData.secondary.trim()) {
        return this.apiData.secondary
      }
      return null
    },

    // 格式化检测时间
    formattedTestTime() {
      const testTime = this.resultData.testTime
      if (!testTime) {
        return this.getCurrentTime()
      }

      try {
        let date

        // 处理不同的时间格式
        if (typeof testTime === 'string') {
          // 检查是否是ISO格式 (如: 2025-07-07T15:15:49.000+08:00)
          if (testTime.includes('T') && (testTime.includes('+') || testTime.includes('Z'))) {
            date = new Date(testTime)
          }
          // 检查是否已经是我们想要的格式 (如: 2025-07-07 15:15)
          else if (/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}$/.test(testTime)) {
            return testTime
          }
          // 其他字符串格式，尝试解析
          else {
            date = new Date(testTime)
          }
        } else if (typeof testTime === 'number') {
          // 时间戳
          date = new Date(testTime)
        } else {
          // 其他类型，尝试转换
          date = new Date(testTime)
        }

        // 检查日期是否有效
        if (isNaN(date.getTime())) {
          console.warn('无效的时间格式:', testTime)
          return this.getCurrentTime()
        }

        // 格式化为标准显示格式
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        const hours = String(date.getHours()).padStart(2, '0')
        const minutes = String(date.getMinutes()).padStart(2, '0')
        return `${year}-${month}-${day} ${hours}:${minutes}`

      } catch (error) {
        console.error('时间格式化失败:', error, '原始时间:', testTime)
        return this.getCurrentTime()
      }
    },

    // 检查是否有节气数据
    hasSolarData() {
      return this.solarTermsInfo || this.solarAdvice
    },

    // 节气信息（solarterms字段）
    solarTermsInfo() {
      // 从根级别solarterms获取节气信息
      if (this.apiData.solarterms &&
          typeof this.apiData.solarterms === 'string' &&
          this.apiData.solarterms.trim()) {
        return this.apiData.solarterms
      }

      console.log('没有找到节气信息')
      return null
    },

    // 节气养生建议（getsolar字段）
    solarAdvice() {
      // 从getsolar获取养生建议
      if (this.apiData.getsolar) {
        if (typeof this.apiData.getsolar === 'string' && this.apiData.getsolar.trim()) {
          return this.apiData.getsolar
        }
      }
      console.log('没有找到节气养生建议')
      return null
    }
  },

  onLoad(options) {
    if (options.data) {
      try {
        this.resultData = JSON.parse(decodeURIComponent(options.data))
        console.log('加载的结果数据:', this.resultData)

        // 提取API返回的数据，支持多种数据结构
        if (this.resultData.apiResponse) {
          // 数据直接在apiResponse根级别
          this.apiData = this.resultData.apiResponse
          console.log('使用apiResponse作为数据源:', this.apiData)
        } else if (this.resultData.data) {
          // 数据在data字段中
          this.apiData = this.resultData.data
          console.log('使用data作为数据源:', this.apiData)
        } else {
          // 数据可能直接在根级别
          this.apiData = this.resultData
          console.log('使用根级别数据作为数据源:', this.apiData)
        }

        // 调试输出，帮助了解数据结构
        console.log('=== 体质检测数据结构分析 ===')
        console.log('原始结果数据:', this.resultData)
        console.log('提取的API数据:', this.apiData)
        console.log('原始时间数据:', this.resultData.testTime)
        console.log('格式化后时间:', this.formattedTestTime)
        console.log('主要体质:', this.getPrimaryConstitution)
        console.log('体质表现:', this.getPerformanceText)
        console.log('养生建议:', this.getWellnessAdvice)
      } catch (error) {
        console.error('解析结果数据失败:', error)
        uni.showToast({
          title: '数据加载失败',
          icon: 'error'
        })
      }
    } else {
      console.log('没有接收到数据参数')
    }
  },

  methods: {






    // 获取当前时间
    getCurrentTime() {
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      const hours = String(now.getHours()).padStart(2, '0')
      const minutes = String(now.getMinutes()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}`
    }
  }
}
</script>

<style lang="scss" scoped>
/*
 * 间距规范统一说明：
 * - 模块间距：40rpx (margin-bottom)
 * - 左右内边距：30rpx (padding)
 * - 主标题底部间距：24rpx
 * - 子标题底部间距：12rpx
 * - 内容区域间距：20-30rpx
 */
.result-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f6fcfd 0%, #eafcff 100%);
  padding-bottom: 30rpx;
}


.result-section {
  padding: 30rpx 30rpx 0 30rpx; /* 统一左右内边距，底部不加间距 */
  margin-bottom: 40rpx; /* 统一模块间距 */
}

.result-card {
  background: #fff;
  border-radius: 28rpx;
  padding: 48rpx 40rpx 40rpx 40rpx;
  box-shadow: 0 12rpx 36rpx rgba(78,205,196,0.10);
  border: 1rpx solid #e0f7fa;
}

.result-header {
  text-align: center;
  margin-bottom: 40rpx;

  .result-title {
    display: block;
    font-size: 32rpx;
    color: #666;
    margin-bottom: 20rpx;
    letter-spacing: 2rpx;
  }

  .result-constitution {
    display: block;
    font-size: 56rpx;
    font-weight: 900;
    background: linear-gradient(90deg, #4ECDC4 0%, #44A08D 100%);
    color: transparent;
    -webkit-background-clip: text;
    background-clip: text;
    margin-bottom: 8rpx;
    letter-spacing: 4rpx;
    text-shadow: 0 4rpx 16rpx rgba(78,205,196,0.12);
  }
}

.result-details {
  margin-bottom: 40rpx;
  border-bottom: 1rpx solid #e0f7fa;
  padding-bottom: 24rpx;

  .detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 18rpx 0;
    border-bottom: 1rpx dashed #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .detail-label {
      font-size: 28rpx;
      color: #6ec6ca;
      font-weight: 600;
      min-width: 160rpx;
    }

    .detail-value {
      font-size: 28rpx;
      color: #333;
      font-weight: 600;
      text-align: right;
    }
  }
}

.result-description {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 32rpx 28rpx;
  margin-top: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(78,205,196,0.06);
  border: 1rpx solid #e0f7fa;

  .desc-title {
    display: block;
    font-size: 30rpx;
    font-weight: 700;
    color: #009688;
    margin-bottom: 16rpx;
  }

  .desc-content {
    font-size: 28rpx;
    color: #666;
    line-height: 1.7;
  }
}

// 养生建议区域
.advice-section {
  padding: 0 30rpx; /* 统一左右内边距 */
  margin-bottom: 40rpx; /* 统一模块间距 */
}

.advice-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 30rpx rgba(78,205,196,0.10);
  border: 1rpx solid #e0f7fa;
}

.advice-title {
  display: block;
  font-size: 32rpx;
  font-weight: 700;
  color: #009688;
  margin-bottom: 24rpx; /* 统一标题底部间距 */
}

.advice-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.7;
}

// 节气信息区域
.solar-section {
  padding: 0 30rpx; /* 统一左右内边距 */
  margin-bottom: 40rpx; /* 统一模块间距 */
}

.solar-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 30rpx rgba(78,205,196,0.10);
  border: 1rpx solid #e0f7fa;
}

.solar-title {
  display: block;
  font-size: 32rpx;
  font-weight: 700;
  color: #009688;
  margin-bottom: 24rpx; /* 统一标题底部间距 */
}

.solar-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.7;
}

// 节气信息区域
.solar-info {
  margin-bottom: 30rpx; /* 统一内部间距 */
}

// 节气养生建议区域
.solar-advice {
  margin-bottom: 20rpx; /* 统一内部间距 */
}

// 节气子标题
.solar-subtitle {
  display: block;
  font-size: 26rpx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 12rpx; /* 统一子标题底部间距 */
  border-left: 4rpx solid rgba(255, 255, 255, 0.6);
  padding-left: 15rpx;
}



// 随访建议区域
.followup-section {
  padding: 0 30rpx; /* 统一左右内边距 */
  margin-bottom: 40rpx; /* 统一模块间距 */
}

.followup-card {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
}

.followup-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
  margin-bottom: 24rpx; /* 统一标题底部间距 */
}

.followup-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.followup-item {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10rpx;
  padding: 20rpx;
}

.followup-content {
  font-size: 28rpx;
  color: #fff;
  line-height: 1.6;
}

.solar-days {
  font-size: 30rpx;
  font-weight: 700;
  color: #666;
  text-align: center;
  margin-bottom: 18rpx;
  letter-spacing: 2rpx;
}
</style>
