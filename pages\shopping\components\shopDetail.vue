<template>
  <view class="shop-detail-container">
    <!-- 商品图片轮播区域 -->
    <view class="image-section">
      <swiper
        class="product-swiper"
        :indicator-dots="true"
        :autoplay="false"
        :circular="true"
        indicator-color="rgba(255,255,255,0.5)"
        indicator-active-color="#fff"
      >
        <swiper-item v-for="(image, index) in productDetail.images" :key="index">
          <image
            :src="image"
            mode="aspectFill"
            class="product-image"
            @click="previewImage(index)"
          />
        </swiper-item>
      </swiper>

      <!-- 图片指示器 -->
      <view class="image-indicator">
        <text>{{ currentImageIndex + 1 }}/{{ (productDetail.images && productDetail.images.length) || 0 }}</text>
      </view>

      <!-- 返回按钮 -->
      <view class="back-btn" @click="goBack">
        <uni-icons type="left" size="20" color="#333"></uni-icons>
      </view>

      <!-- 分享按钮 -->
      <view class="share-btn" @click="shareProduct">
        <uni-icons type="redo" size="20" color="#333"></uni-icons>
      </view>
    </view>

    <!-- 商品基本信息 -->
    <view class="product-info-section">
      <view class="price-section">
        <view class="current-price">¥{{ productDetail.price }}</view>
        <view class="original-price" v-if="productDetail.originalPrice">
          ¥{{ productDetail.originalPrice }}
        </view>
        <view class="promotion-tag" v-if="productDetail.isPromotion">
          限时优惠
        </view>
      </view>

      <view class="product-title">{{ productDetail.name }}</view>

      <view class="product-tags">
        <text class="tag" v-if="productDetail.freeShipping">包邮</text>
        <text class="tag" v-if="productDetail.isPromotion">限时优惠</text>
        <text class="tag guarantee-tag">正品保证</text>
        <text class="tag service-tag">七天无理由退货</text>
      </view>

      <view class="sales-info">
        <view class="sales-item">
          <text class="label">已售</text>
          <text class="value">{{ productDetail.sales || 0 }}件</text>
        </view>
        <view class="sales-item">
          <text class="label">库存</text>
          <text class="value">{{ productDetail.stock || 0 }}件</text>
        </view>
        <view class="sales-item">
          <text class="label">好评</text>
          <text class="value">{{ productDetail.rating || '100' }}%</text>
        </view>
      </view>
    </view>

    <!-- 商品详情评价区域 -->
    <view class="detail-section">
      <view class="section-tabs">
        <view
          class="tab-item"
          :class="{ active: activeTab === 'detail' }"
          @click="switchTab('detail')"
        >
          商品详情
        </view>
        <view
          class="tab-item"
          :class="{ active: activeTab === 'reviews' }"
          @click="switchTab('reviews')"
        >
          商品评价({{ productDetail.reviewCount || 0 }})
        </view>
      </view>

      <!-- 商品详情内容 -->
      <view class="tab-content" v-if="activeTab === 'detail'">
        <view class="detail-content">
          <view class="detail-item" v-for="(item, index) in productDetail.detailImages" :key="index">
            <image :src="item" mode="widthFix" class="detail-image" />
          </view>
          <view class="detail-text" v-if="productDetail.description">
            {{ productDetail.description }}
          </view>
        </view>
      </view>

      <!-- 商品评价内容 -->
      <view class="tab-content" v-if="activeTab === 'reviews'">
        <view class="reviews-summary">
          <view class="rating-overview">
            <view class="rating-score">{{ productDetail.rating || '5.0' }}</view>
            <view class="rating-stars">
              <uni-rate
                :value="Math.floor(productDetail.rating || 5)"
                :readonly="true"
                size="16"
                color="#ff9900"
              ></uni-rate>
            </view>
          </view>
          <view class="rating-detail">
            <text>好评率 {{ productDetail.rating || '100' }}%</text>
          </view>
        </view>

        <view class="reviews-list">
          <view class="review-item" v-for="review in productDetail.reviews" :key="review.id">
            <view class="review-header">
              <view class="user-info">
                <image :src="review.avatar" class="user-avatar" />
                <text class="username">{{ review.username }}</text>
              </view>
              <view class="review-date">{{ review.date }}</view>
            </view>
            <view class="review-rating">
              <uni-rate
                :value="review.rating"
                :readonly="true"
                size="14"
                color="#ff9900"
              ></uni-rate>
              <text class="rating-text">非常满意</text>
            </view>
            <view class="review-content">{{ review.content }}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 规格选择弹窗 -->
    <uni-popup ref="specPopup" type="bottom" background-color="#fff" :safe-area="true" @change="onPopupChange">
      <view class="spec-popup-container">
        <view class="spec-popup">
          <view class="popup-header">
            <view class="popup-title">选择规格</view>
            <view class="close-btn" @click="closeSpecPopup">
              <uni-icons type="close" size="20" color="#999"></uni-icons>
            </view>
          </view>

          <scroll-view class="popup-content" scroll-y="true">
            <view class="spec-info">
              <image :src="productDetail.images && productDetail.images[0]" class="spec-image" />
              <view class="spec-details">
                <view class="spec-price">
                  <view class="total-price">¥{{ totalPrice }}</view>
                  <view class="unit-price" v-if="selectedQuantity > 1">单价：¥{{ currentPrice }}</view>
                </view>
                <view class="spec-stock">库存{{ currentStock }}件</view>
                <view class="spec-text" v-if="getSelectedSpecText()">已选：{{ getSelectedSpecText() }}</view>
              </view>
            </view>

            <view class="spec-options">
              <!-- 规格加载状态 -->
              <view v-if="isLoadingSpecs" class="spec-loading">
                <view class="loading-text">正在加载规格信息...</view>
              </view>

              <!-- 规格选项 -->
              <view v-else-if="productDetail.specs && productDetail.specs.length > 0" class="spec-group" v-for="spec in productDetail.specs" :key="spec.name">
                <view class="spec-name">{{ spec.name }}</view>
                <view class="spec-values">
                  <view
                    class="spec-value"
                    :class="{
                      active: selectedSpecs[spec.name] === value,
                      disabled: !isSpecValueAvailable(spec.name, value)
                    }"
                    v-for="value in spec.values"
                    :key="value"
                    @click="selectSpec(spec.name, value)"
                    v-if="isSpecValueAvailable(spec.name, value)"
                  >
                    {{ value }}
                  </view>
                </view>
              </view>

              <!-- 无规格提示 -->
              <view v-else class="no-specs">
                <view class="no-specs-text">该商品暂无规格选项</view>
              </view>
            </view>

            <view class="quantity-section">
              <view class="quantity-label">数量</view>
              <view class="quantity-control">
                <view class="quantity-btn" @click="decreaseQuantity">-</view>
                <input
                  type="number"
                  v-model="selectedQuantity"
                  class="quantity-input"
                  @blur="validateQuantity"
                />
                <view class="quantity-btn" @click="increaseQuantity">+</view>
              </view>
            </view>
          </scroll-view>

          <!-- 弹窗底部按钮 -->
          <view class="popup-actions">
            <!-- 只显示加入购物车按钮 -->
            <view v-if="actionType === 'cart'" class="popup-btn single-btn" @click="handleAddToCart">
              加入购物车
            </view>

            <!-- 显示加入购物车和立即购买按钮 -->
            <view v-else class="popup-btn-group">
              <view class="popup-btn left-btn" @click="handleAddToCart">
                加入购物车
              </view>
              <view class="popup-btn right-btn" @click="handleBuyNow">
                立即购买
              </view>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>

    <!-- 底部导航栏 -->
    <view class="bottom-nav" v-if="showBottomNav">
      <view class="nav-item" @click="toggleFavorite">
        <uni-icons :type="isFavorited ? 'star-filled' : 'star'" :color="isFavorited ? '#ff5555' : '#666'" size="24"></uni-icons>
        <text :style="{ color: isFavorited ? '#ff5555' : '#666' }">{{ isFavorited ? '已收藏' : '收藏' }}</text>
      </view>
      <view class="nav-item" @click="goToCart">
        <uni-icons type="cart" size="24" color="#666"></uni-icons>
        <text>购物车</text>
        <view class="cart-badge" v-if="cartCount > 0">{{ cartCount }}</view>
      </view>
      <view class="action-buttons">
        <view class="action-btn add-cart-btn" @click="showSpecPopup('cart')">
          加入购物车
        </view>
        <view class="action-btn buy-now-btn" @click="showSpecPopup('buy')">
          立即购买
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getProductDetail, getProductSpec } from '@/api/system/shop'

export default {
  name: 'ShopDetail',
  props: {
    productId: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      currentImageIndex: 0,
      activeTab: 'detail', // detail, reviews
      selectedSpecs: {}, // 选中的规格
      selectedQuantity: 1, // 选中的数量
      actionType: 'cart', // cart, buy
      cartCount: 0, // 购物车数量
      isFavorited: false, // 是否已收藏
      showBottomNav: true, // 控制底部导航栏显示
      isLoading: false, // 是否正在加载
      loadError: false, // 是否加载出错
      isLoadingSpecs: false, // 是否正在加载规格
      specsLoaded: false, // 规格是否已加载
      productDetail: {
        id: '',
        name: '',
        price: '',
        originalPrice: '',
        images: [],
        detailImages: [],
        description: '',
        isPromotion: false,
        freeShipping: false,
        sales: 0,
        stock: 0,
        rating: 5.0,
        reviewCount: 0,
        specs: [], // 规格选项
        reviews: [] // 评价列表
      }
    }
  },
  computed: {
    // 计算当前选择的SKU
    currentSKU() {
      // 明确依赖于 selectedSpecs 以确保响应式更新
      const specs = this.selectedSpecs;
      const skuData = this.skuData;
      console.log('💡 计算currentSKU - 规格:', specs, 'SKU数据长度:', skuData ? skuData.length : 0);

      const result = this.getSelectedSKU();
      return result;
    },

    // 计算当前单价
    currentPrice() {
      const sku = this.currentSKU;
      console.log('💰 计算当前价格 - SKU:', sku);
      if (sku && sku.price) {
        return sku.price;
      }
      const defaultPrice = this.productDetail.price || '0.00';
      console.log('💰 使用默认价格:', defaultPrice);
      return defaultPrice;
    },

    // 计算总价格（单价 × 数量）
    totalPrice() {
      const unitPrice = parseFloat(this.currentPrice) || 0;
      const quantity = this.selectedQuantity || 1;
      const total = (unitPrice * quantity).toFixed(2);
      return total;
    },

    // 计算当前库存
    currentStock() {
      const sku = this.currentSKU;
      if (sku && sku.stock !== undefined) {
        return sku.stock;
      }
      const defaultStock = this.productDetail.stock || 0;
      return defaultStock;
    }
  },
  mounted() {
    this.loadProductDetail();
    this.loadCartCount();
    this.loadFavoriteStatus();
  },
  onShow() {
    // 页面显示时刷新评价数据，确保最新评价能及时显示
    this.loadProductReviews();
    this.loadCartCount();
    this.loadFavoriteStatus(); // 重新加载收藏状态
  },
  methods: {
    // 加载商品详情
    async loadProductDetail() {
      this.isLoading = true;
      this.loadError = false;

      try {
        console.log('🛍️ 开始加载商品详情，ID:', this.productId);

        // 显示加载提示
        uni.showLoading({
          title: '加载中...',
          mask: true
        });

        // 直接调用商品详情API
        const response = await getProductDetail(this.productId);


        if (response && (response.data || response.rows)) {
          const productData = response.data || response.rows;

        

          // 处理API返回的商品详情数据
          this.productDetail = this.processProductData(productData);

          console.log('商品详情加载成功:', this.productDetail);
        } else {
          console.warn('商品详情数据格式异常:', response);
          throw new Error('商品详情数据格式异常');
        }

        // 加载评价数据
        this.loadProductReviews();

        uni.hideLoading();
        this.isLoading = false;
      } catch (error) {
        console.error('❌ 加载商品详情失败:', error);
        uni.hideLoading();
        this.isLoading = false;
        this.loadError = true;

        uni.showToast({
          title: '商品信息加载失败',
          icon: 'none'
        });
      }
    },

    // 处理API返回的商品数据
    processProductData(data) {
      // 处理单个商品对象或数组中的第一个商品
      const product = Array.isArray(data) ? data[0] : data;

      if (!product) {
        throw new Error('商品数据为空');
      }

      // 处理图片数据 - 优先使用已解析的images数组
      let productImages;
      if (product.images && Array.isArray(product.images)) {
        // 如果已经有解析好的images数组，直接使用
        productImages = product.images;
      } else {
        // 否则使用processImages方法解析
        productImages = this.processImages(product.images || product.imageUrl || product.image);
      }

      // 处理描述图片数据
      let descriptionImages;
      if (product.descriptionImg && Array.isArray(product.descriptionImg)) {
        // 如果已经有解析好的descriptionImg数组，直接使用
        descriptionImages = product.descriptionImg;
      } else {
        // 否则使用processImages方法解析 image_text 字段
        descriptionImages = this.processImages(product.image_text || product.descriptionImg);
      }

      return {
        id: product.id || product.product_id,
        name: product.name || product.product_name,
        price: product.price || product.min_price || '0.00',
        originalPrice: product.originalPrice || product.min_old_price || product.price || '0.00',
        images: productImages,
        detailImages: descriptionImages.length > 0 ? descriptionImages : productImages, // 优先使用描述图片
        description: this.processDescriptionContent(product.detail_content) || product.description || '暂无描述',
        isPromotion: product.isPromotion || true,
        freeShipping: product.freeShipping || true,
        sales: product.sales || product.max_sale || 0,
        stock: product.stock || product.total_stock || product.stock_status || 999,
        rating: product.rating || 5.0,
        reviewCount: product.reviewCount || 0,
        categoryId: product.categoryId || product.category_id || 0,
        hasSpecs: product.hasSpecs || product.spec_summary || (product.specs && product.specs.length > 0),
        specs: product.specs || [],
        specPrices: product.specPrices || {},
        reviews: []
      };
    },

    // 处理图片数据
    processImages(imageData) {
      try {
        if (!imageData) {
          return ['/static/images/default-product.png'];
        }

        if (typeof imageData === 'string') {
          // 处理特殊格式：{"url1","url2","url3"}
          if (imageData.startsWith('{') && imageData.endsWith('}')) {
            // 移除首尾的大括号
            const content = imageData.slice(1, -1);

            // 按逗号分割并清理引号
            const urls = content.split(',').map(url => {
              return url.trim().replace(/^["']|["']$/g, ''); // 移除首尾引号
            }).filter(url => url.length > 0);

            return urls.length > 0 ? urls : ['/static/images/default-product.png'];
          }

          // 尝试解析为标准JSON数组
          try {
            const parsed = JSON.parse(imageData);
            if (Array.isArray(parsed) && parsed.length > 0) {
              return parsed;
            }
          } catch (e) {
            // 如果解析失败，检查是否是逗号分隔的URL
            if (imageData.includes(',')) {
              return imageData.split(',').map(url => url.trim());
            }
            // 单个URL
            return [imageData];
          }
        }

        if (Array.isArray(imageData)) {
          return imageData.length > 0 ? imageData : ['/static/images/default-product.png'];
        }

        return ['/static/images/default-product.png'];
      } catch (error) {
        console.error('处理图片数据失败:', error, imageData);
        return ['/static/images/default-product.png'];
      }
    },

    // 处理描述内容数据
    processDescriptionContent(contentData) {
      try {
        if (!contentData) {
          return '暂无描述';
        }
        // 如果已经是数组，直接处理
        if (Array.isArray(contentData)) {
          console.log('数组格式描述:', contentData);
          return contentData.join('\n');
        }

        // 如果是字符串，尝试解析
        if (typeof contentData === 'string') {
          // 处理JSON数组格式：["内容1","内容2","内容3"]
          if (contentData.startsWith('[') && contentData.endsWith(']')) {
            try {
              const parsed = JSON.parse(contentData);
              if (Array.isArray(parsed)) {
                console.log('解析JSON数组描述:', parsed);
                return parsed.join('\n');
              }
            } catch (e) {
              console.log('❌ JSON数组解析失败，尝试手动解析');

              // 手动解析数组格式
              const content = contentData.slice(1, -1); // 移除 [ ]
              const regex = /"([^"]*)"/g;
              const items = [];
              let match;

              while ((match = regex.exec(content)) !== null) {
                items.push(match[1]);
              }

              if (items.length > 0) {
                return items.join('\n');
              }
            }
          }

          // 处理特殊格式：{"内容1","内容2","内容3"}
          if (contentData.startsWith('{') && contentData.endsWith('}')) {
            const content = contentData.slice(1, -1);
            const regex = /"([^"]*)"/g;
            const items = [];
            let match;

            while ((match = regex.exec(content)) !== null) {
              items.push(match[1]);
            }

            if (items.length > 0) {
              console.log('解析对象格式描述:', items);
              return items.join('\n');
            }
          }

          // 直接返回字符串
          return contentData;
        }

        // 其他情况
        return contentData.toString();
      } catch (error) {
        console.error('❌ 处理描述内容失败:', error, contentData);
        return '暂无描述';
      }
    },

    // 加载商品规格
    async loadProductSpecs() {
      // 如果正在加载或已经加载过，直接返回
      if (this.isLoadingSpecs || this.specsLoaded) {
        console.log('规格已在加载中或已加载，跳过');
        return;
      }

      this.isLoadingSpecs = true;

      try {
        console.log('开始加载商品规格，商品ID:', this.productId);

        // 显示加载提示
        uni.showLoading({
          title: '加载规格中...',
          mask: true
        });

        // 使用商品ID作为规格查询参数
        const variantId = this.productDetail.variantId || this.productId;

        const response = await getProductSpec(variantId);


        if (response && (response.data || response.rows)) {
          const specData = response.data || response.rows;

          // 保存原始SKU数据
          this.skuData = Array.isArray(specData) ? specData : [specData];

          // 处理规格数据
          this.productDetail.specs = this.processSpecData(specData);


          // 重置规格选择，确保响应式更新
          this.resetSpecSelection();

          // 如果没有规格数据，显示提示
          if (this.productDetail.specs.length === 0) {
            console.log('该商品暂无规格选项');
          }
        } else {
          console.warn('商品规格数据格式异常:', response);

          // 使用默认规格
          this.productDetail.specs = [];
          this.skuData = [];
        }

        this.specsLoaded = true; // 标记规格已加载
        uni.hideLoading();
      } catch (error) {
        console.error('❌ 加载商品规格失败:', error);
        uni.hideLoading();

        // 显示错误提示
        uni.showToast({
          title: '规格加载失败',
          icon: 'none',
          duration: 2000
        });

        // 设置空规格数组，不影响主要功能
        this.productDetail.specs = [];
      } finally {
        this.isLoadingSpecs = false;
      }
    },

    // 处理规格数据
    processSpecData(data) {
      try {
        if (!data) {
          console.log('规格数据为空');
          return [];
        }
        console.log('处理规格数据:', data);

        // 如果是单个对象，包装成数组
        const specArray = Array.isArray(data) ? data : [data];

        // 提取所有可能的规格属性
        const specFields = new Set();
        specArray.forEach(item => {
          // 收集所有规格相关的字段
          Object.keys(item).forEach(key => {
            if (['dosage_form', 'dose', 'flavor', 'specification', 'size', 'color', 'style'].includes(key)) {
              specFields.add(key);
            }
          });
        });

        console.log('发现的规格字段:', Array.from(specFields));

        // 为每个规格字段创建规格选项
        const processedSpecs = [];

        specFields.forEach(field => {
          // 收集该字段的所有唯一值
          const values = new Set();
          specArray.forEach(item => {
            if (item[field] && item[field] !== '') {
              values.add(item[field]);
            }
          });

          if (values.size > 0) {
            // 字段名映射
            const fieldNameMap = {
              'dosage_form': '剂型',
              'dose': '剂量',
              'flavor': '口味',
              'specification': '规格',
              'size': '尺寸',
              'color': '颜色',
              'style': '款式'
            };

            processedSpecs.push({
              name: fieldNameMap[field] || field,
              field: field, // 保存原始字段名，用于匹配
              values: Array.from(values)
            });
          }
        });

        console.log('处理后的规格数据:', processedSpecs);

        // 如果没有找到标准规格字段，尝试传统方式
        if (processedSpecs.length === 0) {
          return this.processTraditionalSpecData(specArray);
        }

        return processedSpecs;
      } catch (error) {
        console.error('❌ 处理规格数据失败:', error, data);
        return [];
      }
    },

    // 处理传统格式的规格数据
    processTraditionalSpecData(specArray) {
      return specArray.map(spec => {
        // 处理不同的规格数据格式
        const specName = spec.name || spec.specName || spec.spec_name || '规格';
        let specValues = spec.values || spec.options || spec.spec_values || [];

        // 如果values是字符串，尝试解析
        if (typeof specValues === 'string') {
          try {
            specValues = JSON.parse(specValues);
          } catch (e) {
            // 如果解析失败，按逗号分割
            specValues = specValues.split(',').map(v => v.trim()).filter(v => v);
          }
        }

        // 确保values是数组
        if (!Array.isArray(specValues)) {
          specValues = [];
        }

        return {
          name: specName,
          values: specValues
        };
      }).filter(spec => spec.values.length > 0); // 过滤掉没有选项的规格
    },

    // 重置规格选择
    resetSpecSelection() {
      this.selectedSpecs = {};
    },

    // 根据选择的规格获取对应的SKU信息
    getSelectedSKU() {
      try {
        if (!this.skuData || !Array.isArray(this.skuData)) {
          console.log('❌ SKU数据无效');
          return null;
        }

        // 如果没有选择任何规格，返回第一个SKU
        if (Object.keys(this.selectedSpecs).length === 0) {
          console.log('🔄 没有选择规格，返回第一个SKU:', this.skuData[0]);
          return this.skuData[0];
        }

        // 查找匹配的SKU
        const matchedSKU = this.skuData.find(sku => {

          // 检查所有选择的规格是否匹配
          const isMatch = Object.entries(this.selectedSpecs).every(([specName, selectedValue]) => {
            // 找到对应的规格字段
            const spec = this.productDetail.specs.find(s => s.name === specName);

            if (!spec || !spec.field) {
              console.log(`❌ 规格 "${specName}" 没有找到对应字段`);
              return false;
            }

            const skuValue = sku[spec.field];
            const match = skuValue === selectedValue;

            return match;
          });

          return isMatch;
        });
        return matchedSKU;
      } catch (error) {
        console.error('❌ 获取SKU失败:', error);
        return null;
      }
    },

    // 检查规格值是否可用
    isSpecValueAvailable(specName, value) {
      try {
        if (!this.skuData || !Array.isArray(this.skuData)) {
          return true; // 如果没有SKU数据，默认都可用
        }

        // 找到对应的规格字段
        const spec = this.productDetail.specs.find(s => s.name === specName);
        if (!spec || !spec.field) {
          return true;
        }

        // 获取其他已选择的规格（排除当前正在检查的规格）
        const otherSelectedSpecs = {};
        Object.entries(this.selectedSpecs).forEach(([key, val]) => {
          if (key !== specName) {
            otherSelectedSpecs[key] = val;
          }
        });

        // 检查是否存在匹配的SKU
        const hasMatchingSKU = this.skuData.some(sku => {
          // 首先检查当前规格值是否匹配
          if (sku[spec.field] !== value) {
            return false;
          }

          // 然后检查其他已选择的规格是否都匹配
          return Object.entries(otherSelectedSpecs).every(([selectedSpecName, selectedValue]) => {
            const selectedSpec = this.productDetail.specs.find(s => s.name === selectedSpecName);
            if (!selectedSpec || !selectedSpec.field) return true;

            return sku[selectedSpec.field] === selectedValue;
          });
        });

        return hasMatchingSKU;
      } catch (error) {
        console.error('检查规格值可用性失败:', error);
        return true; // 出错时默认可用
      }
    },



    // 获取当前SKU编码
    getCurrentSKUCode() {
      const sku = this.getSelectedSKU();
      if (sku && sku.sku_code) {
        return sku.sku_code;
      }
      return '';
    },



    // 预览图片
    previewImage(index) {
      this.currentImageIndex = index;
      uni.previewImage({
        urls: this.productDetail.images,
        current: index
      });
    },

    // 返回上一页
    goBack() {
      uni.navigateBack();
    },

    // 分享商品
    shareProduct() {
      // TODO: 实现分享功能
      uni.showToast({
        title: '分享功能开发中',
        icon: 'none'
      });
    },

    // 切换标签页
    switchTab(tab) {
      this.activeTab = tab;
    },

    // 显示规格选择弹窗
    async showSpecPopup(type) {
      this.actionType = type;
      this.showBottomNav = false; // 隐藏底部导航栏

      // 在弹窗弹起时调用规格API
      await this.loadProductSpecs();

      this.$refs.specPopup.open();
    },

    // 关闭规格选择弹窗
    closeSpecPopup() {
      this.showBottomNav = true; // 显示底部导航栏
      this.$refs.specPopup.close();
    },

    // 选择规格
    selectSpec(specName, value) {

      // 使用Vue.set确保响应式更新
      this.$set(this.selectedSpecs, specName, value);


      if (matchedSKU) {
    
        // 强制触发响应式更新
        this.$forceUpdate();

        // 手动触发价格和库存的更新
        this.$nextTick(() => {
          console.log('🔄 当前显示价格:', this.currentPrice);
          console.log('🔄 当前显示库存:', this.currentStock);
        });
      } else {
        console.log('⚠️ 未找到匹配的SKU');
      }

      // 强制更新视图
      this.$forceUpdate();
    },

    // 检查规格选择并执行操作
    checkAndExecuteAction() {
      // 保留原有逻辑，后续按钮手动调用
      // ... existing code ...
    },

    // 减少数量
    decreaseQuantity() {
      if (this.selectedQuantity > 1) {
        this.selectedQuantity--;
        // 取消自动执行操作
        // this.checkAndExecuteAction();
      }
    },

    // 增加数量
    increaseQuantity() {
      if (this.selectedQuantity < this.currentStock) {
        this.selectedQuantity++;
        // 取消自动执行操作
        // this.checkAndExecuteAction();
      } else {
        uni.showToast({
          title: '超出库存数量',
          icon: 'none'
        });
      }
    },

    // 验证数量输入
    validateQuantity() {
      const quantity = parseInt(this.selectedQuantity);
      if (isNaN(quantity) || quantity < 1) {
        this.selectedQuantity = 1;
      } else if (quantity > this.currentStock) {
        this.selectedQuantity = this.currentStock;
          uni.showToast({
            title: '超出库存数量',
            icon: 'none'
          });
      }
      // 取消自动执行操作
      // this.checkAndExecuteAction();
    },



    // 获取规格组合的key
    getSpecKey() {
      const specs = this.productDetail.specs || [];
      const selectedValues = [];

      for (const spec of specs) {
        if (this.selectedSpecs[spec.name]) {
          selectedValues.push(this.selectedSpecs[spec.name]);
        } else {
          return null; // 如果有规格未选择，返回null
        }
      }

      return selectedValues.join('-');
    },

    // 获取已选规格文本
    getSelectedSpecText() {
      const specs = this.productDetail.specs || [];
      const selectedTexts = [];

      for (const spec of specs) {
        if (this.selectedSpecs[spec.name]) {
          selectedTexts.push(`${spec.name}:${this.selectedSpecs[spec.name]}`);
        }
      }

      return selectedTexts.join(' ');
    },

    // 加载购物车数量
    loadCartCount() {
      const cartItems = uni.getStorageSync('cartItems') || [];
      this.cartCount = cartItems.reduce((total, item) => total + item.quantity, 0);
    },

    // 验证选择
    validateSelection() {
      // 检查是否选择了所有必需的规格
      for (const spec of this.productDetail.specs) {
        if (!this.selectedSpecs[spec.name]) {
          uni.showToast({
            title: `请选择${spec.name}`,
            icon: 'none'
          });
          return false;
        }
      }

      // 检查数量
      if (this.selectedQuantity < 1 || this.selectedQuantity > this.productDetail.stock) {
        uni.showToast({
          title: '请选择正确的数量',
          icon: 'none'
        });
        return false;
      }

      return true;
    },

    // 处理加入购物车按钮点击
    handleAddToCart() {
      if (!this.validateSelection()) {
        return;
      }

      this.closeSpecPopup();
      this.executeAddToCart();
    },

    // 处理立即购买按钮点击
    handleBuyNow() {
      if (!this.validateSelection()) {
        return;
      }

      this.closeSpecPopup();
      this.executeBuyNow();
    },

    // 执行加入购物车
    executeAddToCart() {
      // 检查登录状态
      if (!this.checkLoginStatus()) {
        return;
      }

      const cartItems = uni.getStorageSync('cartItems') || [];
      const productInfo = {
        id: this.productDetail.id,
        name: this.productDetail.name,
        price: this.currentPrice,
        image: this.productDetail.images[0],
        specs: { ...this.selectedSpecs },
        quantity: this.selectedQuantity,
        selected: true,
        specText: this.getSelectedSpecText()
      };

      // 查找是否已存在相同规格的商品
      const existIndex = cartItems.findIndex(item =>
        item.id === productInfo.id &&
        JSON.stringify(item.specs) === JSON.stringify(productInfo.specs)
      );

      if (existIndex > -1) {
        // 检查库存
        const newQuantity = cartItems[existIndex].quantity + this.selectedQuantity;
        const currentStock = this.currentStock;
        if (newQuantity > currentStock) {
          uni.showToast({
            title: '超出库存数量',
            icon: 'none'
          });
          return;
        }
        cartItems[existIndex].quantity = newQuantity;
      } else {
        cartItems.push(productInfo);
      }

      uni.setStorageSync('cartItems', cartItems);
      this.loadCartCount();

      uni.showToast({
        title: '已添加到购物车',
        icon: 'success'
      });
    },

    // 执行立即购买
    executeBuyNow() {
      // 检查登录状态
      if (!this.checkLoginStatus()) {
        return;
      }

      // 生成订单数据
      const orderData = {
        id: Date.now().toString(),
        createTime: new Date().toLocaleString(),
        status: 'pending',
        statusText: '待付款',
        products: [{
          id: this.productDetail.id,
          name: this.productDetail.name,
          price: this.currentPrice,
          image: this.productDetail.images[0],
          specs: { ...this.selectedSpecs },
          quantity: this.selectedQuantity,
          specText: this.getSelectedSpecText()
        }],
        totalAmount: (parseFloat(this.currentPrice) * this.selectedQuantity).toFixed(2),
        paymentMethod: '',
        address: {}
      };

      // 保存订单到本地存储
      const orders = uni.getStorageSync('orders') || [];
      orders.unshift(orderData);
      uni.setStorageSync('orders', orders);

      // 跳转到待付款订单页面
      uni.navigateTo({
        url: '/pages/shopping/order/order?status=1'
      });
    },

    // 加载收藏状态
    loadFavoriteStatus() {
      const favoriteItems = uni.getStorageSync('favoriteItems') || [];
      // 使用字符串比较确保ID匹配
      const productId = String(this.productId);
      this.isFavorited = favoriteItems.some(item => String(item.id) === productId);
      console.log('加载收藏状态:', productId, this.isFavorited);
    },

    // 切换收藏状态
    toggleFavorite() {
      const favoriteItems = uni.getStorageSync('favoriteItems') || [];
      const productId = String(this.productId);
      const existIndex = favoriteItems.findIndex(item => String(item.id) === productId);

      if (existIndex > -1) {
        // 取消收藏
        favoriteItems.splice(existIndex, 1);
        this.isFavorited = false;
        uni.showToast({
          title: '已取消收藏',
          icon: 'success'
        });
        console.log('取消收藏:', productId);
      } else {
        // 检查商品信息是否完整
        if (!this.productDetail.name) {
          uni.showToast({
            title: '商品信息加载中，请稍后',
            icon: 'none'
          });
          return;
        }

        // 添加收藏
        const favoriteItem = {
          id: productId,
          name: this.productDetail.name,
          price: this.currentPrice, // 使用当前价格
          originalPrice: this.productDetail.originalPrice,
          image: this.productDetail.images && this.productDetail.images[0],
          isPromotion: this.productDetail.isPromotion,
          freeShipping: this.productDetail.freeShipping,
          sales: this.productDetail.sales,
          stock: this.currentStock, // 使用当前库存
          addTime: new Date().getTime()
        };

        favoriteItems.push(favoriteItem);
        this.isFavorited = true;
        uni.showToast({
          title: '已添加收藏',
          icon: 'success'
        });
        console.log('添加收藏:', productId, favoriteItem);
      }

      // 保存到本地存储
      uni.setStorageSync('favoriteItems', favoriteItems);

      // 重新加载收藏状态确保一致性
      this.$nextTick(() => {
        this.loadFavoriteStatus();
      });
    },

    // 跳转到收藏页面
    goToFavorite() {
      uni.navigateTo({
        url: '/pages/shopping/favorite/favorite'
      });
    },

    // 跳转到购物车
    goToCart() {
      uni.navigateTo({
        url: '/pages/shopping/cart/cart'
      });
    },

    // 检查登录状态
    checkLoginStatus() {
      const token = this.$store.getters.token;
      if (!token) {
        uni.showModal({
          title: '提示',
          content: '您还未登录，请先登录。',
          confirmText: '去登录',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              uni.redirectTo({
                url: '/pages/login'
              });
            }
          }
        });
        return false;
      }
      return true;
    },

    // 加载商品评价数据
    loadProductReviews() {
      const productReviews = uni.getStorageSync('productReviews') || {};
      const reviews = productReviews[this.productId] || [];

      // 更新商品详情中的评价数据
      this.productDetail.reviews = reviews;
      this.productDetail.reviewCount = reviews.length;

      // 计算平均评分
      if (reviews.length > 0) {
        const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0);
        this.productDetail.rating = (totalRating / reviews.length).toFixed(1);
      } else {
        this.productDetail.rating = '5.0';
      }
    },

    // 弹窗状态变化监听
    onPopupChange(e) {
      if (e.show === false) {
        // 弹窗关闭时显示底部导航栏
        this.showBottomNav = true;
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/static/fontA/iconfont.css';

.shop-detail-container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 120rpx; // 为底部导航留出空间
}

// 图片区域
.image-section {
  position: relative;
  height: 750rpx;
  background: #fff;

  .product-swiper {
    width: 100%;
    height: 100%;

    .product-image {
      width: 100%;
      height: 100%;
    }
  }

  .image-indicator {
    position: absolute;
    bottom: 20rpx;
    right: 20rpx;
    background: rgba(0, 0, 0, 0.5);
    color: #fff;
    padding: 8rpx 16rpx;
    border-radius: 20rpx;
    font-size: 24rpx;
  }

  .back-btn, .share-btn {
    position: absolute;
    top: 60rpx;
    width: 60rpx;
    height: 60rpx;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  }

  .back-btn {
    left: 30rpx;
  }

  .share-btn {
    right: 30rpx;
  }
}

// 商品信息区域
.product-info-section {
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;

  .price-section {
    display: flex;
    align-items: baseline;
    margin-bottom: 20rpx;

    .current-price {
      font-size: 48rpx;
      color: #ff5555;
      font-weight: bold;
      margin-right: 20rpx;
    }

    .original-price {
      font-size: 28rpx;
      color: #999;
      text-decoration: line-through;
      margin-right: 20rpx;
    }

    .promotion-tag {
      background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
      color: #fff;
      padding: 6rpx 16rpx;
      border-radius: 20rpx;
      font-size: 22rpx;
    }
  }

  .product-title {
    font-size: 32rpx;
    color: #333;
    line-height: 1.5;
    margin-bottom: 20rpx;
    font-weight: 500;
  }

  .product-tags {
    margin-bottom: 30rpx;

    .tag {
      display: inline-block;
      padding: 8rpx 16rpx;
      background: rgba(62, 198, 198, 0.1);
      color: #3ec6c6;
      font-size: 22rpx;
      border-radius: 20rpx;
      margin-right: 16rpx;
      margin-bottom: 10rpx;

      &.guarantee-tag {
        background: rgba(76, 175, 80, 0.1);
        color: #4caf50;
      }

      &.service-tag {
        background: rgba(255, 152, 0, 0.1);
        color: #ff9800;
      }
    }
  }

  .sales-info {
    display: flex;
    justify-content: space-between;

    .sales-item {
      text-align: center;

      .label {
        display: block;
        font-size: 24rpx;
        color: #999;
        margin-bottom: 8rpx;
      }

      .value {
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
      }
    }
  }
}

// 详情评价区域
.detail-section {
  background: #fff;

  .section-tabs {
    display: flex;
    border-bottom: 1rpx solid #eee;

    .tab-item {
      flex: 1;
      height: 88rpx;
      line-height: 88rpx;
      text-align: center;
      font-size: 28rpx;
      color: #666;
      position: relative;

      &.active {
        color: #3ec6c6;
        font-weight: 500;

        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 60rpx;
          height: 4rpx;
          background: #3ec6c6;
          border-radius: 2rpx;
        }
      }
    }
  }

  .tab-content {
    padding: 30rpx;

    .detail-content {
      .detail-image {
        width: 100%;
        margin-bottom: 20rpx;
        border-radius: 12rpx;
      }

      .detail-text {
        font-size: 28rpx;
        color: #666;
        line-height: 1.6;
      }
    }

    .reviews-summary {
      display: flex;
      align-items: center;
      padding: 30rpx 0;
      border-bottom: 1rpx solid #eee;
      margin-bottom: 30rpx;

      .rating-overview {
        margin-right: 40rpx;

        .rating-score {
          font-size: 48rpx;
          color: #ff9900;
          font-weight: bold;
          text-align: center;
          margin-bottom: 10rpx;
        }

        .rating-stars {
          text-align: center;
        }
      }

      .rating-detail {
        font-size: 28rpx;
        color: #666;
      }
    }

    .reviews-list {
      .review-item {
        padding: 30rpx 0;
        border-bottom: 1rpx solid #f5f5f5;

        &:last-child {
          border-bottom: none;
        }

        .review-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16rpx;

          .user-info {
            display: flex;
            align-items: center;

            .user-avatar {
              width: 60rpx;
              height: 60rpx;
              border-radius: 50%;
              margin-right: 20rpx;
            }

            .username {
              font-size: 28rpx;
              color: #333;
            }
          }

          .review-date {
            font-size: 24rpx;
            color: #999;
          }
        }

        .review-rating {
          display: flex;
          align-items: center;
          margin-bottom: 16rpx;

          .rating-text {
            margin-left: 16rpx;
            font-size: 24rpx;
            color: #ff9900;
          }
        }

        .review-content {
          font-size: 28rpx;
          color: #666;
          line-height: 1.5;
        }
      }
    }
  }
}

// 规格选择弹窗
.spec-popup-container {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;
  overflow: hidden;
  position: relative;
  z-index: 1000;
}

.spec-popup {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;

  .popup-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }

  .close-btn {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.popup-content {
  flex: 1;
  padding: 0 30rpx;
  max-height: 60vh;
}

.spec-info {
  display: flex;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
  margin-bottom: 30rpx;

  .spec-image {
    width: 120rpx;
    height: 120rpx;
    border-radius: 12rpx;
    margin-right: 20rpx;
  }

  .spec-details {
    flex: 1;

    .spec-price {
      margin-bottom: 8rpx;

      .total-price {
        font-size: 36rpx;
        color: #ff5555;
        font-weight: bold;
        line-height: 1.2;
      }

      .unit-price {
        font-size: 24rpx;
        color: #999;
        margin-top: 4rpx;
      }
    }

    .spec-stock {
      font-size: 24rpx;
      color: #999;
      margin-bottom: 8rpx;
    }

    .spec-text {
      font-size: 24rpx;
      color: #666;
    }
  }
}

.spec-options {
  margin-bottom: 30rpx;

  .spec-group {
    margin-bottom: 30rpx;

    .spec-name {
      font-size: 28rpx;
      color: #333;
      margin-bottom: 20rpx;
      font-weight: 500;
    }

    .spec-values {
      display: flex;
      flex-wrap: wrap;
      gap: 20rpx;

      .spec-value {
        padding: 16rpx 24rpx;
        border: 2rpx solid #e5e5e5;
        border-radius: 8rpx;
        font-size: 26rpx;
        color: #666;
        background: #fff;
        min-width: 80rpx;
        text-align: center;

        &.active {
          border-color: #3ec6c6;
          color: #3ec6c6;
          background: #f0fffe;
        }

        &.disabled {
          border-color: #f0f0f0;
          color: #ccc;
          background: #f8f8f8;
          pointer-events: none;
        }
      }
    }
  }
}

.quantity-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;

  .quantity-label {
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
  }

  .quantity-control {
    display: flex;
    align-items: center;
    border: 2rpx solid #e5e5e5;
    border-radius: 8rpx;
    overflow: hidden;

    .quantity-btn {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f8f8f8;
      font-size: 32rpx;
      color: #666;
      border: none;

      &:active {
        background: #e5e5e5;
      }
    }

    .quantity-input {
      width: 80rpx;
      height: 60rpx;
      text-align: center;
      font-size: 28rpx;
      border: none;
      background: #fff;
    }
  }
}

.popup-actions {
  display: flex;
  padding: 30rpx;
  gap: 20rpx;
  background: #fff;
  border-top: 1rpx solid #f5f5f5;

  .popup-btn {
    flex: 1;
    height: 88rpx;
    line-height: 88rpx;
    text-align: center;
    font-size: 32rpx;
    border-radius: 44rpx;
    font-weight: 500;

    &.single-btn {
      background: #3ec6c6;
      color: #fff;
    }

    &.left-btn {
      background: #e6f7f7;
      color: #3ec6c6;
      border: 2rpx solid #3ec6c6;
    }

    &.right-btn {
      background: #3ec6c6;
      color: #fff;
    }
  }

  .popup-btn-group {
    display: flex;
    gap: 20rpx;
    width: 100%;
  }
}

// 底部导航
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background: #fff;
  border-top: 1rpx solid #eee;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  z-index: 999;

  .nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 120rpx;
    position: relative;

    text {
      font-size: 20rpx;
      color: #666;
      margin-top: 8rpx;
    }

    .cart-badge {
      position: absolute;
      top: -8rpx;
      right: 20rpx;
      background: #ff5555;
      color: #fff;
      font-size: 18rpx;
      min-width: 32rpx;
      height: 32rpx;
      line-height: 32rpx;
      text-align: center;
      border-radius: 16rpx;
      padding: 0 8rpx;
    }
  }

  .action-buttons {
    flex: 1;
    display: flex;
    margin-left: 40rpx;
    gap: 20rpx;

    .action-btn {
      flex: 1;
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      font-size: 28rpx;
      border-radius: 40rpx;

      &.add-cart-btn {
        background: rgba(62, 198, 198, 0.1);
        color: #3ec6c6;
        border: 2rpx solid #3ec6c6;
      }

      &.buy-now-btn {
        background: #3ec6c6;
        color: #fff;
      }
    }
  }
}

// 规格加载状态样式
.spec-loading {
  padding: 40rpx 0;
  text-align: center;

  .loading-text {
    color: #999;
    font-size: 26rpx;
  }
}

.no-specs {
  padding: 40rpx 0;
  text-align: center;

  .no-specs-text {
    color: #999;
    font-size: 26rpx;
  }
}
</style>