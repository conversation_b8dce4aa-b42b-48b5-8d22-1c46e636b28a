<template>
  <view class="cart-container">
    <!-- 购物车为空时显示 -->
    <view class="empty-cart" v-if="cartList.length === 0">
      <view class="iconfontA icon-gouwuche"></view>
      <text class="empty-text">购物车还是空的</text>
      <button class="go-shopping-btn" @click="goShopping">去逛逛</button>
    </view>

    <!-- 购物车有商品时显示 -->
    <view class="cart-content" v-else>
      <!-- 商品列表 -->
      <scroll-view scroll-y class="cart-list">
        <view class="cart-item" v-for="(item, index) in cartList" :key="index"
          @touchstart="touchStart"
          @touchmove="touchMove($event, index)"
          @touchend="touchEnd"
          :style="{ transform: `translateX(${item.offsetX || 0}px)` }"
        >
          <view class="item-content">
            <view class="checkbox-wrapper" @tap="toggleSelect(index)">
              <view class="custom-checkbox" :class="{ 'checked': item.selected }">
                <uni-icons v-if="item.selected" type="checkmarkempty" size="12" color="#fff"></uni-icons>
              </view>
            </view>
            <image :src="item.image" mode="aspectFill" class="product-image" @error="handleImageError"></image>
            <view class="product-info">
              <text class="product-name">{{ item.name }}</text>
              <view class="spec-info" v-if="item.specText" @click.stop="showSpecPopup(index)">
                <text class="spec-text">{{ item.specText }}</text>
                <uni-icons type="right" size="12" color="#999"></uni-icons>
              </view>
              <view class="price-quantity">
                <text class="price">¥{{ item.price }}</text>
                <view class="quantity-control">
                  <view
                    class="quantity-btn minus"
                    :class="{ disabled: item.quantity <= 1 }"
                    @click="updateQuantity(index, -1)"
                  >-</view>
                  <input
                    type="number"
                    v-model="item.quantity"
                    class="quantity-input"
                    @blur="validateQuantity(index)"
                  />
                  <view
                    class="quantity-btn plus"
                    @click="updateQuantity(index, 1)"
                  >+</view>
                </view>
              </view>
            </view>
          </view>
          <view class="delete-btn" @click="removeItem(index)">删除</view>
        </view>
      </scroll-view>

      <!-- 底部结算栏 -->
      <view class="settlement-bar" v-if="showBottomBar">
        <view class="select-all" @tap="toggleSelectAll">
          <view class="custom-checkbox" :class="{ 'checked': isAllSelected }">
            <uni-icons v-if="isAllSelected" type="checkmarkempty" size="12" color="#fff"></uni-icons>
          </view>
          <text>全选</text>
        </view>
        <view class="total-info">
          <text>合计:</text>
          <text class="total-price">¥{{ totalPrice }}</text>
        </view>
        <button 
          class="checkout-btn" 
          :disabled="selectedCount === 0"
          @click="handleCheckout"
        >
          结算({{ selectedCount }})
        </button>
      </view>
    </view>

    <!-- 规格选择弹窗 -->
    <uni-popup ref="specPopup" type="bottom" background-color="#fff" :safe-area="true" @change="onPopupChange">
      <view class="spec-popup-container" v-if="currentEditItem">
        <view class="spec-popup">
          <view class="popup-header">
            <view class="popup-title">选择规格</view>
            <view class="close-btn" @click="closeSpecPopup">
              <uni-icons type="close" size="20" color="#999"></uni-icons>
            </view>
          </view>

          <scroll-view class="popup-content" scroll-y="true">
            <view class="spec-info">
              <image :src="currentEditItem.image" class="spec-image" />
              <view class="spec-details">
                <view class="spec-price">
                  <view class="total-price">¥{{ getTotalPrice() }}</view>
                  <view class="unit-price" v-if="tempQuantity > 1">单价：¥{{ getCurrentSpecPrice() }}</view>
                </view>
                <view class="spec-stock">库存{{ getCurrentStock() }}件</view>
                <view class="spec-text" v-if="getSelectedSpecText()">已选：{{ getSelectedSpecText() }}</view>
              </view>
            </view>

            <view class="spec-options">

              <!-- 规格加载状态 -->
              <view v-if="isLoadingSpecs" class="spec-loading">
                <view class="loading-text">正在加载规格信息...</view>
              </view>

              <!-- 规格选项 -->
              <view v-else-if="productSpecs && productSpecs.length > 0" class="spec-group" v-for="spec in productSpecs" :key="spec.name">
                <view class="spec-name">{{ spec.name }}</view>
                <view class="spec-values">
                  <view
                    class="spec-value"
                    :class="{
                      active: tempSelectedSpecs[spec.name] === value,
                      disabled: !isSpecValueAvailable(spec.name, value)
                    }"
                    v-for="value in spec.values"
                    :key="value"
                    @click="selectTempSpec(spec.name, value)"
                    v-if="isSpecValueAvailable(spec.name, value)"
                  >
                    {{ value }}
                  </view>
                </view>
              </view>

              <!-- 无规格提示 -->
              <view v-else class="no-specs">
                <view class="no-specs-text">该商品暂无规格选项</view>
              </view>
            </view>

            <view class="quantity-section">
              <view class="quantity-label">数量</view>
              <view class="quantity-control">
                <view
                  class="quantity-btn minus"
                  :class="{ disabled: tempQuantity <= 1 }"
                  @click="updateTempQuantity(-1)"
                >-</view>
                <input
                  type="number"
                  v-model="tempQuantity"
                  class="quantity-input"
                  @blur="validateTempQuantity"
                />
                <view
                  class="quantity-btn plus"
                  @click="updateTempQuantity(1)"
                >+</view>
              </view>
            </view>
          </scroll-view>

          <view class="popup-actions">
            <view class="action-btn confirm-btn" @click="confirmSpecChange">确定</view>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import {  getProductSpec } from '@/api/system/shop'

export default {
  
  data() {
    return {
      cartList: [], // 购物车商品列表
      isAllSelected: false, // 是否全选
      startX: 0,
      deleteWidth: 80, // 删除按钮宽度
      currentEditItem: null, // 当前编辑的商品
      currentEditIndex: -1, // 当前编辑的商品索引
      tempSelectedSpecs: {}, // 临时选择的规格
      tempQuantity: 1, // 临时选择的数量
      showBottomBar: true, // 控制底部结算栏显示/隐藏
      isLoadingSpecs: false, // 是否正在加载规格
      productSpecs: [], // 商品规格数据
      skuData: [] // SKU数据
    }
  },
  computed: {
    // 选中商品数量
    selectedCount() {
      return this.cartList.filter(item => item.selected).length
    },
    // 总价格
    totalPrice() {
      return this.cartList
        .filter(item => item.selected)
        .reduce((total, item) => {
          return total + (parseFloat(item.price) * item.quantity)
        }, 0)
        .toFixed(2)
    }
  },
  onLoad() {
    // 初始化时确保底部导航栏显示
    this.showBottomBar = true
  },
  onShow() {
    this.loadCartData()
    // 确保底部导航栏显示
    this.showBottomBar = true
  },
  onHide() {
    // 页面隐藏时重置弹窗状态
    this.showBottomBar = true
    this.currentEditItem = null
    this.currentEditIndex = -1
    this.tempSelectedSpecs = {}
  },
  methods: {
    // 加载购物车数据
    loadCartData() {
      const cartData = uni.getStorageSync('cartItems') || []
      this.cartList = cartData.map(item => ({
        ...item,
        selected: false // 添加选中状态
      }))
      this.checkSelectAll()
    },
    // 保存购物车数据
    saveCartData() {
      const cartData = this.cartList.map(({ selected, ...item }) => item)
      uni.setStorageSync('cartItems', cartData)
    },
    // 切换商品选中状态
    toggleSelect(index) {
      const item = this.cartList[index];

      // 如果商品显示"请选择规格"，点击勾选框时弹出规格选择对话框
      if (item.specText === '请选择规格') {
        this.showSpecPopup(index);
        return;
      }

      // 正常的选中/取消选中逻辑
      this.cartList[index].selected = !this.cartList[index].selected
      this.checkSelectAll()
    },
    // 切换全选状态
    toggleSelectAll() {
      this.isAllSelected = !this.isAllSelected
      this.cartList.forEach(item => {
        item.selected = this.isAllSelected
      })
    },
    // 检查是否全选
    checkSelectAll() {
      this.isAllSelected = this.cartList.length > 0 && 
        this.cartList.every(item => item.selected)
    },
    // 更新商品数量
    updateQuantity(index, change) {
      const newQuantity = this.cartList[index].quantity + change
      if (newQuantity < 1) return
      this.cartList[index].quantity = newQuantity
      this.saveCartData()
    },
    // 验证输入的数量
    validateQuantity(index) {
      let quantity = parseInt(this.cartList[index].quantity)
      if (isNaN(quantity) || quantity < 1) {
        quantity = 1
      }
      this.cartList[index].quantity = quantity
      this.saveCartData()
    },
    // 去购物
    goShopping() {
      uni.switchTab({
        url: '/pages/shop/index'
      })
    },
    // 结算按钮点击事件
    handleCheckout() {
      const selectedItems = this.cartList.filter(item => item.selected)
      if (selectedItems.length === 0) {
        uni.showToast({
          title: '请选择要结算的商品',
          icon: 'none'
        })
        return
      }

      // 生成订单数据
      const orderData = {
        id: Date.now().toString(),
        createTime: new Date().toLocaleString(),
        status: 'pending',
        statusText: '待付款',
        statusClass: 'status-pending',
        products: selectedItems.map(item => ({
          id: item.id,
          name: item.name,
          image: item.image,
          price: Number(item.price),
          count: Number(item.quantity),
          specification: item.specText || '' // 添加规格信息
        })),
        totalCount: selectedItems.reduce((sum, item) => sum + Number(item.quantity), 0),
        totalPrice: selectedItems.reduce((sum, item) => sum + Number(item.price) * Number(item.quantity), 0).toFixed(2)
      }

      // 保存订单数据到本地存储
      const orders = uni.getStorageSync('orders') || []
      orders.unshift(orderData)
      uni.setStorageSync('orders', orders)

      // 从购物车中移除已结算的商品
      this.cartList = this.cartList.filter(item => !item.selected)
      // 更新本地存储的购物车数据
      uni.setStorageSync('cartItems', this.cartList)

      // 跳转到待付款订单页面
      uni.navigateTo({
        url: '/pages/shopping/order/order?status=1'
      })
    },
    // 图片加载失败处理
    handleImageError(e) {
      const index = this.cartList.findIndex(item => item.image === e.target.src)
      if (index !== -1) {
        this.cartList[index].image = '/static/images/default-product.png'
      }
    },
    // 触摸开始
    touchStart(e) {
      this.startX = e.touches[0].clientX
    },

    // 触摸移动
    touchMove(e, index) {
      let moveX = e.touches[0].clientX - this.startX
      let offsetX = 0

      // 限制只能向左滑动
      if (moveX < 0) {
        offsetX = Math.max(-this.deleteWidth, moveX)
      }

      // 更新当前项的偏移量
      this.$set(this.cartList[index], 'offsetX', offsetX)
    },

    // 触摸结束
    touchEnd(e) {
      this.startX = 0
    },

    // 删除商品
    removeItem(index) {
      uni.showModal({
        title: '提示',
        content: '确定要删除这个商品吗？',
        success: res => {
          if (res.confirm) {
            this.cartList.splice(index, 1)
            this.saveCartData()
            this.checkSelectAll()
          }
        }
      })
    },

    // 显示规格选择弹窗
    showSpecPopup(index) {
      console.log('🛒 购物车 - 点击规格信息，显示规格弹窗，索引:', index);

      if (!this.cartList[index]) {
        console.error('🛒 购物车 - 商品不存在，索引:', index);
        return;
      }

      this.currentEditItem = this.cartList[index];
      this.currentEditIndex = index;
      this.tempSelectedSpecs = { ...this.currentEditItem.specs };
      this.tempQuantity = this.currentEditItem.quantity || 1;
      this.showBottomBar = false; // 隐藏底部结算栏

      console.log('🛒 购物车 - 当前编辑商品:', this.currentEditItem);
      console.log('🛒 购物车 - 商品ID:', this.currentEditItem.id);
      console.log('🛒 购物车 - 商品名称:', this.currentEditItem.name);
      console.log('🛒 购物车 - 当前规格文本:', this.currentEditItem.specText);
      console.log('🛒 购物车 - 初始选择规格:', this.tempSelectedSpecs);

      // 重置规格数据
      this.productSpecs = [];
      this.skuData = [];

      // 加载商品规格数据
      this.loadProductSpecs(this.currentEditItem.id);

      console.log('🛒 购物车 - 准备打开弹窗');
      this.$refs.specPopup.open();
    },

    // 关闭规格选择弹窗
    closeSpecPopup() {
      this.showBottomBar = true; // 显示底部结算栏
      this.$refs.specPopup.close();
      this.currentEditItem = null;
      this.currentEditIndex = -1;
      this.tempSelectedSpecs = {};
    },

    // 弹窗状态变化监听
    onPopupChange(e) {
      if (e.show === false) {
        // 弹窗关闭时确保显示底部结算栏
        this.showBottomBar = true;
        // 清理编辑状态
        this.currentEditItem = null;
        this.currentEditIndex = -1;
        this.tempSelectedSpecs = {};

        // 强制刷新视图
        this.$nextTick(() => {
          this.showBottomBar = true;
        });
      }
    },

    // 加载商品规格数据
    async loadProductSpecs(productId) {
      try {
        console.log('🛒 购物车 - 开始加载规格数据，商品ID:', productId);
        this.isLoadingSpecs = true;

        // 调用API获取规格数据
        const response = await getProductSpec(productId);
        let skuRows = null;
        if (response && response.code === 200) {
        if (response.rows) {
            skuRows = response.rows;
          } 

        }

        if (skuRows && (Array.isArray(skuRows) ? skuRows.length > 0 : true)) {
          // 保存原始SKU数据
          this.skuData = Array.isArray(skuRows) ? skuRows : [skuRows];

          // 处理规格数据
          this.productSpecs = this.processSpecData(skuRows);

          // 强制更新视图
          this.$forceUpdate();
        } else {
          console.warn('🛒 购物车 - 规格数据格式异常或为空:', response);
          this.productSpecs = [];
          this.skuData = [];
        }
      } catch (error) {
        console.error('🛒 购物车 - 加载商品规格失败:', error);
        this.productSpecs = [];
        this.skuData = [];
      } finally {
        this.isLoadingSpecs = false;
      }
    },



    // 处理规格数据（与商品详情页面保持一致）
    processSpecData(data) {
      try {
        if (!data) {
          console.log('规格数据为空');
          return [];
        }
        console.log('处理规格数据:', data);

        // 如果是单个对象，包装成数组
        const specArray = Array.isArray(data) ? data : [data];

        // 提取所有可能的规格属性
        const specFields = new Set();
        specArray.forEach(item => {
          // 收集所有规格相关的字段
          Object.keys(item).forEach(key => {
            if (['dosage_form', 'dose', 'flavor', 'specification', 'size', 'color', 'style'].includes(key)) {
              specFields.add(key);
            }
          });
        });
        // 为每个规格字段创建规格选项
        const processedSpecs = [];

        specFields.forEach(field => {
          // 收集该字段的所有唯一值
          const values = new Set();
          specArray.forEach(item => {
            if (item[field] && item[field] !== '') {
              values.add(item[field]);
            }
          });

          if (values.size > 0) {
            // 字段名映射
            const fieldNameMap = {
              'dosage_form': '剂型',
              'dose': '剂量',
              'flavor': '口味',
              'specification': '规格',
              'size': '尺寸',
              'color': '颜色',
              'style': '款式'
            };

            processedSpecs.push({
              name: fieldNameMap[field] || field,
              field: field, // 保存原始字段名，用于匹配
              values: Array.from(values)
            });
          }
        });

        console.log('处理后的规格数据:', processedSpecs);

        // 如果没有找到标准规格字段，尝试传统方式
        if (processedSpecs.length === 0) {
          return this.processTraditionalSpecData(specArray);
        }

        return processedSpecs;
      } catch (error) {
        console.error('❌ 处理规格数据失败:', error, data);
        return [];
      }
    },

    // 处理传统格式的规格数据
    processTraditionalSpecData(specArray) {
      return specArray.map(spec => {
        // 处理不同的规格数据格式
        const specName = spec.name || spec.specName || spec.spec_name || '规格';
        let specValues = spec.values || spec.options || spec.spec_values || [];

        // 如果values是字符串，尝试解析
        if (typeof specValues === 'string') {
          try {
            specValues = JSON.parse(specValues);
          } catch (e) {
            // 如果解析失败，按逗号分割
            specValues = specValues.split(',').map(v => v.trim()).filter(v => v);
          }
        }

        // 确保values是数组
        if (!Array.isArray(specValues)) {
          specValues = [];
        }

        return {
          name: specName,
          values: specValues
        };
      }).filter(spec => spec.values.length > 0); // 过滤掉没有选项的规格
    },

    // 提取规格选项（已整合到processSpecData中）
    extractSpecOptions(skuData) {
      // 这个方法现在只是返回已处理的规格数据
      return this.processSpecData(skuData);
    },

    // 选择临时规格
    selectTempSpec(specName, value) {

      if (!this.isSpecValueAvailable(specName, value)) {
        console.log('🛒 购物车 - 规格值不可用:', specName, value);
        return;
      }

      this.tempSelectedSpecs[specName] = value;
      console.log('🛒 购物车 - 当前选择的规格:', this.tempSelectedSpecs);

      // 获取匹配的SKU信息
      const matchedSKU = this.getCurrentSKU();
   
      if (matchedSKU) {
       

        // 手动触发价格和库存的更新
        this.$nextTick(() => {
         
        });
      } else {
        console.log('🛒 购物车 - 未找到匹配的SKU');
      }

      this.$forceUpdate();
    },

    // 检查规格值是否可用
    isSpecValueAvailable(specName, value) {
      try {
        if (!this.skuData || !Array.isArray(this.skuData)) {
          return true;
        }

        const spec = this.productSpecs.find(s => s.name === specName);
        if (!spec || !spec.field) {
          return true;
        }

        const otherSelectedSpecs = {};
        Object.entries(this.tempSelectedSpecs).forEach(([key, val]) => {
          if (key !== specName) {
            otherSelectedSpecs[key] = val;
          }
        });

        const hasMatchingSKU = this.skuData.some(sku => {
          if (sku[spec.field] !== value) {
            return false;
          }

          return Object.entries(otherSelectedSpecs).every(([selectedSpecName, selectedValue]) => {
            const selectedSpec = this.productSpecs.find(s => s.name === selectedSpecName);
            if (!selectedSpec || !selectedSpec.field) return true;

            return sku[selectedSpec.field] === selectedValue;
          });
        });

        return hasMatchingSKU;
      } catch (error) {
        console.error('检查规格值可用性失败:', error);
        return true;
      }
    },

    // 获取当前选择的SKU
    getCurrentSKU() {
      try {
       
        if (!this.skuData || !Array.isArray(this.skuData)) {
          return null;
        }

        if (Object.keys(this.tempSelectedSpecs).length === 0) {
          return this.skuData[0];
        }

        const matchedSKU = this.skuData.find(sku => {

          const isMatch = Object.entries(this.tempSelectedSpecs).every(([specName, selectedValue]) => {
            const spec = this.productSpecs.find(s => s.name === specName);

            if (!spec || !spec.field) {
              return false;
            }

            const skuValue = sku[spec.field];
            const match = skuValue === selectedValue;

            return match;
          });
          return isMatch;
        });
        return matchedSKU;
      } catch (error) {
        console.error('🛒 购物车 - 获取SKU失败:', error);
        return null;
      }
    },

    // 获取当前库存
    getCurrentStock() {
      const sku = this.getCurrentSKU();
      if (sku && sku.stock !== undefined) {
        return sku.stock;
      }
      const defaultStock = 999;
      return defaultStock;
    },

    // 获取选择的规格文本
    getSelectedSpecText() {
      const specTexts = [];
      for (const [key, value] of Object.entries(this.tempSelectedSpecs)) {
        specTexts.push(`${key}:${value}`);
      }
      return specTexts.join(' ');
    },

    // 更新临时数量
    updateTempQuantity(change) {
      const newQuantity = this.tempQuantity + change;
      if (newQuantity < 1) return;

      const maxStock = this.getCurrentStock();
      if (newQuantity > maxStock) {
        uni.showToast({
          title: `库存不足，最多${maxStock}件`,
          icon: 'none'
        });
        return;
      }

      this.tempQuantity = newQuantity;
    },

    // 验证临时数量
    validateTempQuantity() {
      let quantity = parseInt(this.tempQuantity);
      if (isNaN(quantity) || quantity < 1) {
        quantity = 1;
      }

      const maxStock = this.getCurrentStock();
      if (quantity > maxStock) {
        quantity = maxStock;
        uni.showToast({
          title: `库存不足，最多${maxStock}件`,
          icon: 'none'
        });
      }

      this.tempQuantity = quantity;
    },

    // 获取总价格
    getTotalPrice() {
      const unitPrice = parseFloat(this.getCurrentSpecPrice()) || 0;
      const quantity = this.tempQuantity || 1;
      const total = (unitPrice * quantity).toFixed(2);
      return total;
    },

    // 获取当前选择规格的价格
    getCurrentSpecPrice() {
      const sku = this.getCurrentSKU();
     
      if (sku && sku.price) {
        return sku.price;
      }
      const defaultPrice = this.currentEditItem ? this.currentEditItem.price : '0.00';
      console.log('🛒 购物车 - 使用默认价格:', defaultPrice);
      return defaultPrice;
    },

    // 确认规格更改
    confirmSpecChange() {
      if (!this.currentEditItem) return;

      // 检查是否选择了所有必需的规格
      const requiredSpecs = this.productSpecs.length;
      const selectedSpecs = Object.keys(this.tempSelectedSpecs).length;

      if (requiredSpecs > 0 && selectedSpecs < requiredSpecs) {
        uni.showToast({
          title: '请选择完整的商品规格',
          icon: 'none'
        });
        return;
      }

      // 检查库存
      const currentStock = this.getCurrentStock();
      if (this.tempQuantity > currentStock) {
        uni.showToast({
          title: `库存不足，最多${currentStock}件`,
          icon: 'none'
        });
        return;
      }

      // 更新商品规格
      this.cartList[this.currentEditIndex].specs = { ...this.tempSelectedSpecs };

      // 更新规格文本
      this.cartList[this.currentEditIndex].specText = this.getSelectedSpecText();

      // 更新价格和数量
      this.cartList[this.currentEditIndex].price = this.getCurrentSpecPrice();
      this.cartList[this.currentEditIndex].quantity = this.tempQuantity;

      // 选择规格后自动勾选商品
      this.cartList[this.currentEditIndex].selected = true;

      this.saveCartData();
      this.checkSelectAll(); // 检查全选状态
      this.closeSpecPopup();

      uni.showToast({
        title: '规格已更新',
        icon: 'success'
      });
    }
  }
}
</script>

<style lang="scss">
.cart-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.empty-cart {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx;
  position: absolute;
  top: 35%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  
  .iconfontA {
    margin-bottom: 30rpx;
    font-size: 260rpx;
    color: #ffb6c1;
    opacity: 0.4;
    text-align: center;
  }
  
  .empty-text {
    font-size: 28rpx;
    color: #999;
    margin-bottom: 40rpx;
    text-align: center;
  }
  
  .go-shopping-btn {
    width: 240rpx;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    background: #3ec6c6;
    color: #fff;
    border-radius: 40rpx;
    font-size: 28rpx;
  }
}

.cart-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.cart-list {
  flex: 1;
  padding: 20rpx;
}

.cart-item {
  position: relative;
  display: flex;
  background: #fff;
  margin-bottom: 2rpx;
  transition: transform 0.2s ease-out;
  
  .item-content {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 20rpx;
    background: #fff;
  }

  .delete-btn {
    position: absolute;
    right: -80px;
    top: 0;
    width: 80px;
    height: 100%;
    background-color: #ff4444;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28rpx;
  }

  .checkbox-wrapper {
    padding: 0 20rpx;
    display: flex;
    align-items: center;
    
    .custom-checkbox {
      width: 36rpx;
      height: 36rpx;
      border-radius: 50%;
      border: 2rpx solid #ddd;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
      
      &.checked {
        background-color: #3ec6c6;
        border-color: #3ec6c6;
      }
    }
  }
  
  .product-image {
    width: 160rpx;
    height: 160rpx;
    border-radius: 8rpx;
    margin-right: 20rpx;
  }
  
  .product-info {
    flex: 1;
    overflow: hidden;
    
    .product-name {
      font-size: 28rpx;
      color: #333;
      margin-bottom: 12rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .spec-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8rpx 12rpx;
      background: #f5f5f5;
      border-radius: 8rpx;
      margin-bottom: 12rpx;
      cursor: pointer;
      transition: background-color 0.2s;

      &:active {
        background: #e8e8e8;
      }

      .spec-text {
        font-size: 24rpx;
        color: #666;
        flex: 1;
      }
    }
    
    .price-quantity {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .price {
        font-size: 32rpx;
        color: #ff4444;
        font-weight: bold;
      }
    }
  }
  
  .quantity-control {
    display: flex;
    align-items: center;
    
    .quantity-btn {
      width: 60rpx;
      height: 60rpx;
      line-height: 60rpx;
      text-align: center;
      background: #f5f5f5;
      color: #333;
      font-size: 28rpx;
      
      &.disabled {
        color: #ccc;
      }
    }
    
    .quantity-input {
      width: 80rpx;
      height: 60rpx;
      line-height: 60rpx;
      text-align: center;
      font-size: 28rpx;
      margin: 0 10rpx;
      background: #f5f5f5;
    }
  }
}

.settlement-bar {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #fff;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
  padding-bottom: env(safe-area-inset-bottom);
  
  .select-all {
    display: flex;
    align-items: center;
    margin-right: 20rpx;
    
    .custom-checkbox {
      width: 36rpx;
      height: 36rpx;
      border-radius: 50%;
      border: 2rpx solid #ddd;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
      margin-right: 10rpx;
      
      &.checked {
        background-color: #3ec6c6;
        border-color: #3ec6c6;
      }
    }
    
    text {
      font-size: 28rpx;
      color: #333;
    }
  }
  
  .total-info {
    flex: 1;
    text-align: right;
    margin-right: 20rpx;
    
    text {
      font-size: 28rpx;
      color: #333;
    }
    
    .total-price {
      font-size: 36rpx;
      color: #ff4444;
      font-weight: bold;
      margin-left: 10rpx;
    }
  }
  
  .checkout-btn {
    width: 220rpx;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    background: #3ec6c6;
    color: #fff;
    border-radius: 40rpx;
    font-size: 28rpx;

    &[disabled] {
      background: #ccc;
    }
  }
}

// 规格选择弹窗样式
.spec-popup-container {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 75vh;
  overflow: hidden;
  padding-bottom: env(safe-area-inset-bottom);
  position: relative;
  z-index: 1000;
}

.spec-popup {
  display: flex;
  flex-direction: column;
  height: auto;

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;
    flex-shrink: 0;

    .popup-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }

    .close-btn {
      padding: 10rpx;
      border-radius: 50%;
      background: #f5f5f5;
    }
  }

  .popup-content {
    flex: 1;
    overflow-y: auto;
    padding: 0 30rpx;
    max-height: 45vh;
  }

  .spec-info {
    display: flex;
    align-items: center;
    margin: 30rpx 0;
    padding: 20rpx;
    background: #f8f9fa;
    border-radius: 12rpx;

    .spec-image {
      width: 100rpx;
      height: 100rpx;
      border-radius: 12rpx;
      margin-right: 20rpx;
      flex-shrink: 0;
    }

    .spec-details {
      flex: 1;

      .spec-price {
        margin-bottom: 8rpx;

        .total-price {
          font-size: 30rpx;
          color: #ff4444;
          font-weight: bold;
          line-height: 1.2;
        }

        .unit-price {
          font-size: 22rpx;
          color: #999;
          margin-top: 4rpx;
        }
      }

      .spec-stock {
        font-size: 24rpx;
        color: #999;
        margin-bottom: 8rpx;
      }

      .spec-text {
        font-size: 22rpx;
        color: #666;
      }
    }
  }

  .spec-options {
    padding-bottom: 30rpx;

    .spec-loading {
      text-align: center;
      padding: 40rpx 0;

      .loading-text {
        font-size: 26rpx;
        color: #999;
      }
    }

    .no-specs {
      text-align: center;
      padding: 40rpx 0;

      .no-specs-text {
        font-size: 26rpx;
        color: #999;
      }
    }

    .spec-group {
      margin-bottom: 30rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .spec-name {
        font-size: 28rpx;
        color: #333;
        margin-bottom: 20rpx;
        font-weight: 500;
      }

      .spec-values {
        display: flex;
        flex-wrap: wrap;
        gap: 15rpx;

        .spec-value {
          padding: 15rpx 25rpx;
          border: 2rpx solid #ddd;
          border-radius: 8rpx;
          font-size: 26rpx;
          color: #666;
          background: #fff;
          transition: all 0.2s ease;
          min-width: 100rpx;
          text-align: center;

          &.active {
            border-color: #3ec6c6;
            color: #3ec6c6;
            background: rgba(62, 198, 198, 0.1);
          }

          &.disabled {
            border-color: #f0f0f0;
            color: #ccc;
            background: #f8f8f8;
            pointer-events: none;
          }
        }
      }
    }
  }

  .quantity-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx 0;

    .quantity-label {
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
    }

    .quantity-control {
      display: flex;
      align-items: center;

      .quantity-btn {
        width: 60rpx;
        height: 60rpx;
        line-height: 60rpx;
        text-align: center;
        background: #f5f5f5;
        color: #333;
        font-size: 28rpx;
        border-radius: 8rpx;

        &.disabled {
          color: #ccc;
        }

        &.minus {
          border-radius: 8rpx 0 0 8rpx;
        }

        &.plus {
          border-radius: 0 8rpx 8rpx 0;
        }
      }

      .quantity-input {
        width: 100rpx;
        height: 60rpx;
        line-height: 60rpx;
        text-align: center;
        font-size: 28rpx;
        background: #f5f5f5;
        border: none;
        outline: none;
      }
    }
  }

  .popup-actions {
    padding: 30rpx;
    padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
    border-top: 1rpx solid #f0f0f0;
    background: #fff;
    flex-shrink: 0;

    .action-btn {
      width: 100%;
      height: 88rpx;
      line-height: 88rpx;
      text-align: center;
      border-radius: 44rpx;
      font-size: 30rpx;
      font-weight: bold;

      &.confirm-btn {
        background: #3ec6c6;
        color: #fff;
        box-shadow: 0 4rpx 12rpx rgba(62, 198, 198, 0.3);
      }
    }
  }
}
</style>