import request from '@/utils/request'

//历史记录api - 获取所有类型的历史记录
export function history(params = {}) {
    return request({
        url: '/common/selectRecordsApp',
        method: 'get',
        params: {
            pageNum: params.pageNum || 1,
            pageSize: params.pageSize || 10,
        }
    })
}

// 获取舌诊历史记录
export function getTongueHistory(phone, creatime) {
    return request({
        url: '/Moudles/historyrecodeApptongue',
        method: 'post',
        data: {
            phone: phone,
            creatime: creatime
        }
    })
}

// 获取体质检测历史记录（问诊）
export function getConstitutionHistory(phone, creatime) {
    return request({
        url: '/Moudles/historyrecodeAppConsultation',
        method: 'post',
        data: {
            phone: phone,
            creatime: creatime
        }
    })
}

// 获取综合问诊历史记录
export function getComprehensiveHistory(phone, creatime) {
    return request({
        url: '/Moudles/historyrecodeApp',
        method: 'post',
        data: {
            phone: phone,
            creatime: creatime
        }
    })
}

// 获取心理评测历史记录
export function getPsychologyHistory(phone, creatime) {
    return request({
        url: '/Moudles/historyrecodeApppsychology',
        method: 'post',
        data: {
            phone: phone,
            creatime: creatime
        }
    })
}

