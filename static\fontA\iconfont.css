@font-face {
  font-family: "iconfontA"; /* Project id 4956787 */
  /* Color fonts */
  src: 
       url('@/static/fontA/iconfont.woff2') format('woff2'),
       url('@/static/fontA/iconfont.ttf') format('truetype');
}

.iconfontA {
  font-family: "iconfontA" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-xinwen:before {
  content: "\e7f7";
}

.icon-shipin:before {
  content: "\e656";
}

.icon-xinwen1:before {
  content: "\eb42";
}

.icon-wp-sj-2:before {
  content: "\e720";
}

.icon-jineng:before {
  content: "\e61e";
}

.icon-tianping:before {
  content: "\e746";
}

.icon-xuexijincheng:before {
  content: "\e626";
}

.icon-xinlipingce:before {
  content: "\e6df";
}

.icon-jincheng:before {
  content: "\e66f";
}

.icon-wenzhenjielun:before {
  content: "\e78b";
}

.icon-jifen:before {
  content: "\e61f";
}

.icon-jiashujia-weijiashujia:before {
  content: "\e602";
}

.icon-xuexijindu:before {
  content: "\e71f";
}

.icon-buchonglinchuangziliao:before {
  content: "\e860";
}

.icon-chuangjianshijian-mian:before {
  content: "\e6d2";
}

.icon-weixinshoucang:before {
  content: "\e600";
}

.icon-lianjie:before {
  content: "\e613";
}

.icon-iconfontzhizuobiaozhunbduan36:before {
  content: "\e6e9";
}

.icon-weixin:before {
  content: "\e609";
}

.icon-wodejiancha:before {
  content: "\e601";
}

.icon-yijianfankui:before {
  content: "\e820";
}

.icon-daishouhuo:before {
  content: "\e608";
}

.icon-tizhijiance:before {
  content: "\e63a";
}

.icon-ziyuan:before {
  content: "\e62e";
}

.icon-fenxiang:before {
  content: "\e634";
}

.icon-pingjia:before {
  content: "\e60e";
}

.icon-jiankangjilu:before {
  content: "\e64e";
}

.icon-daifahuo:before {
  content: "\f5dc";
}

.icon-gouwuche:before {
  content: "\e61d";
}

.icon-daifukuan:before {
  content: "\1018c";
}

.icon-tongue:before {
  content: "\e6ca";
}

.icon-shouhuodizhi:before {
  content: "\e606";
}

.icon-zaixiankefu:before {
  content: "\e607";
}

.icon-shoucang:before {
  content: "\e65a";
}

