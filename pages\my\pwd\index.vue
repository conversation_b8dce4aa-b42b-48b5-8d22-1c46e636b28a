<template>
  <view class="pwd-retrieve-container">
    <uni-forms ref="form" :value="user" labelWidth="80px">
      <uni-forms-item name="oldPassword" label="旧密码">
        <uni-easyinput type="password" v-model="user.oldPassword" placeholder="请输入旧密码" />
      </uni-forms-item>
      <uni-forms-item name="newPassword" label="新密码">
        <uni-easyinput type="password" v-model="user.newPassword" placeholder="请输入新密码" />
      </uni-forms-item>
      <uni-forms-item name="confirmPassword" label="确认密码">
        <uni-easyinput type="password" v-model="user.confirmPassword" placeholder="请确认新密码" />
      </uni-forms-item>
      <button type="primary" @click="submit">提交</button>
    </uni-forms>
  </view>
</template>

<script>
  import { updateUserPwd } from "@/api/system/user"
  import { getUserName, getPassWord, removeToken, removePassWord } from "@/utils/auth"

  export default {
    data() {
      return {
        user: {
          oldPassword: undefined,
          newPassword: undefined,
          confirmPassword: undefined
        },
        rules: {
          oldPassword: {
            rules: [{
              required: true,
              errorMessage: '旧密码不能为空'
            }, {
              validateFunction: (rule, value, data) => {
                // 从缓存中获取当前密码进行验证
                const cachedPassword = getPassWord()
                return cachedPassword === value
              },
              errorMessage: '旧密码输入错误'
            }]
          },
          newPassword: {
            rules: [{
                required: true,
                errorMessage: '新密码不能为空',
              },
              {
                minLength: 6,
                maxLength: 20,
                errorMessage: '长度在 6 到 20 个字符'
              },
              {
                validateFunction: (rule, value, data) => {
                  // 只有当旧密码输入正确时，才进行新旧密码比较
                  const cachedPassword = getPassWord()
                  if (data.oldPassword === cachedPassword && value === cachedPassword) {
                    return false
                  }
                  return true
                },
                errorMessage: '新密码不能与旧密码相同'
              }
            ]
          },
          confirmPassword: {
            rules: [{
                required: true,
                errorMessage: '确认密码不能为空'
              }, {
                validateFunction: (rule, value, data) => data.newPassword === value,
                errorMessage: '两次输入的密码不一致'
              }
            ]
          }
        }
      }
    },
    onReady() {
      this.$refs.form.setRules(this.rules)
    },
    methods: {
      submit() {
        this.$refs.form.validate().then(res => {
          // 构建包含用户名的data对象
          const updateData = {
            oldPassword: this.user.oldPassword,
            newPassword: this.user.newPassword,
            username: getUserName() // 从本地存储获取用户名
          }

          updateUserPwd(updateData).then(response => {
            // 密码修改成功后的处理
            uni.showToast({
              title: '密码修改成功',
              icon: 'success',
              duration: 2000,
              mask: true
            })

            // 延迟执行清除token和跳转，让用户看到成功提示
            setTimeout(() => {
              // 清除token和密码，但保留用户名
              removeToken()
              removePassWord()
              // 不清除用户名，让登录页面可以自动填充

              uni.reLaunch({
                url: '/pages/login'
              })
            }, 2000) // 2秒后执行

          }).catch(error => {
            console.error('密码修改失败:', error)
            uni.showToast({
              title: error.data.msg || '密码修改失败',
              icon: 'none',
              duration: 3000,
              mask: true
            })
          })
        }).catch(error => {
          console.error('表单验证失败:', error)
        })
      }
    }
  }
</script>

<style lang="scss">
  page {
    background-color: #ffffff;
  }

  .pwd-retrieve-container {
    min-height: 100vh;
    padding: 40rpx 30rpx;
    padding-bottom: 100rpx; /* 增加底部间距，确保提示信息有足够空间 */
    box-sizing: border-box;
  }

  /* 优化表单项间距 */
  .uni-forms-item {
    margin-bottom: 40rpx !important;
  }

  /* 优化输入框样式 */
  .uni-easyinput {
    border-radius: 12rpx !important;
  }

  /* 确保错误提示信息完整显示 */
  .uni-forms-item__error {
    font-size: 22rpx !important;
    position: absolute;
    top: 100rpx; /* 紧贴输入框下方 */
    left: 30rpx;
    line-height: 1.4 !important;
    word-wrap: break-word !important;
    white-space: normal !important;
  }
  button {
    margin-top: 100rpx;
    height: 88rpx;
    border-radius: 44rpx;
    background-color: #3ec6c6 !important;
    border: none !important;
    color: white !important;
    font-size: 32rpx;
    font-weight: 600;
    box-shadow: 0 8rpx 24rpx rgba(62, 198, 198, 0.3);
    transition: all 0.3s ease;
  }

  button:hover {
    background-color: #2bb1b1 !important;
  }

  button:active {
    background-color: #2bb1b1 !important;
    transform: translateY(2rpx);
    box-shadow: 0 4rpx 12rpx rgba(62, 198, 198, 0.4);
  }

  button:focus {
    background-color: #3ec6c6 !important;
  }
</style>
