<script>
  import config from './config'
  import { getToken, getTokenExpiredHandlingStatus, validateTokenSync, fixTokenSync } from '@/utils/auth'
  import { verifyUpdateSuccess, checkTokenByVersionAPI, installUpdate } from '@/utils/upversion'

  export default {
    data() {
      return {
        lastTokenCheckTime: 0, // 上次token检测时间
        tokenCheckInterval: 30 * 1000, // token检测间隔：30秒
        appShowTimer: null // 应用显示防抖定时器
      }
    },
    onLaunch: function() {
      console.log('=== 应用首次启动 ===')
      this.initApp()
    },
    onShow: function() {
      console.log('=== 应用从后台回到前台 ===')
      this.checkTokenOnShowWithDebounce()
    },
    onHide: function() {
      console.log('=== 应用进入后台 ===')
      // 清除防抖定时器
      if (this.appShowTimer) {
        clearTimeout(this.appShowTimer)
        this.appShowTimer = null
      }
    },
    methods: {
      // 初始化应用
      initApp() {
        // 初始化应用配置
        this.initConfig()
        // 检查更新状态
        this.checkUpdateStatus()
        // 检查用户登录状态
        console.log('App启动时，token的状态:', getToken());
        this.checkLogin()
        // 检查Token状态（首次启动）
        this.checkTokenOnLaunch()
      },
      initConfig() {
        this.globalData.config = config
      },
      // 检查更新状态
      checkUpdateStatus() {
        const updatePending = uni.getStorageSync('update_pending');
        if (updatePending) {
          const currentTime = Date.now();
          const timeDiff = currentTime - updatePending.timestamp;

          // 如果更新记录超过24小时，清除记录
          if (timeDiff > 24 * 60 * 60 * 1000) {
            uni.removeStorageSync('update_pending');
            return;
          }

          // 根据状态显示不同提示
          switch(updatePending.status) {
            case 'installed':
              // 验证版本更新是否成功，然后显示相应提示
              setTimeout(() => {
                const isUpdateSuccess = verifyUpdateSuccess(updatePending.version);

                if (isUpdateSuccess) {
                  // 版本验证成功，显示更新成功提示
                  uni.showModal({
                    title: '更新成功',
                    content: `恭喜！应用已成功更新到版本 ${updatePending.version}\n\n新版本包含功能改进和问题修复，感谢您的使用！`,
                    showCancel: false,
                    confirmText: '我知道了',
                    success: () => {
                      // 清除更新记录
                      uni.removeStorageSync('update_pending');
                    }
                  });
                } else {
                  // 版本验证失败，显示更新异常提示
                  console.warn(`版本更新验证失败: 期望版本 ${updatePending.version}, 当前版本 ${this.globalData.config.appInfo.version}`);
                  uni.showModal({
                    title: '更新异常',
                    content: `检测到版本更新可能未完全成功，当前版本可能不是最新版本。\n\n如有问题，请重新检查更新或联系客服。`,
                    showCancel: false,
                    confirmText: '我知道了',
                    success: () => {
                      // 清除更新记录
                      uni.removeStorageSync('update_pending');
                    }
                  });
                }
              }, 1000);
              break;

            case 'downloaded':
            case 'ready_to_install':
              // 提醒用户有未完成的安装
              setTimeout(() => {
                uni.showModal({
                  title: '安装提醒',
                  content: `检测到版本 ${updatePending.version} 的安装包已下载完成，是否现在安装？`,
                  confirmText: '立即安装',
                  cancelText: '稍后安装',
                  success: (res) => {
                    if (res.confirm) {
                      // 检查是否有安装包路径
                      if (updatePending.tempFilePath) {
                        // 调用真正的安装函数
                        installUpdate(updatePending.version, updatePending.tempFilePath);
                      } else {
                        // 没有安装包路径，提示重新下载
                        uni.showToast({
                          title: '安装包已失效，请重新下载',
                          icon: 'none'
                        });
                        // 清除无效记录
                        uni.removeStorageSync('update_pending');
                      }
                    } else {
                      // 用户选择稍后安装，保留记录
                      console.log('用户选择稍后安装');
                    }
                  }
                });
              }, 2000);
              break;

            default:
              // 其他状态清除记录
              uni.removeStorageSync('update_pending');
              break;
          }
        }
      },

      checkLogin() {
        console.log('检查令牌是否存在');

        // 首先检查token同步状态
        const syncStatus = validateTokenSync();
        if (!syncStatus.isSync) {
          console.warn('checkLogin: 检测到token不同步，尝试修复');
          const fixResult = fixTokenSync();
          console.log('checkLogin: token同步修复结果:', fixResult);
        }

        const tokenInCheckLogin = getToken();
        console.log('令牌的结果：', tokenInCheckLogin);
        if (tokenInCheckLogin) {
          console.log('令牌存在，跳过重新登录');
          return;
        }
        console.log('令牌缺失或为空，启动重新登录。');
        this.$tab.reLaunch('/pages/login')
      },

      // 应用首次启动时的Token检测
      async checkTokenOnLaunch() {
        const token = getToken();
        if (!token) {
          console.log('用户未登录，跳过Token检测');
          return;
        }

        console.log('应用首次启动，检测Token状态');
        try {
          await checkTokenByVersionAPI(false); // 不显示"已是最新版本"提示
        } catch (error) {
          console.error('首次启动Token检测失败:', error);
        }
      },

      // 应用从后台回到前台时的Token检测（带防抖）
      checkTokenOnShowWithDebounce() {
        // 清除之前的定时器
        if (this.appShowTimer) {
          clearTimeout(this.appShowTimer)
        }

        // 设置防抖定时器，避免频繁切换时重复检测
        this.appShowTimer = setTimeout(() => {
          this.checkTokenOnShow()
        }, 500) // 500ms防抖
      },

      // 应用从后台回到前台时的Token检测
      async checkTokenOnShow() {
        const token = getToken();
        if (!token) {
          console.log('用户未登录，跳过Token检测');
          return;
        }

        // 检查是否正在处理token过期
        if (getTokenExpiredHandlingStatus()) {
          console.log('Token过期处理正在进行中，跳过重复检测');
          return;
        }

        // 检查检测间隔，避免过于频繁的检测
        const now = Date.now();
        if (now - this.lastTokenCheckTime < this.tokenCheckInterval) {
          console.log(`距离上次Token检测时间过短（${now - this.lastTokenCheckTime}ms < ${this.tokenCheckInterval}ms），跳过检测`);
          return;
        }

        console.log('应用回到前台，检测Token状态');
        this.lastTokenCheckTime = now;

        try {
          await checkTokenByVersionAPI(false); // 不显示"已是最新版本"提示
        } catch (error) {
          console.error('应用回到前台Token检测失败:', error);
          // 检测失败时不重置时间，允许下次尽快重试
          this.lastTokenCheckTime = 0;
        }
      }
    }
  }
</script>

<style lang="scss">
  /* 注意要写在第一行，同时给style标签加入lang="scss"属性 */
  @import "@/static/fontA/iconfont.css";
  @import "@/static/fontB/iconfont.css";
  @import "uview-ui/index.scss";
  @import '@/static/scss/index.scss';
  // @import url("/static/css/animate.min.css");
</style>
