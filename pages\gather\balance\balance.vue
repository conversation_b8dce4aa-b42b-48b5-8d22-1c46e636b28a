<template>
	<view class="container">
		
		<!-- 计量换算区域 -->
		<view class="converter-section">
			<view class="section-title">计量换算(汉)</view>

			<!-- 换算输入区 -->
			<view class="converter-input">
				<view class="input-group">
					<input
						type="number"
						v-model="inputValue"
						placeholder="1"
						class="number-input"
						@input="handleInputChange"
					/>
					<view class="unit-selector" @click="showFromUnitPicker">
						<text>{{ fromUnit }}</text>
						<uni-icons type="down" size="14" color="#999"></uni-icons>
					</view>
				</view>

				<view class="equals-sign">=</view>

				<view class="input-group">
					<input
						type="text"
						:value="resultValue"
						placeholder="15.625"
						class="number-input result-input"
						readonly
					/>
					<view class="unit-selector" @click="showToUnitPicker">
						<text>{{ toUnit }}</text>
						<uni-icons type="down" size="14" color="#999"></uni-icons>
					</view>
				</view>
			</view>
		</view>

		<!-- 换算说明区域 -->
		<view class="reference-section">
			<view class="reference-title">梧桐子大 = 黄豆大</view>

			<view class="reference-list">
				<view class="reference-item">1方寸匙 = 金石类2.74克 = 药末约2克 = 草木类药末约1克</view>
				<view class="reference-item">一毫升 = 1 克</view>
				<view class="reference-item">蜀椒一升 = 50 克</view>
				<view class="reference-item">半方寸匙 = 1.5 克</view>
				<view class="reference-item">亭苈子一升 = 60 克</view>
				<view class="reference-item">一钱匙 = 1.5~1.8 克</view>
				<view class="reference-item">吴茱萸一升 = 50 克</view>
				<view class="reference-item">一铢 = 100 个黍米的重量</view>
				<view class="reference-item">半夏一升 = 130 克</view>
				<view class="reference-item">虻虫一升 = 16 克</view>
				<view class="reference-item">附子大者一枚 = 20~30 克</view>
				<view class="reference-item">附子中者一枚 = 15 克</view>
				<view class="reference-item">强乌头一枚小者 = 3 克</view>
				<view class="reference-item">强乌头一枚大者 = 5~6 克</view>
				<view class="reference-item">杏仁大者10枚 = 4 克</view>
				<view class="reference-item">栀子10枚平均15 克</view>
				<view class="reference-item">瓜蒌大小平均一枚 = 46 克</view>
				<view class="reference-item">枳实一枚 ≈ 14.4 克</view>
				<view class="reference-item">石膏鸡蛋大一枚 ≈ 40 克</view>
				<view class="reference-item">'石': 读shí,后来读dàn</view>
				<view class="reference-item">厚朴一尺 ≈ 30 克</view>
				<view class="reference-item">'龠': 读yuè</view>
				<view class="reference-item">竹叶一握 ≈ 12 克</view>
			</view>
		</view>

		<!-- 单位选择器弹窗 -->
		<uni-popup ref="popup" type="bottom" :mask-click="true">
			<view class="picker-container">
				<view class="picker-header">
					<view class="picker-cancel" @click="onPickerCancel">取消</view>
					<view class="picker-title">{{ pickerTitle }}</view>
					<view class="picker-confirm" @click="onPickerConfirm">确定</view>
				</view>
				<picker-view
					class="picker-view"
					:value="pickerValue"
					@change="onPickerChange"
					:indicator-style="indicatorStyle"
				>
					<picker-view-column>
						<view
							v-for="(item, index) in currentPickerData"
							:key="index"
							class="picker-item"
							:class="{ 'picker-item-selected': index === pickerValue[0] }"
						>
							{{ item.label }}
						</view>
					</picker-view-column>
				</picker-view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				inputValue: '1',
				resultValue: '15.625',
				fromUnit: '两',
				toUnit: '克',
				showPicker: false,
				pickerType: '', // 'from' 或 'to'
				pickerTitle: '单位选择',
				pickerValue: [0], // picker-view 的选中值
				selectedIndex: 0, // 当前选中的索引
				indicatorStyle: 'height: 58px; background: transparent;', // picker 指示器样式

				// 中医计量单位
				units: [
					'石', '钧', '斤', '两', '撮', '刀圭', '钱匙', '铢', '分', '斛', '斗', '升', '合', '龠', '撮', '主', '引', '丈', '尺', '寸'
				],

				// 现代单位 - 根据中医单位类型分类
				modernUnits: {
					weight: ['克'], // 分及分以上对应克
					volume: ['毫升'], // 圭以及圭以上分以下对应毫升
					length: ['厘米'] // 其余的对应厘米
				},

				// 换算关系表 - 根据中医古籍记录
				conversionRates: {
					// 重量单位换算为克（分及分以上对应克）
					'石': 29760.0,  // 1石 = 4钧 = 120斤
					'钧': 7440.0,   // 1钧 = 30斤
					'斤': 248.0,    // 1斤 = 16两
					'两': 15.625, // 1两 = 4分 (参考图片中1两=15.625克)
					'撮': 2.0,      // 1撮约2克
					'刀圭': 1.5,  // 1刀圭约0.5克
					'钱匙': 1.5,  // 1钱匕 = 1.5~1.8克，取中间值
					'铢': 0.65,    // 1铢 = 100个茶米的重量，约0.1克
					'分': 3.9,   // 1分约3.9克
					

					// 容量单位换算为毫升（圭以及圭以上分以下对应毫升）
					'斛': 20000.0,  // 1厘约20000毫升
					'斗': 2000.0,   // 1斗约2000毫升
					'升': 200.0,    // 1升约200毫升（参考：蜀椒一升=50克，半夏一升=130克等）
					'合': 20.0,     // 1合约20毫升
					'龠': 10.0,      // 1龠约2毫升
					'圭': 0.5,    // 1圭约0.5毫升

					// 长度单位换算为厘米（其余的对应厘米）
					'引': 2310.0,   // 1引约2310厘米
					'丈': 231.0,    // 1丈约231厘米
					'尺': 23.1,     // 1尺约23.1厘米（参考：厚朴一尺≈30克）
					'寸': 2.31      // 1寸约2.31厘米
				}
			}
		},
		computed: {
			currentPickerData() {
				if (this.pickerType === 'from') {
					return this.units.map(unit => ({ label: unit, value: unit }))
				} else {
					// 根据选择的源单位确定目标单位类型
					const unitType = this.getUnitType(this.fromUnit)
					const targetUnits = this.modernUnits[unitType] || ['克']
					return targetUnits.map(unit => ({ label: unit, value: unit }))
				}
			}
		},
		methods: {
			goBack() {
				uni.navigateBack()
			},

			showFromUnitPicker() {
				this.pickerType = 'from'
				this.pickerTitle = '选择源单位'
				this.selectedIndex = this.units.indexOf(this.fromUnit)
				this.pickerValue = [this.selectedIndex >= 0 ? this.selectedIndex : 0]
				this.$refs.popup.open()
			},

			showToUnitPicker() {
				this.pickerType = 'to'
				this.pickerTitle = '选择目标单位'
				const unitType = this.getUnitType(this.fromUnit)
				const targetUnits = this.modernUnits[unitType] || ['克']
				this.selectedIndex = targetUnits.indexOf(this.toUnit)
				this.pickerValue = [this.selectedIndex >= 0 ? this.selectedIndex : 0]
				this.$refs.popup.open()
			},

			onPickerChange(e) {
				this.selectedIndex = e.detail.value[0]
				this.pickerValue = e.detail.value
			},

			onPickerConfirm() {
				const selectedItem = this.currentPickerData[this.selectedIndex]
				if (selectedItem) {
					if (this.pickerType === 'from') {
						this.fromUnit = selectedItem.value
						// 自动设置对应的目标单位
						this.setDefaultToUnit()
					} else {
						this.toUnit = selectedItem.value
					}
					this.calculateResult()
				}
				this.$refs.popup.close()
			},

			onPickerCancel() {
				this.$refs.popup.close()
			},

			setDefaultToUnit() {
				const unitType = this.getUnitType(this.fromUnit)
				if (unitType === 'weight') {
					this.toUnit = '克'
				} else if (unitType === 'volume') {
					this.toUnit = '毫升'
				} else {
					this.toUnit = '厘米'
				}
			},

			getUnitType(unit) {
				// 分及分以上对应克（重量）
				const weightUnits = ['石', '钧', '斤', '两', '撮', '刀圭', '钱匙', '铢', '分']
				// 圭以及圭以上分以下对应毫升（容量）- 注意：这里的"圭"在古代计量中通常指容量单位
				const volumeUnits = ['斛','斗', '升', '合', '龠','圭']
				// 其余的对应厘米（长度）
				const lengthUnits = ['引', '丈', '尺', '寸']

				if (weightUnits.includes(unit)) {
					return 'weight'
				} else if (volumeUnits.includes(unit)) {
					return 'volume'
				} else if (lengthUnits.includes(unit)) {
					return 'length'
				} else {
					return 'weight' // 默认为重量
				}
			},

			handleInputChange() {
				this.calculateResult()
			},

			calculateResult() {
				if (!this.inputValue || this.inputValue === '0') {
					this.resultValue = '0'
					return
				}

				const inputNum = parseFloat(this.inputValue)
				const fromRate = this.conversionRates[this.fromUnit]

				if (!fromRate) {
					this.resultValue = '0'
					return
				}

				// 计算结果
				let result = inputNum * fromRate

				// 格式化结果 - 保持合理的精度
				if (result === 0) {
					this.resultValue = '0'
				} else if (result < 0.001) {
					this.resultValue = result.toExponential(2)
				} else if (result < 0.1) {
					this.resultValue = result.toFixed(4)
				} else if (result < 1) {
					this.resultValue = result.toFixed(3)
				} else if (result < 100) {
					this.resultValue = result.toFixed(2)
				} else {
					this.resultValue = Math.round(result).toString()
				}
			}
		},

		mounted() {
			this.calculateResult()
		}
	}
</script>

<style scoped>
	.container {
		min-height: 100vh;
		background-color: #f5f5f5;
	}

	

	/* 换算区域 */
	.converter-section {
		background-color: #fff;
		margin: 10px;
		border-radius: 8px;
		padding: 20px;
	}

	.section-title {
		font-size: 16px;
		color: #666;
		margin-bottom: 20px;
	}

	.converter-input {
		display: flex;
		align-items: center;
		justify-content: space-between;
		gap: 15px;
	}

	.input-group {
		flex: 1;
		display: flex;
		align-items: center;
		border: 1px solid #e5e5e5;
		border-radius: 6px;
		overflow: hidden;
	}

	.number-input {
		flex: 1;
		height: 44px;
		padding: 0 12px;
		font-size: 16px;
		border: none;
		outline: none;
	}

	.result-input {
		background-color: #f8f8f8;
		color: #000000;
	}

	.unit-selector {
		display: flex;
		align-items: center;
		gap: 5px;
		padding: 0 12px;
		height: 44px;
		background-color: #f8f8f8;
		border-left: 1px solid #e5e5e5;
		min-width: 60px;
		justify-content: center;
	}

	.equals-sign {
		font-size: 18px;
		color: #999;
		font-weight: 500;
	}

	/* 参考说明区域 */
	.reference-section {
		background-color: #fff;
		margin: 10px;
		border-radius: 8px;
		padding: 20px;
	}

	.reference-title {
		font-size: 16px;
		color: #333;
		font-weight: 500;
		text-align: center;
		margin-bottom: 20px;
	}

	.reference-list {
		display: flex;
		flex-direction: column;
		gap: 12px;
	}

	.reference-item {
		font-size: 14px;
		color: #666;
		line-height: 1.5;
		text-align: center;
		padding: 8px 0;
	}

	/* Picker 容器样式 */
	.picker-container {
		background-color: #fff;
		border-radius: 12px 12px 0 0;
		height: 50vh;
		display: flex;
		flex-direction: column;
	}

	.picker-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 15px 20px;
		border-bottom: 1px solid #e5e5e5;
	}

	.picker-cancel,
	.picker-confirm {
		font-size: 16px;
		color: #3ec6c6;
		padding: 5px 10px;
	}

	.picker-title {
		font-size: 18px;
		font-weight: 500;
		color: #333;
	}

	.picker-view {
		flex: 1;
		padding: 20px 0;
		height: 300px;
		overflow: hidden;
	}

	.picker-item {
		display: flex;
		/* align-items: center; */
		justify-content: center;
		height: 58px;
		font-size: 16px;
		color: #4e4e4e;
		transition: all 0.2s ease;
		text-align: center;
		width: 100%;
		line-height: 1;
	}

	.picker-item-selected {
		font-weight: bold !important;
		color: #3c3c3c !important;
		font-size: 20px !important;
		text-align: center !important;
	}
</style>
