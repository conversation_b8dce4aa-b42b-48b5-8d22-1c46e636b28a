<template>
	<view class="courses-page">
		<!-- Custom Banner Image Header -->
		<view class="banner-header">
			<image src="http://www.aigather.katlot.cn/sourcePic/d1fd8e0dfc27093765d6296bd257dcf.jpg" mode="widthFix" class="header-banner-image"></image>
			<view class="back-button-overlay" @click="goBack">
				<text class="cuIcon-back back-icon"></text>
			</view>
			<view class="info-button-overlay" @click="showInfo">
				<text class="cuIcon-info back-icon"></text>
			</view>
		</view>

		<!-- Main Content List -->
		<scroll-view
			scroll-y
			class="content-scroll-view"
			refresher-enabled
			:refresher-triggered="refreshing"
			@refresherrefresh="handleRefresh"
		>
			<!-- 加载状态 -->
			<view v-if="isLoading" class="loading-container">
				<text class="loading-text">加载中...</text>
			</view>

			<!-- 课程列表 -->
			<view v-else-if="courseList.length > 0" class="course-item" v-for="(course, index) in courseList" :key="course.id" @click="goToVideoDetail(course.id)">
				<view class="item-rank">
					{{ index + 1 }}
				</view>
				<view class="item-image-wrapper">
					<image :src="course.image" mode="heightFix" class="course-image"></image>
					<view class="top-badge" v-if="course.top">
						{{ course.top }}
					</view>
					<view class="item-tag" :class="course.free === 'free' ? 'bg-green' : 'bg-red'">
						{{ course.free=== 'free' ? '免费' : '付费' }}
					</view>
					<view class="item-duration" v-if="course.duration">
						{{ course.duration }}
					</view>
					<view class="image-overlay">
						<text class="overlay-subtitle">{{ course.author}}</text>
						<text class="cuIcon-playfill play-icon"></text>
					</view>
				</view>
				<view class="item-info">
					<text class="item-title">{{ course.title }}</text>
					<view class="item-meta">
						<text class="views">{{ course.views }}</text>
						<text class="date">{{ course.time }}</text>
					</view>
				</view>
			</view>

			<!-- 空状态 -->
			<view v-else-if="!isLoading && courseList.length === 0" class="empty-container">
				<text class="empty-text">暂无课程数据</text>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	import { videoList, videoViews } from '@/api/system/video.js'

	export default {
		data() {
			return {
				isLoading: false,
				refreshing: false, // 下拉刷新状态
				courseList: []
			}
		},
		onLoad() {
			this.loadCourseData()
		},
		onShow() {
			// 页面显示时加载数据
			this.loadCourseData()
		},
		methods: {
			// 加载课程数据
			async loadCourseData() {
				try {
					this.isLoading = true
					uni.showLoading({ title: '加载中...' })

					console.log('请求API获取课程数据')
					const response = await videoList()
					console.log('视频列表API响应:', response)

					if (response && response.rows) {
						this.parseCourseData(response.rows)
					} else {
						console.warn('API返回数据格式异常:', response)
						this.courseList = []
						uni.showToast({
							title: '加载失败',
							icon: 'none'
						})
					}
				} catch (error) {
					console.error('加载课程数据失败:', error)
					uni.showToast({
						title: '网络错误',
						icon: 'none',
						duration: 2000
					})
					this.courseList = []
				} finally {
					this.isLoading = false
					uni.hideLoading()
				}
			},

			// 解析API返回的课程数据
			parseCourseData(data) {
				console.log('解析课程数据:', data)

				if (Array.isArray(data)) {
					// 按观看次数排序
					const sortedData = [...data].sort((a, b) => {
						const viewsA = this.parseViewsToNumber(a.views || 0)
						const viewsB = this.parseViewsToNumber(b.views || 0)
						return viewsB - viewsA
					})

					this.courseList = sortedData.map((item, index) => {
						return {
							id: item.id,
							image: item.image,
							free: item.free,
							title: (item.title || '').trim(),
							text: item.text,
							author: item.author,
							views: this.formatViews(item.views),
							duration: item.time || '未知时长',
							top: `${index + 1} TOP`,
							time: item.time,
							vedio: item.vedio,
							duration: item.duration,
							originalData: item
						}
					})
				}

				if (this.courseList.length > 0) {
					console.log('TOP1课程观看次数:', this.courseList[0].views);
				}
			},

			// 解析观看次数为数字
			parseViewsToNumber(views) {
				if (typeof views === 'number') {
					return views
				}

				if (typeof views === 'string') {
					const numStr = views.replace(/[^\d]/g, '')
					return parseInt(numStr) || 0
				}

				return 0
			},

			// 格式化观看次数显示
			formatViews(views) {
				const num = this.parseViewsToNumber(views)

				if (num >= 100000000) {
					return (num / 100000000).toFixed(1) + '亿次观看'
				} else if (num >= 10000) {
					return (num / 10000).toFixed(1) + '万次观看'
				} else if (num >= 1000) {
					return (num / 1000).toFixed(1) + 'k次观看'
				} else {
					return num + '次观看'
				}
			},



			goBack() {
				uni.navigateBack();
			},
			showInfo() {
				console.log('Show info clicked');
			},
			// 跳转到视频详情页面，传递完整数据
			async goToVideoDetail(courseId) {
				console.log('🔥 点击事件触发！课程ID:', courseId);

				// 查找对应的课程数据
				const courseData = this.courseList.find(course => course.id === courseId)
				if (!courseData) {
					console.error('未找到课程数据，ID:', courseId)
					uni.showToast({
						title: '课程数据不存在',
						icon: 'none'
					})
					return
				}

				console.log('准备传递的课程数据:', courseData)

				// 调用观看次数自增接口
				await this.incrementVideoViews(courseId, courseData.originalData?.views || courseData.views)

				// 将完整的课程数据编码后传递
				const encodedData = encodeURIComponent(JSON.stringify(courseData))

				// console.log('准备跳转到:', `/pages/gather/video_a/detail?videoId=${courseId}&courseData=${encodedData}`);
				uni.navigateTo({
					url: `/pages/gather/video_a/detail?videoId=${courseId}&courseData=${encodedData}`,
					success: function(res) {
						console.log('✅ 跳转成功:', res);
					},
					fail: function(err) {
						console.error('❌ 跳转失败:', err);
						uni.showToast({
							title: '页面跳转失败',
							icon: 'none'
						});
					}
				});
			},

			// 视频观看次数自增
			async incrementVideoViews(videoId, currentViews) {
				try {
					console.log('开始自增视频观看次数，视频ID:', videoId, '当前观看次数:', currentViews);

					const numericViews = this.parseViewsToNumber(currentViews);
					const newViews = numericViews + 1;

					// 调用API更新观看次数
					const updateData = {
						id: videoId,
						views: newViews
					};

					console.log('准备调用videoViews API，数据:', updateData);
					const response = await videoViews(updateData);
					console.log('videoViews API响应:', response);

					// 更新本地课程数据并重新排序
					const courseIndex = this.courseList.findIndex(course => course.id === videoId);
					if (courseIndex !== -1) {
						this.courseList[courseIndex].views = this.formatViews(newViews);
						if (this.courseList[courseIndex].originalData) {
							this.courseList[courseIndex].originalData.views = newViews;
						}

						// 重新按观看次数排序
						this.courseList.sort((a, b) => {
							const viewsA = this.parseViewsToNumber(a.originalData?.views || a.views);
							const viewsB = this.parseViewsToNumber(b.originalData?.views || b.views);
							return viewsB - viewsA;
						});
					}

					console.log('视频观看次数自增成功，新观看次数:', newViews);
				} catch (error) {
					console.error('视频观看次数自增失败:', error);
				}
			},

			// 下拉刷新处理
			handleRefresh() {
				console.log('触发下拉刷新')
				this.refreshing = true

				this.loadCourseData().then(() => {
					uni.showToast({
						title: '刷新成功',
						icon: 'success',
						duration: 1500
					})
				}).catch((error) => {
					console.error('下拉刷新失败:', error)
					uni.showToast({
						title: '刷新失败',
						icon: 'none'
					})
				}).finally(() => {
					this.refreshing = false
				})
			}
		},

		// 页面生命周期
		onHide() {
			// 页面隐藏时的处理
		}
	}
</script>

<style lang="scss">
	page {
		background-color: #f8f8f8;
	}

	.courses-page {
		display: flex;
		flex-direction: column;
		height: 100vh;
	}

	.banner-header {
		position: relative;
		width: 100%;
		height: 150px; 
		overflow: hidden;
	}

	.header-banner-image {
		width: 100%;
		height: 100%;
		object-fit: cover;
		display: block;
	}

	.back-button-overlay {
		position: absolute;
		top: var(--status-bar-height) + 10px; 
		left: 15px;
		width: 40px;
		height: 40px;
		z-index: 10;
		display: flex;
		align-items: center;
		justify-content: center;
		
	}

	.info-button-overlay {
		position: absolute;
		top: var(--status-bar-height) + 10px; 
		right: 15px;
		width: 40px;
		height: 40px;
		z-index: 10;
		display: flex;
		align-items: center;
		justify-content: center;

	}

	.back-icon {
		font-size: 28px; 
		color: #fff; /* White back icon */
	}

	.content-scroll-view {
		flex: 1;
		overflow-y: auto;
		padding: 10px 12px;
		padding-top: 0; /* Remove top padding to compensate for banner */
	}

	.course-item {
		display: flex;
		background-color: #fff;
		border-radius: 10px;
		margin-bottom: 10px;
		padding: 10px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
		align-items: center;
	}

	.item-rank {
		font-size: 24px;
		font-weight: bold;
		color: #999; /* Grey color for rank */
		width: 40px;
		text-align: center;
		flex-shrink: 0;
	}

	.item-image-wrapper {
		position: relative;
		width: 120px; /* Adjust as needed */
		height: 80px; /* Adjust as needed */
		border-radius: 8px;
		overflow: hidden;
		margin-right: 10px;
		flex-shrink: 0;
	}

	.course-image {
		width: 100%;
		height: 100%;
		object-fit: cover;
		display: block;
	}

	.top-badge {
		position: absolute;
		top: 0;
		left: 0;
		background-color: #ff8c00; /* Darker orange */
		color: #fff;
		font-size: 10px;
		padding: 2px 8px 2px 12px; /* Adjust padding for flag shape */
		clip-path: polygon(0 0, 100% 0, 85% 100%, 0% 100%); /* Create a flag shape */
		z-index: 2; /* Ensure it's above other tags */
	}

	.item-tag {
		position: absolute;
		top: 5px;
		right: 5px;
		font-size: 10px;
		color: #fff;
		padding: 2px 6px;
		border-radius: 4px;
		z-index: 1;
	}

	.bg-green {
		background-color: #00c000; /* Green for free */
	}

	.bg-red {
		background-color: #ff4500; /* Red for paid */
	}

	.item-duration {
		position: absolute;
		bottom: 5px;
		right: 5px;
		font-size: 10px;
		color: #fff;
		background-color: rgba(0, 0, 0, 0.6);
		padding: 2px 6px;
		border-radius: 4px;
		z-index: 1;
	}

	.image-overlay {
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		background-color: rgba(0, 0, 0, 0.4); /* Semi-transparent black overlay */
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 3px 5px;
	}

	.overlay-subtitle {
		font-size: 10px;
		color: #fff;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		flex: 1;
		margin-right: 5px;
	}

	.play-icon {
		font-size: 18px;
		color: #fff;
	}

	.item-info {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		height: 100%;
	}

	.item-title {
		font-size: 15px;
		font-weight: bold;
		color: #333;
		margin-bottom: 5px;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.item-subtitle {
		font-size: 12px;
		color: #666;
		margin-bottom: 5px;
	}

	.item-meta {
		display: flex;
		align-items: center;
		font-size: 12px;
		color: #999;
		margin-top: auto; 
	}

	.item-meta .views {
		margin-right: 15px;
		color: #666;
		font-weight: 500;
		background: rgba(0, 0, 0, 0.05);
		padding: 2px 8px;
		border-radius: 12px;
		font-size: 11px;
	}

	.item-meta .date {
		margin-left: auto;
	}

	/* 加载状态样式 */
	.loading-container {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 50px 0;
	}

	.loading-text {
		font-size: 16px;
		color: #999;
	}

	/* 空状态样式 */
	.empty-container {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 50px 0;
	}

	.empty-text {
		font-size: 16px;
		color: #999;
	}
</style>
