<template>
	<view class="tongue-page">
		<!-- 背景装饰 -->
		<view class="bg-decoration">
			<view class="bg-circle bg-circle-1"></view>
			<view class="bg-circle bg-circle-2"></view>
			<view class="bg-circle bg-circle-3"></view>
		</view>

		<!-- 页面内容（支持下拉刷新） -->
		<scroll-view
			class="content-scroll"
			scroll-y
			refresher-enabled
			:refresher-triggered="refreshing"
			@refresherrefresh="handleRefresh"
		>
			<view class="content">
			<!-- 搜索框 -->
			<view class="search-section">
				<view class="search-box">
					<input
						class="search-input"
						placeholder="搜索舌象名称、特征或主病"
						v-model="searchKeyword"
						@input="onSearch"
					/>
					<view v-if="searchKeyword" class="search-clear" @click="clearSearch">
						<uni-icons type="clear" size="14" color="#999"></uni-icons>
					</view>
					<view class="search-btn" @click="onSearch">
						<uni-icons type="search" size="18" color="#fff"></uni-icons>
					</view>
				</view>
			</view>

			<!-- 搜索结果 -->
			<view v-if="showSearchResults" class="search-results">
				<view v-if="!hasSearchResults" class="no-result">
					<text class="no-result-text">未找到相关舌象信息</text>
				</view>
				<view v-else class="result-list">
					<view
						v-for="(item, index) in searchResults"
						:key="index"
						class="result-item"
						@click="onSearchItemClick(item)"
					>
						<view class="result-content">
							<view class="result-header">
								<text class="result-name">{{item.name}}</text>
								<text class="result-category">{{item.category}}</text>
							</view>
							<text v-if="item.characteristics" class="result-desc">特征：{{item.characteristics}}</text>
							<text v-if="item.mainDisease" class="result-clinical">主病：{{item.mainDisease}}</text>
						</view>
						<uni-icons type="right" size="14" color="#ccc"></uni-icons>
					</view>
				</view>
			</view>

			<!-- 功能卡片区域 -->
			<view class="function-cards">
				<!-- 舌质卡片 -->
				<view class="function-card tongue-quality-card" @click="navigateToTongue">
					<view class="card-content">
							<text class="card-title">舌质</text>
					</view>
				</view>

				<!-- 舌苔卡片 -->
				<view class="function-card tongue-coating-card" @click="navigateToCoating">
					<view class="card-content">
							<text class="card-title">舌苔</text>
					</view>
				</view>

				<!-- AI舌诊卡片 -->
				<view class="function-card ai-card" @click="navigateToAI">
					<view class="card-content">
							<text class="card-title">AI舌诊</text>
					</view>
				</view>
			</view>

			<!-- 望舌指南 -->
			<view class="guide-section">
				<view class="guide-header">
					<view class="guide-title-wrapper">
					<text class="guide-title">望舌指南</text>
						<text class="guide-subtitle">专业舌诊知识库</text>
					</view>
					<view class="guide-icon-wrapper" @click="showGuideInfo">
						<uni-icons type="info" size="20" color="#3ec6c6"></uni-icons>
					</view>
				</view>

				<view class="guide-list">
					<view
						class="guide-item"
						v-for="(item, index) in guideData"
						:key="index"
						@click="showGuideDetail(item)"
					>
						<view class="guide-content">
							<text class="guide-name">{{ item.title }}</text>
							<text class="guide-desc">{{ item.description }}</text>
						</view>
						<view class="guide-arrow">
							<uni-icons type="right" size="14" color="#ccc"></uni-icons>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 指南详情弹窗 -->
		<view class="guide-modal" v-if="showGuideModal" @click="closeGuideDetail">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<view class="modal-title-wrapper">
					<text class="modal-title">{{ selectedGuide.title }}</text>
						<view class="modal-subtitle">专业舌诊知识</view>
					</view>
					<view class="close-btn" @click="closeGuideDetail">
						<uni-icons type="close" size="20" color="#666"></uni-icons>
					</view>
				</view>
				<view class="modal-body">
					<scroll-view scroll-y class="modal-scroll">
						<view class="guide-detail-content">
							<text class="detail-text">{{ selectedGuide.content }}</text>
						</view>
					</scroll-view>
				</view>
			</view>
		</view>

		<!-- 添加指南信息弹窗 -->
		<uni-popup ref="guideInfoPopup" type="center">
			<view class="info-popup">
				<view class="info-popup-header">
					<text class="info-popup-title">关于望舌指南</text>
					<view class="info-popup-close" @click="closeGuideInfo">
						<uni-icons type="close" size="20" color="#666"></uni-icons>
					</view>
				</view>
				<view class="info-popup-content">
					<text class="info-popup-text">望舌指南是一个专业的中医舌诊知识库，包含了：</text>
					<view class="info-popup-list">
						<text class="info-popup-item">• 舌诊基础知识</text>
						<text class="info-popup-item">• 舌象辨识方法</text>
						<text class="info-popup-item">• 临床诊断指导</text>
						<text class="info-popup-item">• 注意事项说明</text>
					</view>
					<text class="info-popup-text">通过系统学习和参考本指南，可以帮助您更好地理解和运用舌诊技术。</text>
				</view>
			</view>
		</uni-popup>
		</scroll-view>
	</view>
</template>

<script>
	import { tongueList } from '@/api/system/tongue'
	import TongueCache from '@/utils/tongueCache'

	export default {
		data() {
			return {
				searchKeyword: '',
				showSearchResults: false,
				showGuideModal: false,
				selectedGuide: {},
				loading: false,
				refreshing: false, // 下拉刷新状态

				// 望舌指南数据
				guideData: [
					{
						title: '舌质是什么',
						description: '舌质即舌体本身的颜色、形态、质地',
						content: `舌质是指舌体本身，包括舌的颜色、形态、质地等方面。

舌质的观察要点：
1. 舌色：正常舌色为淡红色，润泽有神
2. 舌形：包括舌体的大小、厚薄、胖瘦
3. 舌态：舌体的动静、强弱、润燥
4. 舌质：舌体的老嫩、荣枯

舌质反映的信息：
• 舌色主要反映气血的盛衰和寒热的变化
• 舌形主要反映脏腑的虚实
• 舌态主要反映病情的轻重和预后
• 舌质主要反映正气的强弱

通过观察舌质的变化，可以了解人体脏腑功能状态，为中医诊断提供重要依据。`
					},
					{
						title: '舌苔是什么',
						description: '舌苔是舌面上的一层苔状物质',
						content: `舌苔是覆盖在舌面上的一层苔状物质，由脱落的角化上皮、唾液、细菌、食物碎屑等组成。

舌苔的观察要点：
1. 苔色：白、黄、灰、黑等不同颜色
2. 苔质：厚薄、润燥、腐腻、剥落等
3. 苔的分布：全苔、半苔、花剥苔等

舌苔的临床意义：
• 苔色主要反映病邪的性质和病位的深浅
• 苔质主要反映胃气的盛衰和病邪的轻重
• 苔的分布反映病变的范围和程度

正常舌苔：
薄白苔，苔质不厚不薄，颗粒均匀，干湿适中，表示胃气正常。

异常舌苔的意义：
• 厚苔：病邪较重，胃肠积滞
• 薄苔：病邪较轻或正气不足
• 腻苔：痰湿内盛
• 腐苔：胃肠积滞，食物腐败`
					},
					{
						title: '舌诊看什么',
						description: '舌诊主要观察舌质、舌苔、舌下络脉',
						content: `舌诊是中医四诊之一，主要观察舌质、舌苔、舌下络脉三个方面。

一、舌质观察：
1. 舌色：淡红、红、绛、淡白、青紫等
2. 舌形：胖大、瘦薄、齿痕、裂纹等
3. 舌态：强硬、痿软、颤动、歪斜等
4. 舌质：老嫩、荣枯、润燥等

二、舌苔观察：
1. 苔色：白、黄、灰、黑等
2. 苔质：厚薄、润燥、腐腻、剥落等
3. 苔的分布：全苔、半苔、花剥苔等

三、舌下络脉观察：
1. 络脉的粗细
2. 络脉的颜色
3. 络脉的充盈度
4. 络脉的分布

通过综合观察这三个方面，可以：
• 判断脏腑功能状态
• 辨别病邪性质
• 了解病情轻重
• 指导治疗方案`
					},
					{
						title: '舌诊怎么看',
						description: '舌诊的正确方法和步骤',
						content: `舌诊的正确方法对诊断准确性至关重要。

观察环境要求：
1. 光线充足：最好在自然光下观察
2. 避免有色光线：不要在彩色灯光下观察
3. 患者配合：张口自然，舌体放松

观察步骤：
1. 先看舌质，后看舌苔
2. 先看舌体，后看舌下络脉
3. 从舌尖到舌根，从舌边到舌中
4. 动态观察，不要只看一瞬间

观察要点：
• 舌质：重点看颜色、形态、质地
• 舌苔：重点看厚薄、颜色、润燥
• 舌下络脉：重点看粗细、颜色、充盈度

注意事项：
1. 观察时间不宜过长，避免舌体疲劳
2. 不要在饭后立即观察
3. 避免在服药后观察
4. 注意患者的年龄、性别差异

舌象变化的动态观察：
• 急性病：舌象变化较快
• 慢性病：舌象变化较慢
• 治疗过程中要动态观察舌象变化`
					},
					{
						title: '舌诊的临床意义',
						description: '舌诊在中医诊断中的重要作用',
						content: `舌诊在中医诊断中具有重要的临床意义，是中医四诊的重要组成部分。

诊断价值：
1. 反映脏腑功能状态
2. 判断病邪性质和病位
3. 了解病情轻重和预后
4. 指导治疗方案制定

具体临床意义：

一、辨证论治：
• 舌质淡红：气血调和，脏腑功能正常
• 舌质红：热证，阴虚火旺
• 舌质淡白：气血不足，阳气虚弱
• 舌质紫暗：血瘀，气滞血瘀

二、病情判断：
• 舌苔厚腻：病邪较重，湿浊内盛
• 舌苔薄白：病邪较轻，正气尚可
• 舌苔剥落：胃阴不足，正气虚弱
• 无苔：胃阴枯竭，病情严重

三、疗效观察：
• 舌象好转：治疗有效
• 舌象恶化：需要调整治疗方案
• 舌象稳定：病情稳定

四、预后判断：
• 舌质润泽：预后良好
• 舌质枯槁：预后较差
• 舌苔骤退：病情危重
• 舌苔渐退：病情好转

舌诊结合其他诊法，能够提高诊断的准确性和治疗的针对性。`
					},
					{
						title: '舌诊的注意事项',
						description: '进行舌诊时需要注意的要点',
						content: `舌诊虽然简便易行，但要获得准确的诊断信息，需要注意以下事项：

观察环境：
1. 光线要求：自然光线最佳，避免强光直射
2. 避免有色光线：不要在红、黄、蓝等有色灯光下观察
3. 环境安静：避免嘈杂环境影响观察

观察时机：
1. 最佳时间：上午9-11点，此时舌象最为稳定
2. 避免饭后：饭后1-2小时内不宜观察
3. 避免刷牙后：刷牙后30分钟内不宜观察
4. 避免服药后：某些药物会影响舌象

患者配合：
1. 张口自然：不要过度用力
2. 舌体放松：自然伸出，不要紧张
3. 伸舌适度：伸出舌体2/3即可
4. 保持时间：观察时间不宜过长

影响因素：
1. 年龄因素：老人舌质偏淡，小儿舌质偏红
2. 性别因素：女性月经期舌象可能有变化
3. 季节因素：夏季舌苔偏厚，冬季舌质偏淡
4. 体质因素：不同体质舌象有差异

特殊情况：
1. 假牙患者：可能影响舌体形态
2. 口腔疾病：需要排除局部病变
3. 精神状态：紧张、恐惧可能影响舌象
4. 药物影响：某些药物会改变舌苔颜色

观察技巧：
1. 先整体后局部
2. 先静态后动态
3. 多角度观察
4. 结合其他症状综合判断`
					}
				],

				// 舌质数据
				tongueData: {
					colors: [],
					shapes: [],
					spirits: [],
					states: []
				},

				// 舌苔数据
				mossData: {
					colors: [],
					qualities: []
				}
			}
		},
		computed: {
			// 搜索结果
			searchResults() {
				if (!this.searchKeyword) return [];

				const keyword = this.searchKeyword.toLowerCase();
				let results = [];

				// 搜索舌质数据 - 舌神
				this.tongueData.spirits.forEach(item => {
					if (this.matchesKeyword(item, keyword)) {
						results.push({
							...item,
							category: '舌神'
						});
					}
				});

				// 搜索舌质数据 - 舌色
				this.tongueData.colors.forEach(item => {
					if (this.matchesKeyword(item, keyword)) {
						results.push({
							...item,
							category: '舌色'
						});
					}
				});

				// 搜索舌质数据 - 舌形
				this.tongueData.shapes.forEach(item => {
					if (this.matchesKeyword(item, keyword)) {
						results.push({
							...item,
							category: '舌形'
						});
					}
				});

				// 搜索舌质数据 - 舌态
				this.tongueData.states.forEach(item => {
					if (this.matchesKeyword(item, keyword)) {
						results.push({
							...item,
							category: '舌态'
						});
					}
				});

				// 搜索舌苔数据 - 苔色
				this.mossData.colors.forEach(item => {
					if (this.matchesKeyword(item, keyword)) {
						results.push({
							...item,
							category: '苔色'
						});
					}
				});

				// 搜索舌苔数据 - 苔质
				this.mossData.qualities.forEach(item => {
					if (this.matchesKeyword(item, keyword)) {
						results.push({
							...item,
							category: '苔质'
						});
					}
				});

				return results;
			},
			// 是否有搜索结果
			hasSearchResults() {
				return this.searchResults.length > 0;
			}
		},
		mounted() {
			this.loadTongueData()
		},
		methods: {
			// 加载舌象数据（支持缓存）
			async loadTongueData(forceRefresh = false) {
				try {
					this.loading = true
					uni.showLoading({ title: '加载中...' })

					// 1. 优先从缓存加载（除非强制刷新）
					if (!forceRefresh) {
						const cacheResult = TongueCache.loadRawData()
						if (cacheResult.success) {
							console.log('从缓存加载舌象数据成功')
							this.processCachedData(cacheResult.data)
							return
						}
					}

					// 2. 缓存无效或强制刷新，请求API
					console.log('缓存无效，请求API获取舌象数据')
					const params = {
						pageNum: 1,
						pageSize: 150
					}
					const response = await tongueList(params)
					console.log('舌象搜索API响应:', response)

					if (response && response.rows) {
						// 3. 保存原始数据到缓存
						TongueCache.saveRawData(response.rows)
						// 4. 处理数据
						this.processTongueData(response.rows)
					} else if (response && Array.isArray(response)) {
						// 3. 保存原始数据到缓存
						TongueCache.saveRawData(response)
						// 4. 处理数据
						this.processTongueData(response)
					} else {
						console.warn('API返回数据格式异常:', response)
						// 如果API失败，尝试使用缓存数据
						const cacheResult = TongueCache.loadRawData()
						if (cacheResult.success) {
							console.log('API失败，使用缓存数据')
							this.processCachedData(cacheResult.data)
							uni.showToast({
								title: '使用缓存数据',
								icon: 'none'
							})
						}
					}
				} catch (error) {
					console.error('加载舌象搜索数据失败:', error)

					// API失败时尝试使用缓存数据
					const cacheResult = TongueCache.loadRawData()
					if (cacheResult.success) {
						console.log('API失败，使用缓存数据')
						this.processCachedData(cacheResult.data)
						uni.showToast({
							title: '网络异常，显示缓存数据',
							icon: 'none'
						})
					} else {
						uni.showToast({
							title: '加载失败',
							icon: 'none'
						})
					}
				} finally {
					this.loading = false
					uni.hideLoading()
				}
			},

			// 处理缓存数据（使用统一的缓存管理器）
			processCachedData(rawData) {
				console.log('处理缓存的舌象数据:', rawData.length, '条')

				// 使用缓存管理器的统一处理方法
				const processedData = TongueCache.processTongueData(rawData, 'all')

				// 更新页面数据
				this.tongueData = processedData.tongueData
				this.mossData = processedData.mossData

				// 输出统计信息
				console.log('缓存数据分类统计:')
				console.log('舌神:', this.tongueData.spirits.length, '条')
				console.log('舌色:', this.tongueData.colors.length, '条')
				console.log('舌形:', this.tongueData.shapes.length, '条')
				console.log('舌态:', this.tongueData.states.length, '条')
				console.log('苔色:', this.mossData.colors.length, '条')
				console.log('苔质:', this.mossData.qualities.length, '条')
			},

			// 处理API返回的舌象数据
			processTongueData(data) {
				console.log('处理舌象搜索数据:', data)
				console.log('数据总数:', Array.isArray(data) ? data.length : 0)

				// 清空现有数据
				this.tongueData = {
					colors: [],
					shapes: [],
					spirits: [],
					states: []
				}
				this.mossData = {
					colors: [],
					qualities: []
				}

				// 根据API返回的数据结构进行分类
				if (Array.isArray(data)) {
					data.forEach(item => {

						const category = item.category || ''
						const subCategory = item.subCategory || ''

						// 根据API返回的数据结构处理
						const processedItem = {
							id: item.id || Math.random().toString(36).substring(2, 11),
							name: item.name || '未知舌象',
							description: item.description || '暂无描述',
							characteristics: item.description || '暂无特征描述',
							clinicalSignificance: item.clinicalMeaning || '暂无临床意义',
							mechanism: item.pathogenesis || '暂无机理说明',
							mainDisease: item.mainDisease || '暂无主病信息',
							subCategory: subCategory,
							category: category,
							referencesinfo: item.referencesinfo || '',
							type: this.getTypeFromCategory(category, subCategory)
						}


						// 根据category和subCategory进行分类
						if (category === '舌质' || category.includes('舌质')) {
							switch (subCategory) {
								case '舌神':
									this.tongueData.spirits.push(processedItem)
									break
								case '舌色':
									this.tongueData.colors.push(processedItem)
									break
								case '舌形':
									this.tongueData.shapes.push(processedItem)
									break
								case '舌态':
									this.tongueData.states.push(processedItem)
									break
								default:
									console.log(`跳过未知舌质subCategory: ${item.name}, subCategory: ${subCategory}`)
							}
						} else if (category === '舌苔' || category.includes('舌苔')) {
							switch (subCategory) {
								case '苔色':
									this.mossData.colors.push(processedItem)
									break
								case '苔质':
									this.mossData.qualities.push(processedItem)
									break
								default:
									console.log(`跳过未知舌苔subCategory: ${item.name}, subCategory: ${subCategory}`)
							}
						} else {
							console.log(`跳过未知category: ${item.name}, category: ${category}`)
						}
					})
				}

				console.log('分类后的搜索数据统计:')
				console.log('舌神:', this.tongueData.spirits.length, '条')
				console.log('舌色:', this.tongueData.colors.length, '条')
				console.log('舌形:', this.tongueData.shapes.length, '条')
				console.log('舌态:', this.tongueData.states.length, '条')
				console.log('苔色:', this.mossData.colors.length, '条')
				console.log('苔质:', this.mossData.qualities.length, '条')
			},

			// 根据category和subCategory获取type
			getTypeFromCategory(category, subCategory) {
				if (category === '舌质' || category.includes('舌质')) {
					switch (subCategory) {
						case '舌神':
							return 'spirit'
						case '舌色':
							return 'color'
						case '舌形':
							return 'shape'
						case '舌态':
							return 'state'
						default:
							return 'tongue_unknown'
					}
				} else if (category === '舌苔' || category.includes('舌苔')) {
					switch (subCategory) {
						case '苔色':
							return 'moss_color'
						case '苔质':
							return 'moss_quality'
						default:
							return 'moss_unknown'
					}
				}
				return 'unknown'
			},
			// 搜索匹配方法
			matchesKeyword(item, keyword) {
				// 搜索名称
				if (item.name && item.name.toLowerCase().includes(keyword)) {
					return true;
				}
				// 搜索特征
				if (item.characteristics && item.characteristics.toLowerCase().includes(keyword)) {
					return true;
				}
				// 搜索主病
				if (item.mainDisease && item.mainDisease.toLowerCase().includes(keyword)) {
					return true;
				}
				return false;
			},

			// 搜索功能
			onSearch() {
				if (this.searchKeyword.trim()) {
					this.showSearchResults = true;
				} else {
					this.showSearchResults = false;
				}
			},

			// 清除搜索
			clearSearch() {
				this.searchKeyword = '';
				this.showSearchResults = false;
			},
			// 点击搜索结果项
			onSearchItemClick(item) {
				console.log('点击搜索结果:', item)

				// 确保数据包含书籍信息
				const itemData = {
					...item,
					// 确保包含参考书籍信息
					referencesinfo: item.referencesinfo || ''
				}

				console.log('传递的搜索结果数据:', itemData)

				// 根据类型跳转到不同页面
				if (item.type === 'spirit' || item.type === 'color' || item.type === 'shape' || item.type === 'state') {
					// 舌质相关，跳转到舌质详情页面
					uni.navigateTo({
						url: `/pages/gather/tongue/detail?id=${item.id}&category=${item.category}&data=${encodeURIComponent(JSON.stringify(itemData))}`
					});
				} else if (item.type === 'moss_color' || item.type === 'moss_quality') {
					// 舌苔相关，跳转到舌苔详情页面
					uni.navigateTo({
						url: `/pages/gather/tongue/detail?id=${item.id}&category=${item.category}&data=${encodeURIComponent(JSON.stringify(itemData))}`
					});
				}

				// 清除搜索状态
				this.clearSearch();
			},
			// 导航到舌质页面
			navigateToTongue() {
				uni.navigateTo({
					url: '/pages/gather/tongue/quality'
				});
			},

			// 导航到舌苔页面
			navigateToCoating() {
				uni.navigateTo({
					url: '/pages/gather/tongue/moss'
				});
			},

			// 导航到AI舌诊
			navigateToAI() {
				uni.navigateTo({
					url: '/pages/gather/diagnosis/diagnosis'
				});
			},


			// 显示指南详情
			showGuideDetail(item) {
				this.selectedGuide = item;
				this.showGuideModal = true;
			},

			// 关闭指南详情
			closeGuideDetail() {
				this.showGuideModal = false;
				this.selectedGuide = {};
			},

			// 显示指南信息
			showGuideInfo() {
				this.$refs.guideInfoPopup.open();
			},
			
			// 关闭指南信息
			closeGuideInfo() {
				this.$refs.guideInfoPopup.close();
			},

			// 下拉刷新
			handleRefresh() {
				console.log('触发下拉刷新')
				this.refreshing = true

				// 强制刷新数据
				this.loadTongueData(true).then(() => {
					uni.showToast({
						title: '刷新成功',
						icon: 'success',
						duration: 1500
					})
				}).catch((error) => {
					console.error('下拉刷新失败:', error)
					uni.showToast({
						title: '刷新失败',
						icon: 'none'
					})
				}).finally(() => {
					this.refreshing = false
				})
			},

			// 检查缓存状态（调试用）
			checkCacheStatus() {
				const status = TongueCache.getCacheStatus()
				console.log('舌象缓存状态:', status)
				return status
			},

			// 清除缓存（调试用）
			clearCache() {
				TongueCache.clearCache()
				uni.showToast({
					title: '缓存已清除',
					icon: 'success'
				})
			}
		}
	}
</script>

<style scoped>
.tongue-page {
	min-height: 100vh;
	background: #f7f8fc;
	position: relative;
	overflow-x: hidden;
}

/* 背景装饰 */
.bg-decoration {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	pointer-events: none;
	z-index: 1;
}

.bg-circle {
	position: absolute;
	border-radius: 50%;
	background: linear-gradient(135deg, rgba(148, 192, 199, 0.1) 0%, rgba(148, 192, 199, 0.05) 100%);
	animation: float 6s ease-in-out infinite;
}

.bg-circle-1 {
	width: 200rpx;
	height: 200rpx;
	top: 10%;
	right: -50rpx;
	animation-delay: 0s;
}

.bg-circle-2 {
	width: 150rpx;
	height: 150rpx;
	top: 60%;
	left: -30rpx;
	animation-delay: 2s;
}

.bg-circle-3 {
	width: 100rpx;
	height: 100rpx;
	top: 30%;
	left: 50%;
	animation-delay: 4s;
}

@keyframes float {
	0%, 100% { transform: translateY(0px) rotate(0deg); }
	50% { transform: translateY(-20px) rotate(180deg); }
}



/* 滚动容器 */
.content-scroll {
	height: 100vh;
	position: relative;
	z-index: 2;
}

/* 页面内容 */
.content {
	padding: 0 30rpx 40rpx;
	position: relative;
	z-index: 2;
}

/* 搜索框 */
.search-section {
	margin: 30rpx 30rpx 40rpx;
}

.search-box {
	display: flex;
	align-items: center;
	background: #ffffff;
	border-radius: 40rpx;
	padding: 24rpx 30rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
	transition: all 0.3s ease;
}

.search-box:focus-within {
	transform: translateY(-2rpx);
	box-shadow: 0 12rpx 40rpx rgba(148, 192, 199, 0.3);
}

.search-input {
	flex: 1;
	font-size: 30rpx;
	color: #333;
}

.search-input::placeholder {
	color: #999;
}

.search-clear {
	padding: 10rpx;
	margin-right: 10rpx;
}

.search-btn {
	width: 52rpx;
	height: 52rpx;
	background: #3ec6c6;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-left: 16rpx;
	transition: all 0.3s ease;
}

.search-btn:active {
	transform: scale(0.95);
}

/* 搜索结果样式 */
.search-results {
	margin: 20rpx 30rpx;
	background: #fff;
	border-radius: 16rpx;
	overflow: hidden;
}

.no-result {
	padding: 60rpx 0;
	text-align: center;
}

.no-result-text {
	font-size: 28rpx;
	color: #999;
}

.result-list {
	padding: 20rpx 0;
}

.result-item {
	display: flex;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f5f5f5;
}

.result-item:last-child {
	border-bottom: none;
}

.result-content {
	flex: 1;
	margin-right: 20rpx;
}

.result-header {
	display: flex;
	align-items: center;
	margin-bottom: 10rpx;
}

.result-name {
	font-size: 30rpx;
	color: #333;
	font-weight: 500;
}

.result-category {
	font-size: 24rpx;
	color: #3ec6c6;
	background: rgba(62, 198, 198, 0.1);
	padding: 4rpx 12rpx;
	border-radius: 20rpx;
	margin-left: 12rpx;
}

.result-desc {
	font-size: 26rpx;
	color: #666;
	line-height: 1.4;
	margin-bottom: 8rpx;
}

.result-clinical {
	font-size: 24rpx;
	color: #999;
	line-height: 1.3;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
}

/* 功能卡片 */
.function-cards {
	display: flex;
	justify-content: space-around;
	margin: 20rpx 30rpx 50rpx;
	gap: 20rpx;
}

.function-card {
	flex: 1;
	height: 80rpx;
	background: #ffffff;
	border-radius: 40rpx;
	position: relative;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
	transition: all 0.3s ease;
}

.function-card:active {
	transform: scale(0.98);
	background: #f8f9fc;
}

.card-content {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 100%;
	text-align: center;
}

.card-title {
	font-size: 30rpx;
	font-weight: 500;
	color: #333;
}

.ai-card {
	background: #3ec6c6;
}

.ai-card .card-title {
	color: #ffffff;
}

/* 望舌指南 */
.guide-section {
	margin: 20rpx;
}

.guide-header {
	display: flex;
	align-items: center;
	justify-content: flex-start;
	margin-bottom: 35rpx;
}

.guide-title-wrapper {
	flex: 1;
	display: flex;
	align-items: center;
}

.guide-title {
	font-size: 34rpx;
	font-weight: 600;
	color: #333;
	margin-right: 12rpx;
	display: block;
}

.guide-subtitle {
	font-size: 26rpx;
	color: #999;
	display: block;
}

.guide-icon-wrapper {
	margin-left: auto;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.guide-list {
	overflow: hidden;
	background: #fff;
	border-radius: 16rpx;
	margin-top: 20rpx;
}

.guide-item {
	display: flex;
	align-items: center;
	padding: 28rpx 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
	transition: all 0.3s ease;
	background: #fff;
}

.guide-item:last-child {
	border-bottom: none;
}

.guide-content {
	flex: 1;
	margin-right: 10rpx;
}

.guide-name {
	font-size: 30rpx;
	color: #333;
	font-weight: 500;
	margin-bottom: 12rpx;
	display: block;
}

.guide-desc {
	font-size: 26rpx;
	color: #999;
	display: block;
	line-height: 1.4;
}

.guide-arrow {
	width: 36rpx;
	height: 36rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: -6rpx;
}

/* 指南详情弹窗 */
.guide-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	backdrop-filter: blur(5rpx);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
	padding: 40rpx;
	animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
	from {
		opacity: 0;
		transform: scale(0.9);
	}
	to {
		opacity: 1;
		transform: scale(1);
	}
}

.modal-content {
	background: rgba(255, 255, 255, 0.98);
	backdrop-filter: blur(20rpx);
	border-radius: 24rpx;
	width: 100%;
	max-height: 80vh;
	display: flex;
	flex-direction: column;
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
	animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
	from {
		transform: translateY(50rpx);
		opacity: 0;
	}
	to {
		transform: translateY(0);
		opacity: 1;
	}
}

.modal-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 35rpx 30rpx;
	border-bottom: 1rpx solid rgba(236, 240, 241, 0.8);
}

.modal-title-wrapper {
	flex: 1;
}

.modal-title {
	font-size: 36rpx;
	font-weight: 700;
	color: #2c3e50;
	margin-bottom: 5rpx;
	display: block;
}

.modal-subtitle {
	font-size: 24rpx;
	color: #7f8c8d;
	display: block;
}

.close-btn {
	width: 60rpx;
	height: 60rpx;
	background: rgba(236, 240, 241, 0.5);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}

.close-btn:active {
	background: rgba(236, 240, 241, 0.8);
	transform: scale(0.95);
}

.modal-body {
	flex: 1;
	overflow: hidden;
}

.modal-scroll {
	height: 100%;
	max-height: 60vh;
}

.guide-detail-content {
	padding: 35rpx 30rpx;
}

.detail-text {
	font-size: 28rpx;
	line-height: 1.8;
	color: #2c3e50;
	white-space: pre-line;
}

/* 信息弹窗样式 */
.info-popup {
	background: #ffffff;
	border-radius: 20rpx;
	width: 600rpx;
	padding: 40rpx;
}

.info-popup-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 30rpx;
}

.info-popup-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.info-popup-close {
	width: 48rpx;
	height: 48rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #f5f5f5;
	border-radius: 50%;
}

.info-popup-content {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
}

.info-popup-text {
	display: block;
	margin-bottom: 20rpx;
}

.info-popup-list {
	margin: 20rpx 0;
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.info-popup-item {
	color: #3ec6c6;
	font-weight: 500;
}
</style>
