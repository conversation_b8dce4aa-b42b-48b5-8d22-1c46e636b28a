<template>
  <view class="my-reviews-page">
    <!-- 用户信息头部 -->
    <view class="user-header">
      <image :src="userInfo.avatar || '/static/images/profile.jpg'" class="user-avatar" />
      <view class="user-info">
        <text class="username">{{ userInfo.nickname || '北苓' }}</text>
        <text class="user-desc">{{ formatDate(new Date()) }}评价</text>
      </view>
    </view>

    <!-- 评价列表 -->
    <view class="reviews-container">
      <view v-if="reviewList.length === 0" class="empty-state">
        <text class="iconfontA icon-pingjia empty-icon"></text>
        <text class="empty-text">暂无评价记录</text>
      </view>

      <view v-else class="review-list">
        <view class="review-item" v-for="review in reviewList" :key="review.id">
          <!-- 评价头部 -->
          <view class="review-header">
            <image :src="userInfo.avatar || '/static/images/profile.jpg'" class="reviewer-avatar" />
            <view class="reviewer-info">
              <text class="reviewer-name">{{ userInfo.nickname || '北苓' }}</text>
              <text class="review-time">{{ review.createTime }}评价</text>
              <view class="info-icon" @click="showReviewInfo(review)">
                <uni-icons type="info" size="14" color="#999"></uni-icons>
              </view>
            </view>
            <view class="more-actions" @click="showMoreActions(review.id)">
              <uni-icons type="more-filled" size="16" color="#999"></uni-icons>
            </view>
          </view>

          <!-- 评价内容 -->
          <view class="review-content">
            <text class="review-text">{{ review.content }}</text>
            
            <!-- 评价图片 -->
            <view class="review-images" v-if="review.images && review.images.length">
              <image 
                v-for="(img, index) in review.images" 
                :key="index"
                :src="img" 
                class="review-image"
                @click="previewImage(img, review.images)"
              />
            </view>
            
            <!-- 评分信息 -->
            <view class="review-rating">
              <text>综合评分: </text>
              <uni-rate :value="review.rating" readonly size="14" color="#ff9900"></uni-rate>
              <text class="rating-text">{{ review.rating }}星</text>
              <text class="divider">|</text>
              <text class="spec-info">{{ review.product.spec }}</text>
            </view>
          </view>

          <!-- 商品信息 -->
          <view class="product-info" @click="goToProduct(review.product.id)">
            <image :src="review.product.image" class="product-image" />
            <view class="product-detail">
              <text class="product-name">{{ review.product.name }}</text>
            </view>
            <button class="reorder-btn" @click.stop="reorder(review.product.id)">
              再次拼单
            </button>
            <uni-icons type="right" size="14" color="#999"></uni-icons>
          </view>

          <!-- 操作按钮 -->
          <view class="review-actions">
            <view class="action-item" @click="toggleLike(review.id)">
              <uni-icons :type="review.isLiked ? 'heart-filled' : 'heart'" size="16" :color="review.isLiked ? '#ff5555' : '#999'"></uni-icons>
              <text>赞</text>
            </view>
            <view class="action-item" @click="showComments(review.id)">
              <uni-icons type="chat" size="16" color="#999"></uni-icons>
              <text>评论</text>
            </view>
            <button class="add-review-btn" @click="addReview(review.product.id)">
              追加评价
            </button>
            <button class="share-btn" @click="shareReview(review.id)">
              分享
            </button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      userInfo: {},
      reviewList: []
    }
  },
  
  onLoad() {
    this.loadUserInfo();
    this.loadReviewList();
  },
  
  onShow() {
    this.loadReviewList();
  },
  
  methods: {
    // 加载用户信息
    loadUserInfo() {
      this.userInfo = uni.getStorageSync('userInfo') || {
        nickname: '北苓',
        avatar: '/static/images/profile.jpg'
      };
    },
    
    // 加载评价列表
    loadReviewList() {
      const reviews = uni.getStorageSync('userReviews') || [];
      this.reviewList = reviews.sort((a, b) => 
        new Date(b.createTime) - new Date(a.createTime)
      );
    },
    
    // 格式化日期
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      
      return `${year}.${month}.${day} ${hours}:${minutes}:${seconds}`;
    },
    
    // 预览图片
    previewImage(current, urls) {
      uni.previewImage({
        current,
        urls
      });
    },
    
    // 跳转到商品详情
    goToProduct(productId) {
      uni.navigateTo({
        url: `/pages/shopping/detail/detail?id=${productId}`
      });
    },
    
    // 再次拼单
    reorder(productId) {
      uni.navigateTo({
        url: `/pages/shopping/detail/detail?id=${productId}`
      });
    },
    
    // 切换点赞
    toggleLike(reviewId) {
      const reviews = uni.getStorageSync('userReviews') || [];
      const reviewIndex = reviews.findIndex(r => r.id === reviewId);
      
      if (reviewIndex !== -1) {
        reviews[reviewIndex].isLiked = !reviews[reviewIndex].isLiked;
        uni.setStorageSync('userReviews', reviews);
        this.loadReviewList();
        
        uni.showToast({
          title: reviews[reviewIndex].isLiked ? '已点赞' : '取消点赞',
          icon: 'none'
        });
      }
    },
    
    // 显示评论
    showComments(reviewId) {
      uni.showToast({
        title: '评论功能开发中',
        icon: 'none'
      });
    },
    
    // 追加评价
    addReview(productId) {
      uni.navigateTo({
        url: `/pages/shopping/appraise/photo?productId=${productId}&type=add`
      });
    },
    
    // 分享评价
    shareReview(reviewId) {
      uni.showActionSheet({
        itemList: ['分享到微信', '分享到朋友圈', '复制链接'],
        success: (res) => {
          uni.showToast({
            title: '分享功能开发中',
            icon: 'none'
          });
        }
      });
    },
    
    // 显示评价信息
    showReviewInfo(review) {
      uni.showModal({
        title: '评价信息',
        content: `评价时间: ${review.createTime}\n商品: ${review.product.name}\n评分: ${review.rating}星`,
        showCancel: false
      });
    },
    
    // 显示更多操作
    showMoreActions(reviewId) {
      uni.showActionSheet({
        itemList: ['删除评价', '举报'],
        success: (res) => {
          if (res.tapIndex === 0) {
            this.deleteReview(reviewId);
          } else if (res.tapIndex === 1) {
            uni.showToast({
              title: '举报功能开发中',
              icon: 'none'
            });
          }
        }
      });
    },
    
    // 删除评价
    deleteReview(reviewId) {
      uni.showModal({
        title: '删除评价',
        content: '确定要删除这条评价吗？',
        success: (res) => {
          if (res.confirm) {
            const reviews = uni.getStorageSync('userReviews') || [];
            const filteredReviews = reviews.filter(r => r.id !== reviewId);
            uni.setStorageSync('userReviews', filteredReviews);
            this.loadReviewList();
            
            uni.showToast({
              title: '删除成功',
              icon: 'success'
            });
          }
        }
      });
    }
  }
}
</script>

<style scoped>
.my-reviews-page {
  background: #f5f5f5;
  min-height: 100vh;
}

/* 用户头部 */
.user-header {
  background: #fff;
  padding: 32rpx 24rpx;
  display: flex;
  align-items: center;
  gap: 24rpx;
  margin-bottom: 20rpx;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
}

.user-info {
  flex: 1;
}

.username {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.user-desc {
  font-size: 24rpx;
  color: #999;
}

/* 评价列表 */
.reviews-container {
  padding: 0 24rpx;
}

.empty-state {
  text-align: center;
  padding: 120rpx 0;
  background: #fff;
  border-radius: 16rpx;
}

.empty-icon {
  font-size: 120rpx;
  color: #ddd;
  display: block;
  margin-bottom: 24rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.review-item {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.05);
}

.review-header {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.reviewer-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
}

.reviewer-info {
  flex: 1;
  position: relative;
}

.reviewer-name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  display: block;
}

.review-time {
  font-size: 24rpx;
  color: #999;
  margin-top: 4rpx;
}

.info-icon {
  position: absolute;
  right: 0;
  top: 0;
}

.review-content {
  margin-bottom: 24rpx;
}

.review-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  display: block;
  margin-bottom: 16rpx;
}

.review-images {
  display: flex;
  gap: 16rpx;
  margin-bottom: 16rpx;
  flex-wrap: wrap;
}

.review-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  object-fit: cover;
}

.review-rating {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #666;
}

.rating-text {
  color: #ff9900;
}

.divider {
  color: #ddd;
  margin: 0 8rpx;
}

.spec-info {
  color: #999;
}

.product-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
  padding: 16rpx;
  margin-bottom: 24rpx;
}

.product-image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
}

.product-detail {
  flex: 1;
}

.product-name {
  font-size: 26rpx;
  color: #333;
  line-height: 1.4;
}

.reorder-btn {
  background: transparent;
  color: #ff5555;
  border: 2rpx solid #ff5555;
  border-radius: 8rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  margin-right: 16rpx;
}

.review-actions {
  display: flex;
  align-items: center;
  gap: 32rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #f0f0f0;
}

.action-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #999;
}

.add-review-btn,
.share-btn {
  background: transparent;
  color: #666;
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  margin-left: auto;
}

.add-review-btn {
  margin-left: auto;
  margin-right: 16rpx;
}
</style>
