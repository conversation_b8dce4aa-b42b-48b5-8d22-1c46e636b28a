<template>
  <view class="login-container">
    <!-- 头部logo区域 -->
    <view class="logo-content">
      <image class="logo-image" :src="globalConfig.appInfo.logo" mode="widthFix"></image>
      <text class="title">数智舌诊移动端登录</text>
    </view>

    <!-- 登录表单 -->
    <view class="login-form">
      <!-- 账号输入 -->
      <view class="input-wrapper">
        <view class="input-item" :class="{ 'error': errors.username }">
          <uni-icons type="person" size="20" color="#999" class="input-icon"></uni-icons>
          <input v-model="loginForm.username" @input="clearUsernameError" @blur="validateUsername" class="input" type="text" placeholder="请输入账号" maxlength="30" />
        </view>
        <view v-if="errors.username" class="error-message">{{ errors.username }}</view>
      </view>

      <!-- 密码输入 -->
      <view class="input-wrapper">
        <view class="input-item" :class="{ 'error': errors.password }">
          <uni-icons type="locked" size="20" color="#999" class="input-icon"></uni-icons>
          <input v-model="loginForm.password" @input="clearPasswordError" @blur="validatePassword" type="password" class="input" placeholder="请输入密码" maxlength="20" />
        </view>
        <view v-if="errors.password" class="error-message">{{ errors.password }}</view>
      </view>

      <!-- 验证码输入 -->
      <!-- <view class="input-item captcha-item" v-if="captchaEnabled">
        <uni-icons type="chatboxes" size="20" color="#999" class="input-icon"></uni-icons>
        <input v-model="loginForm.code" type="number" class="input captcha-input" placeholder="请输入验证码" maxlength="4" />
        <view class="captcha-image" @click="getCode">
          <image :src="codeUrl" class="captcha-img"></image>
        </view>
      </view> -->

      <!-- 登录按钮 -->
      <view class="action-btn">
        <button @click="handleLogin" class="login-btn">登录</button>
      </view>

      <!-- 忘记密码和注册链接 -->
      <view class="link-section">
        <text @click="handleUserForgetPassword" class="link-text">忘记密码</text>
        <text class="separator">|</text>
        <text @click="handleUserRegister" class="link-text">立即注册</text>
      </view>

      <!-- 用户协议 -->
      <view class="agreement-section">
        <view class="checkbox-wrapper">
          <uni-data-checkbox multiple :value="ruleValue" :localdata="aaa" @change="change"></uni-data-checkbox>
        </view>
        <view class="agreement-links">
          <text @click="handleUserAgrement" class="agreement-link">《用户协议》</text>
          <text @click="handlePrivacy" class="agreement-link">《隐私协议》</text>
        </view>
      </view>
    </view>

    <!-- 同意协议提示弹窗 -->
    <u-modal :show="showRule" title="用户协议及隐私协议提示" showCancelButton confirmText="同意并继续" cancelText="放弃登录" @cancel="closeRule()" @confirm="confirmRule()">
      <view class="modal-content">
        <text>为了更好地保障您的合法权益，请您阅读并同意以下协议</text>
        <text @click="handleUserAgrement" class="modal-link">《用户协议》</text>
        <text>和</text>
        <text @click="handlePrivacy" class="modal-link">《隐私协议》</text>
      </view>
    </u-modal>
  </view>
</template>

<script>
  import { getCodeImg } from '@/api/login'
import {getUserName,getPassWord, getRule, setRule, removeRule} from '@/utils/auth'//auth.js（用户身份验证的文件），目前里边有对token和companyId的关于本地数据缓存的方法

  export default {
    data() {
      return {
		aaa:[{"value": 0,"text": "我已阅读并同意"	}],
		ruleValue:[],
        codeUrl: "",
        // captchaEnabled: true,
        // 用户注册开关
        register: true,
        globalConfig: getApp().globalData.config,
        loginForm: {
		  // company: "测试",
		  // phonenumber:"18555555555",
          username: getUserName(),//"xiaosun",
          password: getPassWord(),//"123456",
          code: "",
          uuid: '',
        },
		
		loginRules: {
			phonenumber: [{
				required: false,
				pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
				message: "请输入正确的手机号码",
				trigger: "blur"
			}],
			username: [{
				required: true,
				trigger: "blur",
				message: "请输入您的账号"
			}],
			password: [{
				required: true,
				trigger: "blur",
				message: "请输入您的密码"
			}],
			code: [{
				required: true,
				trigger: "change",
				message: "请输入验证码"
			}]
		},
		errors: {
			username: '',
			phonenumber: '',
			password: '',
			code: ''
		},
		showPasswordA:false,
		checkedFlag:getRule(), // 同意用户协议标志 
		showRule:false,
      }
    },
    created() {
      // this.getCode()
		this.getRuleValue()
    },
	/* onLoad(options) {
		checkVersion('1.1.01','http://www.aigather.katlot.cn/sourcePic/gather_A01.apk');
	}, */
    methods: {
		// 清除账号错误状态
		clearUsernameError() {
			this.errors.username = '';
		},

		// 清除密码错误状态
		clearPasswordError() {
			this.errors.password = '';
		},

		// 验证账号
		validateUsername() {
			const username = this.loginForm.username;
			if (!username) {
				this.errors.username = '请输入账号';
			} else if (username.length < 2) {
				this.errors.username = '账号长度不能少于2位';
			} else {
				this.errors.username = '';
			}
		},

		// 验证密码
		validatePassword() {
			const password = this.loginForm.password;
			if (!password) {
				this.errors.password = '请输入密码';
			} else if (password.length < 6) {
				this.errors.password = '密码长度不能少于6位';
			} else {
				this.errors.password = '';
			}
		},

		getRuleValue(){
			this.ruleValue=!!this.checkedFlag?[0]:[];
		},
		openRule(){
			this.showRule = true;
		},
		closeRule(){
			this.showRule = false;
			this.checkedFlag = false;
			setRule(this.checkedFlag);
			this.getRuleValue()
		},
		confirmRule(){
			this.showRule = false;
			this.checkedFlag = true;
			setRule(this.checkedFlag);
			this.getRuleValue()
			this.handleLogin();
		},
		
		
		validateField(field) {
			if(field == 'username' ){
				this.checkedFlag = this.loginForm.username == getUserName() ? getRule() : false;
				this.getRuleValue();
			}
			const rule = this.loginRules[field][0]
			const value = this.loginForm[field]
			let error = ''
			
			if (rule.required && !value) {
				error = rule.message
			} else if (rule.pattern && !rule.pattern.test(value)) {
				error = rule.message
			}

			this.$set(this.errors, field, error)
		},
		//忘记密码
		handleUserForgetPassword() {
			this.$tab.redirectTo(`/pages/forget-password`)
		},
		
      // 用户注册
      handleUserRegister() {
        this.$tab.redirectTo(`/pages/register`)
      },
      // 隐私协议
      handlePrivacy() {
        this.$tab.navigateTo('/pages/my/common/privacy/index')
      },
      // 用户协议
      handleUserAgrement() {
        this.$tab.navigateTo('/pages/my/common/protocol/index')
      },
      // 获取图形验证码
      // getCode() {
      //   getCodeImg().then(res => {
      //     this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled
      //     if (this.captchaEnabled) {
      //       this.codeUrl = 'data:image/gif;base64,' + res.img
      //       this.loginForm.uuid = res.uuid
      //     }
      //   })
      // },
	  
	  change(e){
	  	// console.log('e:',e);
	  	this.checkedFlag=(e.detail.data.length==1 && e.detail.data[0].value==0);
	  	setRule(this.checkedFlag)
	  },
	  
      // 登录方法
      async handleLogin() {
		  // 首先执行所有输入框验证
		  this.validateUsername();
		  this.validatePassword();

		  // 检查是否有输入错误
		  if (this.errors.username || this.errors.password) {
		  	return; // 有错误时不提交，不弹出用户协议提示
		  }

		  // 所有输入框都正确后，再检查用户协议
		  if(!this.checkedFlag){
		  	// 只有在所有输入都正确的情况下，才弹出用户协议提示
		  	this.openRule();
		  	return;
		  }

		  this.$modal.loading("登录中，请耐心等待...")
		  this.pwdLogin()
      },
      // 密码登录
      async pwdLogin() {
        this.$store.dispatch('Login', this.loginForm).then(() => {
          this.$modal.closeLoading()
          this.loginSuccess()
        })
        // .catch(() => {
        //   if (this.captchaEnabled) {
        //     this.getCode()
        //   }
        // })
      },
      // 登录成功后，处理函数
      loginSuccess(result) {
        // 设置用户信息
        this.$store.dispatch('GetInfo').then(res => {
          // 登录成功后同步用户健康信息
          this.syncUserHealthInfo()
          this.$tab.reLaunch('/pages/index')
        }).catch(() => {
          this.$modal.msgError("获取用户信息失败，请重新登录")
          this.$tab.reLaunch('/pages/login')
        })
      },

      // 同步用户健康信息到本地缓存
      async syncUserHealthInfo() {
        try {
          console.log('开始同步用户健康信息...')

          // 检查网络状态
          const networkType = await this.checkNetworkStatus()
          if (!networkType || networkType === 'none') {
            console.log('无网络连接，跳过用户信息同步')
            this.initEmptyUserInfo()
            return
          }

          // 导入用户管理API
          const { getUser } = require('@/api/system/userManagement.js')

          // 请求用户健康信息
          const response = await getUser()
          console.log('获取到的用户健康信息:', response)

          if (response && response.data) {
            let userData = null

            // 处理不同的响应数据结构
            if (Array.isArray(response.data) && response.data.length > 0) {
              userData = response.data[0]
              console.log(`服务器返回${response.data.length}条记录，取第一条`)
            } else if (typeof response.data === 'object' && Object.keys(response.data).length > 0) {
              userData = response.data
            }

            if (userData && (userData.person_name || userData.name || userData.phone || userData.phonenumber)) {
              // 转换服务器字段到本地字段
              const userHealthInfo = this.convertServerToLocal(userData)

              // 保存到本地存储
              uni.setStorageSync('userInfo', userHealthInfo)
              uni.setStorageSync('User-Informations', userHealthInfo)

              // 记录登录时间和同步时间
              const currentTime = Date.now()
              uni.setStorageSync('lastLoginTime', currentTime)
              uni.setStorageSync('lastUserInfoSyncTime', currentTime)

              console.log('用户健康信息已同步到本地缓存:', userHealthInfo)
              console.log('用户信息同步成功')
            } else {
              console.log('服务器未返回有效的用户健康信息')
              // 初始化空的用户信息结构
              this.initEmptyUserInfo()
            }
          } else {
            console.log('服务器响应为空，初始化用户信息')
            this.initEmptyUserInfo()
          }
        } catch (error) {
          console.error('同步用户健康信息失败:', error)
          // 同步失败时初始化空的用户信息结构
          this.initEmptyUserInfo()
        }
      },

      // 检查网络状态
      async checkNetworkStatus() {
        return new Promise((resolve) => {
          uni.getNetworkType({
            success: (res) => {
              console.log('网络类型:', res.networkType)
              resolve(res.networkType)
            },
            fail: () => {
              resolve('none')
            }
          })
        })
      },

      // 转换服务器字段到本地字段
      convertServerToLocal(serverData) {
        return {
          name: serverData.person_name || serverData.name || '',
          sex: serverData.sex || '',
          dateBirth: serverData.dateBirth || '',
          phonenumber: serverData.phone || serverData.phonenumber || '',
          occupation: serverData.occupation || '',
          maritalStatus: serverData.maritalStatus || '',
          height: serverData.height || '',
          weight: serverData.weight || '',
          age: serverData.age || '',
          diastolicPressure: serverData.diastolicPressure || '',
          systolicPressure: serverData.systolicPressure || '',
          allergy: serverData.allergy || '',
          medicalHistory: serverData.medicalHistory || '',
          TimeStamp: serverData.TimeStamp || new Date().getTime(),
          UserseeionType: 3
        }
      },

      // 初始化空的用户信息结构
      initEmptyUserInfo() {
        const emptyUserInfo = {
          name: '',
          sex: '',
          dateBirth: '',
          phonenumber: '',
          occupation: '',
          maritalStatus: '',
          height: '',
          weight: '',
          age: '',
          diastolicPressure: '',
          systolicPressure: '',
          allergy: '',
          medicalHistory: '',
          TimeStamp: new Date().getTime(),
          UserseeionType: 3
        }

        uni.setStorageSync('userInfo', emptyUserInfo)
        uni.setStorageSync('User-Informations', emptyUserInfo)

        // 记录登录时间
        uni.setStorageSync('lastLoginTime', Date.now())

        console.log('已初始化空的用户信息结构')
      },
	  // 切换密码可见性A
	  togglePasswordVisibilityA() {
	    this.showPasswordA = !this.showPasswordA;
	  },
    }
  }
</script>

<style lang="scss" scoped>
  page {
    background-color: #f8f9fa;
  }

  .login-container {
    min-height: 100vh;
    background: linear-gradient(180deg, #f6fcfd 60%, #eafcff 100%);
    padding: 0 40rpx;
    display: flex;
    flex-direction: column;
  }

  /* 头部logo区域 */
  .logo-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 120rpx 0 80rpx;
    text-align: center;
  }

  .logo-image {
    width: 120rpx;
    height: 120rpx;
    border-radius: 20rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
  }

  .title {
    font-size: 48rpx;
    font-weight: bold;
    color: #4e4e4e;
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  }

  /* 登录表单 */
  .login-form {
    background: #fff;
    border-radius: 24rpx;
    padding: 60rpx 40rpx;
    margin-bottom: 40rpx;
    box-shadow: 0 16rpx 40rpx rgba(0, 0, 0, 0.1);
  }

  /* 输入框样式 */
  .input-wrapper {
    position: relative;
    margin-bottom: 40rpx; /* 固定间距，为错误信息预留空间 */
  }

  .input-item {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border-radius: 16rpx;
    padding: 0 30rpx;
    height: 100rpx;
    border: 2rpx solid transparent;
    transition: all 0.3s ease;
  }

  .input-item:focus-within {
    border-color: #3ec6c6;
    background: #fff;
    box-shadow: 0 0 0 4rpx rgba(62, 198, 198, 0.1);
  }

  .input-item.error {
    border-color: #ff4757;
    background: #fff5f5;
  }

  .input-item.error:focus-within {
    border-color: #ff4757;
    box-shadow: 0 0 0 4rpx rgba(255, 71, 87, 0.1);
  }

  .error-message {
    position: absolute;
    top: 100rpx; /* 紧贴输入框下方 */
    left: 30rpx;
    color: #ff4757;
    font-size: 22rpx;
    line-height: 1.4;
    white-space: nowrap; /* 防止换行 */
  }

  .input-icon {
    margin-right: 20rpx;
    flex-shrink: 0;
  }

  .input {
    flex: 1;
    font-size: 32rpx;
    color: #333;
    height: 100%;
    border: none;
    outline: none;
  }

  .input::placeholder {
    color: #999;
  }

  /* 验证码特殊样式 */
  .captcha-item {
    width: 100%;
  }

  .captcha-input {
    flex: 1;
  }

  .captcha-image {
    margin-left: 20rpx;
    flex-shrink: 0;
  }

  .captcha-img {
    width: 160rpx;
    height: 60rpx;
    border-radius: 8rpx;
    border: 2rpx solid #e0e0e0;
  }

  /* 登录按钮 */
  .action-btn {
    margin: 60rpx 0 40rpx;
  }

  .login-btn {
    width: 100%;
    height: 100rpx;
    background: #3ec6c6;
    color: #fff;
    border: none;
    border-radius: 50rpx;
    font-size: 32rpx;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8rpx 20rpx rgba(62, 198, 198, 0.3);
    transition: all 0.3s ease;
  }

  .login-btn:active {
    transform: translateY(2rpx);
    box-shadow: 0 4rpx 12rpx rgba(62, 198, 198, 0.3);
  }

  .login-btn::after {
    border: none;
  }

  /* 链接区域 */
  .link-section {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 40rpx 0;
    gap: 20rpx;
  }

  .link-text {
    color: #3ec6c6;
    font-size: 28rpx;
    text-decoration: underline;
  }

  .separator {
    color: #ccc;
    font-size: 28rpx;
  }

  /* 用户协议区域 */
  .agreement-section {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 40rpx;
    gap: 10rpx;
  }

  .checkbox-wrapper {
    flex-shrink: 0;
  }

  /* 复选框样式自定义 */
  .checkbox-wrapper ::v-deep .uni-data-checklist {
    flex-direction: row;
    align-items: center;
  }

  .checkbox-wrapper ::v-deep .checklist-group {
    flex-direction: row;
    align-items: center;
  }

  .checkbox-wrapper ::v-deep .checklist-box {
    margin-right: 8rpx;
  }

  .checkbox-wrapper ::v-deep .checkbox__inner {
    border-color: #3ec6c6 !important;
  }

  .checkbox-wrapper ::v-deep .checkbox__inner.checkbox__inner--checked {
    background-color: #3ec6c6 !important;
    border-color: #3ec6c6 !important;
  }

  .checkbox-wrapper ::v-deep .checkbox__inner-icon {
    border-right-color: #3ec6c6 !important;
    border-bottom-color: #3ec6c6 !important;
  }

  .checkbox-wrapper ::v-deep .checklist-text {
    color: #666 !important;
    font-size: 26rpx !important;
  }

  .agreement-links {
    display: flex;
    align-items: center;
    gap: 10rpx;
  }

  .agreement-link {
    color: #3ec6c6;
    font-size: 26rpx;
    text-decoration: underline;
  }

  /* 弹窗样式 */
  .modal-content {
    padding: 20rpx;
    line-height: 1.6;
    font-size: 28rpx;
    color: #666;
  }

  .modal-link {
    color: #3ec6c6;
    text-decoration: underline;
  }

  /* 通用样式 */
  .flex {
    display: flex;
  }

  .align-center {
    align-items: center;
  }

  .justify-center {
    justify-content: center;
  }

  .text-center {
    text-align: center;
  }
</style>
