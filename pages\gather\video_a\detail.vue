<template>
	<view class="video-detail-page">
		<!-- 使用自定义视频详情组件，传递完整数据 -->
		<videoa :videoId="videoId" :courseData="courseData"></videoa>
	</view>
</template>

<script>
	import videoa from '../components/videoa/videoa.vue'

	export default {
		components: {
			videoa
		},
		data() {
			return {
				videoId: '',
				courseData: null
			}
		},
		onLoad(options) {
			console.log('视频详情页面onLoad，接收到的参数:', options);

			// 从页面参数获取视频ID
			if (options.videoId) {
				this.videoId = options.videoId;
				console.log('视频详情页面加载，视频ID:', this.videoId);
			} else {
				console.error('未获取到视频ID，options:', options);
				uni.showToast({
					title: '视频ID缺失',
					icon: 'none'
				});
			}

			// 获取传递过来的完整课程数据
			if (options.courseData) {
				try {
					this.courseData = JSON.parse(decodeURIComponent(options.courseData));
					console.log('接收到的完整课程数据:', this.courseData);
					console.log('courseData中的video字段:', this.courseData.vedio);
				} catch (error) {
					console.error('解析课程数据失败:', error);
					this.courseData = null;
				}
			} else {
				console.warn('未接收到courseData参数');
			}
		},
		onShow() {
			// 页面显示时的逻辑
			console.log('视频详情页面显示');
		}
	}
</script>

<style lang="scss">
.video-detail-page {
	width: 100%;
	min-height: 100vh;
	background: #f8f9fa;
}
</style>
