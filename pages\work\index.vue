<template>
  <view class="chat-container">
    <!-- 消息展示区域 -->
	<scroll-view  class="chat-box"  scroll-y="true" :scroll-top="scrollTop" scroll-with-animation style="max-height: 93vh;">
	  <block v-for="(msg, index) in messages" :key="index">
	    <!-- 用户消息 -->
	    <view v-if="msg.type === 'user'" class="user-message">
	      <image src="http://www.gather.katlot.cn/sourcePic/photo/user.png"  class="avatar"></image>
		  <view class="message-bubble right">
	        {{ msg.content }}
	      </view>
	    </view>
	
	    <!-- AI消息 -->
	    <view v-if="msg.type === 'bot'" class="bot-message">
	      <image 
	        src="http://www.gather.katlot.cn/sourcePic/photo/ai.png" 
	        class="avatar"></image>
	      <view class="message-bubble left">
	        {{ msg.content }}
	      </view>
	    </view>
	  </block>
	</scroll-view>
    

    <!-- 输入区域 -->
    <view class="input-area">
      <uni-easyinput  v-model="inputText" placeholder="请输入内容" :disabled="isLoading" @confirm="sendMessage" class="input-box"></uni-easyinput>
      <button v-if="!isLoading" class="send-btn" type="primary" :disabled="isLoading" @click="sendMessage">
		  <!-- <text style="font-size: 13px;white-space: nowrap;color: #fff;">发送</text> -->
		  <uni-icons  type="paperplane"  size="25"  color="#fff"></uni-icons>
      </button>
      <button v-else class="send-btn" style="background: #fff;" type="warn" :disabled="!isLoading" @click="closeWebSocketMsg">
		  <!-- <text style="font-size: 13px;white-space: nowrap;color: #fff;">结束</text> -->
          <uni-icons  type="circle-filled"  size="40"  color="#f00"></uni-icons>
      </button>
    </view>
  </view>
</template>

<script>
	import {sendMsgToAi, sendMsgToAiSSE, sendMsgToAiWS} from '@/api/system/ai.js'
	
export default {
  data() {
    return {
      inputText: '',
      messages: [
        { type: 'bot', content: '您好，我是AI智能体小特，请问有什么可以帮您？' }
      ],
      isLoading: false,
      scrollTop: 0,
	  currentEventSource:null,
	  sseController: null
    }
  },
	beforeDestroy() {
		if (this.currentEventSource) {
			this.currentEventSource.close();
		}
	},
  methods: {
    async sendMessages() {
		// console.log(this.isLoading);
      if (!this.inputText.trim() || this.isLoading) return
	  
      // console.log("用户输入msg:",this.inputText);
	  
	  // 添加用户消息
	  this.messages.push({ type: 'user',  content: this.inputText })
	  this.scrollToBottom()// 滚动到底部
	  
      try {
        this.isLoading = true;
         
          // 创建初始空消息
          const botMessage = { type: 'bot', content: '思考中请稍等 . . . ' };
          this.messages = [...this.messages, botMessage];
			let count = 0;
          // 调用流式接口
          const es = sendMsgToAiSSE(
            { text: this.inputText },
            (chunk, error) => {
				// console.log(chunk);
              if (error) {
				this.isLoading = false;
				this.messages[this.messages.length - 1].content = '...';
                uni.showToast({ title: '请求失败', icon: 'none' });
                return;
              }
              
              // 持续更新最后一条消息内容
              if (chunk) {
				  if (chunk === 'event: done\n{}') {
					  this.isLoading = false;
					  return;
				  	// console.log('aaaa关闭成功！1');
				  }
				if((count += 1) ==1){this.messages[this.messages.length - 1].content=''}
                this.messages[this.messages.length - 1].content += chunk;
                this.$nextTick(() => this.scrollToBottom()); // 确保DOM更新后滚动
              }
            }
          );
        
          // 保存EventSource实例以便后续控制
          this.currentEventSource = es;
		 
      } catch (error) {
		  console.log(error);
        uni.showToast({ title: '请求失败', icon: 'none' })
      } finally {
        this.inputText = ''
        // this.isLoading = false; //这个会提前执行怎么办
        this.scrollToBottom();// 滚动到底部
      }
    },


	async sendMessage() {
		if (!this.inputText.trim() || this.isLoading) return;
	
		try {
			const inputMsg = this.inputText
			// 初始化状态
			this.isLoading = true;
			this.messages.push({ type: 'user', content: inputMsg });
			this.$nextTick(this.scrollToBottom);
			this.messages.push({ type: 'bot', content: '思考中请稍等 . . .' });
			this.inputText = '';
			
			// 创建 WebSocket 连接并发送消息
			this.sseController = sendMsgToAiWS(
				{ text: inputMsg },
				(chunk, payload) => {				//chunk是每次的回复内容， payload是错误、结束等提示
					console.log(chunk);
					if (payload?.error) {
						this.messages[this.messages.length - 1].content = '服务器繁忙，请重试！'
						this.isLoading = false;
						this.handleError('服务器繁忙，请重试！');
						return;
					}
	
					if (payload?.done) {
						this.isLoading = false;
						this.$nextTick(this.scrollToBottom);
						return;
					}
	
					// 流式更新内容
					const lastMsg = this.messages[this.messages.length - 1];
					if(lastMsg.content == '思考中请稍等 . . .') lastMsg.content='';
					if(chunk.trim() != '<think>' && chunk.trim() != '</think>' ) lastMsg.content += chunk;
					
					// this.$nextTick(this.scrollToBottom);
				}
			);
		} catch (err) {
			console.log(err);
			this.inputText = '';
			this.isLoading = false;
			// this.handleError(err.message);
		}
		// this.$nextTick(this.scrollToBottom);
	},

	/** 弹窗提示 错误信息 */
	handleError(msg) {
		console.log(msg);  // 错误日志
		this.isLoading = false;
		uni.showToast({ title: msg, icon: 'none' });
		this.sseController?.close();
	},

	/** 关闭socket连接 */
	closeWebSocketMsg() {
		this.sseController?.close();
		console.log("socket连接已关闭");
		this.isLoading = false;
		console.log("输入框标识已重置");
	},

	/** 关闭连接 */
	beforeUnmount() {
		this.isLoading = false;
		this.sseController?.close();
	},



      
	/** 模拟ai回复 非流式 */
    mockAIResponse() {
      return new Promise(resolve => {
        setTimeout(() => {
          resolve(`已收到您的消息：${this.inputText}`)
        }, 1000)
      })
    },
    
	/** 滚动到底部 */
    scrollToBottom() {
      this.$nextTick(() => {
        const query = uni.createSelectorQuery().in(this)
        query.select('.chat-box').boundingClientRect()
        query.select('.chat-box').scrollOffset(res => {
              this.scrollTop = res.scrollHeight // 直接使用内容总高度[6](@ref)
            }).exec()
		/* query.exec(res => {
          this.scrollTop = res[0].height + 100
        }) */
      })
    },
    
	/** 。。。 */
    closeDialog() {
      uni.navigateBack()
    }

  }
}
</script>

<style lang="scss" scoped>
.chat-container {
  height: 95vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.chat-header {
  height: 60rpx;
  padding: 20rpx;
  background: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2rpx 6rpx rgba(0,0,0,0.1);
  
  .title {
    font-size: 36rpx;
    font-weight: 500;
	margin: 0 auto;
  }
  
  .close-btn {
    padding: 10rpx;
  }
}

.chat-box {
  flex: 1;
  padding: 30rpx;
  background: #f5f5f5;
}

.message-bubble {
  max-width: 70%;
  padding: 20rpx 30rpx;
  border-radius: 16rpx;
  margin: 20rpx;
  line-height: 1.5;
  
  &.left {
    background: #fff;
    border: 1rpx solid #e5e5e5;
  }
  
  &.right {
    background: #007AFF;
    color: #fff;
  }
}

.user-message, .bot-message {
  display: flex;
  align-items: flex-start;
  margin: 30rpx 0;
  
  .avatar {
    width: 80rpx;
    height: 80rpx;
	margin-top: 10px;
    border-radius: 50%;
  }
}

.user-message {
  flex-direction: row-reverse;
}

.input-area {
  padding: 20rpx;
  background: #fff;
  display: flex;
  align-items: center;
  box-shadow: 0 -2rpx 6rpx rgba(0,0,0,0.05);
  overflow: hidden;
	position: absolute;
	bottom: 0;
  width: 100%;
  .input-box {
    flex: 1;
    margin-right: 20rpx;
	
  }
  
  .send-btn {
    width: 80rpx;
    height: 80rpx;
    border-radius: 60%;
    // background: #007AFF;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .send-btn:after{
	border: 1px solid #fff;
  }
}
</style>