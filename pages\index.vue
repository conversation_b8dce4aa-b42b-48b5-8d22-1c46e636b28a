<template>
	<view  style="background: #f6fcfd;background: linear-gradient(to bottom, rgba(60, 55, 51,0.05), rgba(255,255,255,1));">
		<!-- 上边是拍照片的内容 -->
		<view class="uni-margin-wrap slideshow-card">
			<swiper class="swiper" circular indicator-dots autoplay nterval="3000" >
				<swiper-item v-for="(item,index) of slideshowList" :key="index">
					<view class="swiper-item">
						<img :src="item" alt="" style="width: 100%;border-radius: 20rpx;"/>
					</view>
				</swiper-item>
			</swiper>
		</view>
		<!-- 一 -->
		<view class="out-size nav-grid-card">
			<uni-grid :column="4" :show-border="false" :square="false" >
				<!-- <uni-grid-item>
					<view class="grid-item-box" @click="toGather()">
						<view class="icon-wrapper icon-scan">
							<text class="iconfontA icon-we<PERSON><PERSON><PERSON><PERSON><PERSON>" style="font-size: 28px; color: #fff; filter: brightness(0) invert(1);"></text>
						</view>
						<text class="text">数智健康</text>
					</view>
				</uni-grid-item> -->
				<uni-grid-item>
					<view class="grid-item-box" @click="toGather()">
						<view class="icon-wrapper icon-scan">
							<uni-icons type="scan" size="28" color="#fff"></uni-icons>
						</view>
						<text class="text">AI舌诊</text>
					</view>
				</uni-grid-item>
				<uni-grid-item>
					<view class="grid-item-box" @click="toHealthy()">
						<view class="icon-wrapper icon-physique">
							<image src="/static/images/icons/physique.png" class="custom-icon" />
						</view>
						<text class="text">体质检测</text>
					</view>
				</uni-grid-item>
				<uni-grid-item>
					<view class="grid-item-box" @click="toHeart()">
						<view class="icon-wrapper icon-heart">
							<uni-icons type="heart-filled" size="28" color="#fff"></uni-icons>
						</view>
						<text class="text">心理评测</text>
					</view>
				</uni-grid-item>
				<uni-grid-item>
					<view class="grid-item-box" @click="toVideo()">
						<view class="icon-wrapper icon-video">
							<uni-icons type="videocam-filled" size="32" color="#fff"></uni-icons>
						</view>
						<text class="text">名家讲堂</text>
					</view>
				</uni-grid-item>
					
			</uni-grid>
		</view>
		<!-- 二 -->
		<view class="out-size book-medic-card">
				<!-- 舌象科普标题 -->
				<view class="section-title">
				<view class="title-left">
					<image src="/static/images/icons/kepu.png" class="section-icon" />
					<text>舌象科普</text>
				</view>
				<view class="title-right" @click="toMore">
					<text class="more-text">更多</text>
					<uni-icons type="right" size="14" color="#999"></uni-icons>
				</view>
			</view>
				<!-- 舌象内容 -->
				<view>
				    <view class="content-grid">
				    	<view class="content-item" @click="navigateToQuality('quality')">
				    		<view class="content-flex">
				    			<view class="item-icon-wrapper custom-circle-icon">
				    				<image src="/static/images/icons/shetou.png" mode="aspectFit" class="circle-icon-img" />
				    			</view>
				    			<view class="item-text-area">
				    				<view class="item-title">舌形</view>
				    				<view class="item-subtitle">
				    					<text>气血的盈亏</text>
				    				</view>
				    			</view>
				    		</view>
				    	</view>

				    	<view class="content-item" @click="navigateToQuality('color')">
				    		<view class="content-flex">
				    			<view class="item-icon-wrapper custom-circle-icon">
				    				<image src="/static/images/icons/shetou.png" mode="aspectFit" class="circle-icon-img" />
				    			</view>
				    			<view class="item-text-area">
				    				<view class="item-title">舌色</view>
				    				<view class="item-subtitle">
				    					<text>疾病的性质</text>
				    				</view>
				    			</view>
				    		</view>
				    	</view>

				    	<view class="content-item" @click="navigateToMoss('quality')">
				    		<view class="content-flex">
				    			<view class="item-icon-wrapper custom-circle-icon">
				    				<image src="/static/images/icons/shetou.png" mode="aspectFit" class="circle-icon-img" />
				    			</view>
				    			<view class="item-text-area">
				    				<view class="item-title">苔质</view>
				    				<view class="item-subtitle">
				    					<text>病邪的轻重</text>
				    				</view>
				    			</view>
				    		</view>
				    	</view>

				    	<view class="content-item" @click="navigateToMoss('color')">
				    		<view class="content-flex">
				    			<view class="item-icon-wrapper custom-circle-icon">
				    				<image src="/static/images/icons/shetou.png" mode="aspectFit" class="circle-icon-img" />
				    			</view>
				    			<view class="item-text-area">
				    				<view class="item-title">苔色</view>
				    				<view class="item-subtitle">
				    					<text>病邪的性质</text>
				    				</view>
				    			</view>
				    		</view>
				    	</view>


				    	<view class="content-item empty-item"></view>
				    </view>
				</view>

		</view>

		<!-- 三 -->
		<view class="out-size consult-grid-card">
			<view class="shop-card" @click="toShop">
				<!-- <text>【养生商城】</text><br/>
				<text>经典名方/名医力荐</text><br/>
				<text>养肝血/健脾胃</text> -->
			</view>
			<view class="consult-right">
				<view class="consult-item" @click="toDiet">
					<view class="consult-text-area">
						<view class="consult-title">滋补药膳</view>
						<view class="consult-subtitle">药膳同源 健康大众</view>
					</view>
					<view class="consult-icon-wrapper">
						<image src="/static/images/icons/wancan.png" class="consult-custom-icon" />
					</view>
				</view>
				<view class="consult-item" @click="handleBalance()">
					<view class="consult-text-area">
						<view class="consult-title">计量换算</view>
						<view class="consult-subtitle">中医计量换算</view>
					</view>
					<view class="consult-icon-wrapper">
						<text class="iconfontB icon-tianping" style="font-size: 30px; color: #3ec6c6;"></text>
					</view>

				</view>
			</view>
		</view>

		<!-- 四 -->
		<view class="out-size feature-card" @click="toHealthy()">
			<view class="feature-text-area">
				<view class="feature-title">测一测你是什么中医体质</view>
				<view class="feature-subtitle">检测体质养生小助手<view class="feature-btn">去测试></view></view>
			</view>
			<view class="feature-icon-wrapper">
				<uni-icons type="notification-filled" color="#3ec6c6" size="35"></uni-icons>
			</view>
		</view>

		<!-- 五 -->
		<view class="out-size feature-card" @click="tohealth()">
			<view class="feature-text-area">
				<view class="feature-title">健康档案管理</view>
				<view class="feature-subtitle">查看舌诊、体质检测等历史记录<view class="feature-btn">去查看></view></view>
			</view>
			<view class="feature-icon-wrapper">
				<uni-icons type="folder-add-filled" color="#3ec6c6" size="35"></uni-icons>
			</view>
		</view>


		<pop/>

		<!-- 底部安全区域间距 -->
		<view class="bottom-safe-area"></view>

	</view>
</template>

<script>
	import pop from '@/components/pop/pop.vue' // 注意这里改为export default的方式导入
	export default {
		components:{
			pop
		},
		data() {
			return {
				slideshowList:[
					'http://www.gather.katlot.cn/sourcePic/photo/A01.jpg',
					'http://www.gather.katlot.cn/sourcePic/photo/B01.jpg',
					'http://www.gather.katlot.cn/sourcePic/photo/B02.jpg',
					'http://www.gather.katlot.cn/sourcePic/photo/B03.jpg',
					'http://www.gather.katlot.cn/sourcePic/photo/B04.jpg',
					'http://www.gather.katlot.cn/sourcePic/photo/A04.jpg',
					'http://www.gather.katlot.cn/sourcePic/photo/A05.jpg',
				],

			};
		},

		onLoad() {
		},

		methods: {

			/* 跳转 舌象识别 页面 */
			toGather(){
				console.log("AI舌诊")
				uni.navigateTo({
					url: "/pages/gather/diagnosis/diagnosis"
				});
			},



			/* 跳转 体质检测 页面 */
			toHealthy(){
				console.log("体质检测")
				uni.navigateTo({
					url: "/pages/gather/healthy/healthy"
				});
			},
			/* 跳转 数智健康 页面 */
			// toHealthy(){
			// 	console.log("数智健康")
			// 	uni.navigateTo({
			// 		url: "/pages/gather/comprehensive/digitalHealth"
			// 	});
			// },
			/* 跳转 名家讲堂 页面 */
			toVideo(){
				console.log("名家讲堂")
				uni.switchTab({
					url: "/pages/video/video"
				});
			},
			/* 跳转 AI助手 页面 */
			// toAiKat(){
			// 	console.log("AI助手")

			// 	uni.navigateTo({
			// 	  url: '/pages/work/index'  // 路径需与 pages.json 中配置的 tabBar 页面路径一致
			// 	});
			// },
			/* 跳转 心理测评 页面 */
			toHeart(){
				console.log("心理测评");
				uni.navigateTo({
					url: "/pages/heart/heart/heart"
				});
			},

			handleBalance() {
			  console.log("中医计量换算");
			  uni.navigateTo({
				url: "/pages/gather/balance/balance"
			  });
			},

			// 点击更多按钮
			toMore() {
				console.log("点击了更多按钮")
				// 跳转到舌象科普页面
				uni.navigateTo({
				  url: '/pages/gather/tongue/tongue'
				})
			},

			// 跳转到滋补药膳页面
			toDiet() {
				console.log("滋补药膳")
				uni.navigateTo({
					url: "/pages/gather/diet/diet"
				});
			},
			// 跳转到舌质页面
			navigateToQuality(type) {
				uni.navigateTo({
					url: `/pages/gather/tongue/quality?type=${type}`
				})
			},
			// 跳转到舌苔页面
			navigateToMoss(type) {
				uni.navigateTo({
					url: `/pages/gather/tongue/moss?type=${type}`
				})
			},
			/* 跳转到商城页面 */
			toShop() {
				console.log("养生商城")
				// uni.switchTab({
				// 	url: "/pages/shop/index"
				// });
			},
			/* 跳转到健康记录页面 */
			tohealth(){
				console.log("健康记录")
				uni.navigateTo({
					url: "/pages/my/health/health"
				});
			}
		},
	}
</script>

<style lang="less" scoped>
	/* 轮播图区域 */
	.slideshow-card {
		width: 690rpx;
		margin: 20rpx auto;
		border-radius: 20rpx;
		overflow: hidden; /* Ensures image respects border-radius */
		box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08);
	}
	.uni-margin-wrap {
		width: 100%;
		.swiper {
			height: 300rpx;

			/* 每一张轮播图 */
			.swiper-item {
				display: block;
				height: 300rpx;
				line-height: 300rpx;
				text-align: center;
				background: #ddd;
			}
		}
	}

	/* 导航宫格区 */
	.nav-grid-card {
		background: #fff;
		padding: 20rpx 0;
		box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.05);
		border-radius: 20rpx;
	}
	.grid-item-box {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 20rpx 0;
	}
	.icon-wrapper {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		margin-bottom: 12rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
	}
	.icon-scan {
		background: linear-gradient(135deg, #6fd6e8 0%, #3ec6c6 100%);
	}
	.icon-physique {
		background: linear-gradient(135deg, #a1c4fd 0%, #c2e9fb 100%);
	}

	.custom-icon {
		width: 60rpx;
		height: 60rpx;
	}
	.icon-heart {
		background: linear-gradient(135deg, #ff9a9e 0%, #fad0c4 100%);
	}
	.icon-video {
		background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
	}
	.text {
		font-size: 28rpx;
		color: #333;
		font-weight: 500;
	}

	/* 舌象科普卡片 */
	.book-medic-card {
		height: auto;
		overflow: hidden;
		background: linear-gradient(135deg, #ffffff 0%, #fafcff 100%);
		padding: 32rpx 24rpx;
		box-shadow: 0 12rpx 40rpx rgba(62, 198, 198, 0.08);
		border-radius: 24rpx;
		border: 1rpx solid rgba(62, 198, 198, 0.08);
		position: relative;
	}

	.book-medic-card::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 5rpx;
		background: linear-gradient(90deg, #3ec6c6 0%, #6fd6e8 50%, #a8edea 100%);
		border-radius: 24rpx 24rpx 0 0;
	}

	/* 舌象科普标题 */
	.section-title {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 16rpx;
		padding: 10rpx 0;
		border-bottom: 2rpx solid #e8f4fd;
		position: relative;
	}

	.title-left {
		display: flex;
		align-items: center;
	}

	.title-left text {
		font-size: 34rpx;
		font-weight: 600;
		color: #2c3e50;
	}

	.title-right {
		display: flex;
		align-items: center;
		cursor: pointer;
	}

	.more-text {
		font-size: 26rpx;
		color: #999;
		margin-right: 6rpx;
	}

	.section-title::after {
		content: '';
		position: absolute;
		bottom: -2rpx;
		left: 0;
		width: 50rpx;
		height: 5rpx;
		background: linear-gradient(90deg, #3ec6c6 0%, #6fd6e8 100%);
		border-radius: 10rpx;
	}

	.section-icon {
		width: 48rpx;
		height: 48rpx;
		margin-right: 16rpx;
		filter: drop-shadow(0 2rpx 4rpx rgba(62, 198, 198, 0.2));
	}
	.content-grid {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		padding-top: 16rpx;
		gap: 8rpx;
	}
	.content-item {
		width: 48%;
		height: 110rpx;
		background: linear-gradient(135deg, #ffffff 0%, #f8fbff 100%);
		border-radius: 20rpx;
		margin-bottom: 20rpx;
		display: flex;
		align-items: center;
		box-shadow: 0 6rpx 20rpx rgba(62, 198, 198, 0.08);
		border: 1rpx solid rgba(62, 198, 198, 0.1);
		transition: all 0.3s ease;
		position: relative;
		overflow: hidden;
	}

	.content-item::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		width: 6rpx;
		height: 100%;
		background: linear-gradient(180deg, #3ec6c6 0%, #6fd6e8 100%);
	}

	.content-item:hover {
		transform: translateY(-4rpx);
		box-shadow: 0 12rpx 32rpx rgba(62, 198, 198, 0.15);
	}
	.content-flex {
		display: flex;
		width: 100%;
		padding: 0 10rpx;
	}
	.item-icon-wrapper {
		width: 76rpx;
		height: 76rpx;
		border-radius: 50%;
		margin-right: 20rpx;
		margin-left: 16rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background: linear-gradient(135deg, #e8f8ff 0%, #f0f9ff 100%);
		box-shadow: 0 4rpx 12rpx rgba(62, 198, 198, 0.12);
		border: 2rpx solid rgba(62, 198, 198, 0.15);
		position: relative;
	}

	.item-icon-wrapper::after {
		content: '';
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		width: 60rpx;
		height: 60rpx;
		border-radius: 50%;
		background: rgba(62, 198, 198, 0.05);
	}
	.item-text-area {
		width: calc(100% - 76rpx - 36rpx);
		padding-left: 0;
		padding-right: 16rpx;
	}
	.item-title {
		font-weight: 600;
		line-height: 44rpx;
		font-size: 30rpx;
		color: #2c3e50;
		margin-bottom: 4rpx;
	}
	.item-subtitle {
		font-size: 26rpx;
		color: #7f8c8d;
		line-height: 36rpx;
		font-weight: 400;
	}
	.empty-item {
		height: 0 !important;
		padding: 0 !important;
		margin-bottom: 0 !important;
		background: none !important;
		box-shadow: none !important;
	}
	/* 问诊和商城 */
	.consult-grid-card {
		height: auto;
		overflow: hidden;
		display: flex;
		justify-content: space-between;
		background: none !important;
		box-shadow: none !important;
		padding: 0 !important;
	}
	.shop-card {
		width: 48%;
		background: #fff;
		border-radius: 20rpx;
		height: 260rpx;
		padding: 30rpx 20rpx;
		background-image: url('http://www.gather.katlot.cn/sourcePic/photo/A02.jpg');
		background-size: cover;
		color: #fff;
		font-size: 28rpx;
		font-weight: bold;
		line-height: 40rpx;
		box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08);
	}
	.consult-right {
		width: 48%;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}
	.consult-item {
		background: #fff;
		border-radius: 20rpx;
		margin-bottom: 20rpx;
		height: 120rpx;
		display: flex;
		justify-content: space-around;
		align-items: center;
		box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);
	}
	.consult-item:last-child {
		margin-bottom: 0;
	}
	.consult-text-area {
		width: 75%;
		text-align: left;
		padding: 10rpx;
		line-height: 40rpx;
	}
	.consult-title {
		font-weight: bold;
		font-size: 30rpx;
		color: #333;
	}
	.consult-subtitle {
		font-size: 24rpx;
		color: #888;
	}
	.consult-icon-wrapper {
		width: 25%;
		margin: auto 0;
		display: flex;
		justify-content: center;
	}

	.consult-custom-icon {
		width: 30px;
		height: 30px;
		filter: drop-shadow(0 2rpx 4rpx rgba(62, 198, 198, 0.3));
	}

	/* 特色功能卡片 */
	.feature-card {
		height: auto;
		overflow: hidden;
		display: flex;
		justify-content: space-between;
		background: #fff;
		padding: 20rpx 0;
		box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.05);
		border-radius: 20rpx;
	}
	.feature-text-area {
		width: 75%;
		text-align: left;
		padding: 10rpx 0;
		line-height: 40rpx;
	}
	.feature-title {
		margin-left: 40rpx;
		font-size: 30rpx;
		font-weight: bold;
		color: #333;
	}
	.feature-subtitle {
		margin-left: 40rpx;
		font-size: 24rpx;
		color: #888;
		display: flex;
		align-items: center;
	}
	.feature-btn {
		text-align: center;
		border-radius: 25px;
		background: linear-gradient(90deg, #6fd6e8 0%, #3ec6c6 100%);
		color:#fff;
		width: 120rpx;
		font-size: 22rpx;
		height: 40rpx;
		line-height: 40rpx;
		display: inline-block;
		margin-left: 20rpx;
	}
	.feature-icon-wrapper {
		width: 25%;
		margin: auto 0;
		padding: 0 20rpx;
		display: flex;
		justify-content: center;
	}
	/* 外边距和圆角 */
	.out-size{
		border-radius: 10px;
		margin: 20rpx 20rpx auto 20rpx;
	}
	/* Old styles, if any, can be removed or refined */

	.grid-dot { /* Keep if used elsewhere, otherwise remove */
		position: absolute;
		top: 5px;
		right: 15px;
	}
	.icon-w569-h828, .controls, .controls1-bgcolor, .controls2-bgcolor, .controls2-bgcolor1, .controls3-bgcolor, .bottom, .wrap, .shelunkuo, .w131-h131, .font-36-fff {
		display: none; /* Hide unused old styles */
	}
	.custom-circle-icon {
		width: 40px;
		height: 40px;
		border-radius: 50%;
		background: #ffffff;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 12px;
	}
	.circle-icon-img {
		width: 45rpx;
		height: 45rpx;
		display: block;
	}
	.content-item:active {
		opacity: 0.8;
	}

	/* 底部安全区域间距 */
	.bottom-safe-area {
		height: 20rpx; /* 与tabbar的间距 */
		box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.05);
	}
</style>