<template>
	<view class="new-page">
		<!-- Custom Banner Image Header -->
		<view class="banner-header">
			<image src="http://www.aigather.katlot.cn/sourcePic/fb6afd56003f68f18c6a9bd2e85e0c4.png" mode="widthFix" class="header-banner-image"></image>
			<view class="back-button-overlay" @click="goBack"></view>
		</view>

		<!-- Main Content List -->
		<scroll-view
			scroll-y
			class="content-scroll-view"
			refresher-enabled
			:refresher-triggered="refreshing"
			@refresherrefresh="handleRefresh"
		>
			<view v-if="isLoading" class="loading-container">
				<text class="loading-text">加载中...</text>
			</view>

			<view v-else-if="newList.length > 0" class="new-item" v-for="item in newList" :key="item.id" @click="goToVideoDetail(item.id)">
				<view class="item-image-wrapper">
					<image :src="item.image" mode="heightFix" class="item-image"></image>
					<view class="item-tag" :class="item.free === 'free' ? 'bg-green' : 'bg-red'">
						{{ item.free === 'free' ? '免费' : '付费' }}
					</view>
					<view class="item-duration" v-if="item.duration">
						{{ item.duration }}
					</view>
				</view>
				<view class="item-info">
					<text class="item-title">{{ item.title }}</text>
					<text class="item-subtitle">{{ item.school }}</text>
					<view class="item-meta">
						<text class="cuIcon-attentionfill"></text>
						<text class="views">{{ item.views }}</text>
						<text class="date">{{ item.time }}</text>
					</view>
				</view>
			</view>

			<!-- 空状态 -->
			<view v-else-if="!isLoading && newList.length === 0" class="empty-container">
				<text class="empty-text">暂无视频数据</text>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	import { videoList, videoViews } from '@/api/system/video'

	export default {
		data() {
			return {
				isLoading: false,
				refreshing: false, // 下拉刷新状态
				newList: []
			}
		},
		onLoad() {
			console.log('最近上新页面加载');
			this.loadVideoList();
		},

		onShow() {
			// 页面显示时加载数据
			this.loadVideoList()
		},
		methods: {
			// 加载视频列表数据
			async loadVideoList() {
				try {
					this.isLoading = true;
					uni.showLoading({
						title: '加载中...'
					});

					console.log('请求API获取最新视频数据')
					const response = await videoList();
					console.log('视频列表API响应:', response);

					if (response && response.rows) {
						this.parseVideoData(response.rows);
					} else {
						console.warn('API返回数据格式异常:', response)
						this.newList = []
						uni.showToast({
							title: '加载失败',
							icon: 'none'
						})
					}
				} catch (error) {
					console.error('加载视频数据失败:', error);
					uni.showToast({
						title: '网络错误',
						icon: 'none',
						duration: 2000
					});
					this.newList = []
				} finally {
					this.isLoading = false;
					uni.hideLoading();
				}
			},

			// 解析API返回的视频数据
			parseVideoData(data) {
				console.log('解析最新视频数据:', data);

				if (Array.isArray(data)) {
					// 按时间排序
					const sortedData = [...data].sort((a, b) => {
						const timeA = this.parseTimeToTimestamp(a.time || a.date)
						const timeB = this.parseTimeToTimestamp(b.time || b.date)
						return timeB - timeA
					})

					const apiVideoList = sortedData.map((item, index) => {
						return {
							id: item.id || (index + 1),
							image: item.image,
							free: item.free || 'free',
							title: item.title || '未知标题',
							school: item.school || item.author || '未知来源',
							text: item.text || item.introduction || '暂无简介',
							author: item.author || '未知作者',
							views: this.formatViews(item.views || 0),
							duration: item.duration || item.time || '未知时长',
							time: item.time || item.date || '未知时间',
							date: item.date || item.time || '未知日期',
							video: item.vedio || item.video,
							originalData: item
						}
					});

					if (apiVideoList.length > 0) {
						this.newList = apiVideoList;
						console.log('使用API数据，共', apiVideoList.length, '条，已按时间排序（最新在前）');
						console.log('最新视频时间:', apiVideoList[0].time);
					} else {
						console.log('API数据为空');
						this.newList = []
					}
				}

				if (this.newList.length > 0) {
					console.log('第一个视频的video字段:', this.newList[0].video);
				}
			},

			// 解析时间为时间戳
			parseTimeToTimestamp(timeStr) {
				if (!timeStr) return 0

				try {
					const date = new Date(timeStr)
					if (!isNaN(date.getTime())) {
						return date.getTime()
					}

					if (timeStr.includes('天前')) {
						const days = parseInt(timeStr.replace('天前', ''))
						return Date.now() - (days * 24 * 60 * 60 * 1000)
					}

					if (timeStr.includes('小时前')) {
						const hours = parseInt(timeStr.replace('小时前', ''))
						return Date.now() - (hours * 60 * 60 * 1000)
					}

					if (timeStr.includes('分钟前')) {
						const minutes = parseInt(timeStr.replace('分钟前', ''))
						return Date.now() - (minutes * 60 * 1000)
					}

					return Date.now()
				} catch (error) {
					console.error('解析时间失败:', timeStr, error)
					return Date.now()
				}
			},

			// 格式化观看次数显示
			formatViews(views) {
				const num = this.parseViewsToNumber(views)

				if (num >= 100000000) {
					return (num / 100000000).toFixed(1) + '亿次观看'
				} else if (num >= 10000) {
					return (num / 10000).toFixed(1) + '万次观看'
				} else if (num >= 1000) {
					return (num / 1000).toFixed(1) + 'k次观看'
				} else {
					return num + '次观看'
				}
			},

			// 解析观看次数为数字
			parseViewsToNumber(views) {
				if (typeof views === 'number') {
					return views
				}

				if (typeof views === 'string') {
					const numStr = views.replace(/[^\d]/g, '')
					return parseInt(numStr) || 0
				}

				return 0
			},


			goBack() {
				uni.navigateBack();
			},
			// 跳转到视频详情页面，传递完整数据
			async goToVideoDetail(videoId) {
				console.log('🔥 最近上新 - 点击事件触发！视频ID:', videoId);

				// 查找对应的视频数据
				const videoData = this.newList.find(video => video.id === videoId);
				if (!videoData) {
					console.error('未找到视频数据，ID:', videoId);
					uni.showToast({
						title: '视频数据不存在',
						icon: 'none'
					});
					return;
				}

				console.log('准备传递的视频数据:', videoData);

				// 调用观看次数自增接口
				await this.incrementVideoViews(videoId, videoData.originalData?.views || videoData.views)

				// 将完整的视频数据编码后传递
				const encodedData = encodeURIComponent(JSON.stringify(videoData));

				uni.navigateTo({
					url: `/pages/gather/video_a/detail?videoId=${videoId}&courseData=${encodedData}`,
					success: function(res) {
						console.log('✅ 跳转成功:', res);
					},
					fail: function(err) {
						console.error('❌ 跳转失败:', err);
						uni.showToast({
							title: '页面跳转失败',
							icon: 'none'
						});
					}
				});
			},

			// 视频观看次数自增
			async incrementVideoViews(videoId, currentViews) {
				try {
					console.log('开始自增视频观看次数，视频ID:', videoId, '当前观看次数:', currentViews);

					const numericViews = this.parseViewsToNumber(currentViews);
					const newViews = numericViews + 1;

					// 调用API更新观看次数
					const updateData = {
						id: videoId,
						views: newViews
					};

					console.log('准备调用videoViews API，数据:', updateData);
					const response = await videoViews(updateData);
					console.log('videoViews API响应:', response);

					// 更新本地视频数据
					const videoIndex = this.newList.findIndex(video => video.id === videoId);
					if (videoIndex !== -1) {
						this.newList[videoIndex].views = this.formatViews(newViews);
						if (this.newList[videoIndex].originalData) {
							this.newList[videoIndex].originalData.views = newViews;
						}
					}

					console.log('视频观看次数自增成功，新观看次数:', newViews);
				} catch (error) {
					console.error('视频观看次数自增失败:', error);
				}
			},

			// 下拉刷新处理
			handleRefresh() {
				console.log('触发下拉刷新')
				this.refreshing = true

				this.loadVideoList().then(() => {
					uni.showToast({
						title: '刷新成功',
						icon: 'success',
						duration: 1500
					})
				}).catch((error) => {
					console.error('下拉刷新失败:', error)
					uni.showToast({
						title: '刷新失败',
						icon: 'none'
					})
				}).finally(() => {
					this.refreshing = false
				})
			}
		},

		// 页面生命周期
		onHide() {
			// 页面隐藏时的处理
		}
	}
</script>

<style lang="scss">
	page {
		background-color: #f8f8f8;
	}

	.new-page {
		display: flex;
		flex-direction: column;
		height: 100vh;
	}

	.banner-header {
		position: relative;
		width: 100%;
		overflow: hidden;
	}

	.header-banner-image {
		width: 100%;
		height: auto; /* Maintain aspect ratio */
		display: block;
	}

	.back-button-overlay {
		position: absolute;
		top: var(--status-bar-height) + 10px; /* Adjust as needed */
		left: 15px; /* Adjust as needed */
		width: 40px; /* Make it clickable */
		height: 40px; /* Make it clickable */
		z-index: 10; /* Ensure it's above the image */
		/* For debugging, you can add a background: rgba(255,0,0,0.3); */
	}

	.content-scroll-view {
		flex: 1;
		overflow-y: auto;
		padding: 10px 15px;
		padding-top: 0; /* Remove top padding to compensate for banner */
	}

	.new-item {
		display: flex;
		background-color: #fff;
		border-radius: 10px;
		margin-bottom: 10px;
		padding: 10px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
		align-items: center;
	}

	.item-image-wrapper {
		position: relative;
		width: 120px; /* Adjust as needed */
		height: 80px; /* Adjust as needed */
		border-radius: 8px;
		overflow: hidden;
		margin-right: 10px;
	}

	.item-image {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	.item-tag {
		position: absolute;
		top: 5px;
		right: 5px;
		font-size: 10px;
		color: #fff;
		padding: 2px 6px;
		border-radius: 4px;
		z-index: 1;
	}

	.bg-green {
		background-color: #00c000; /* Green for free */
	}

	.bg-red {
		background-color: #ff4500; /* Red for paid */
	}

	.item-duration {
		position: absolute;
		bottom: 5px;
		right: 5px;
		font-size: 10px;
		color: #fff;
		background-color: rgba(0, 0, 0, 0.6);
		padding: 2px 6px;
		border-radius: 4px;
	}

	.item-info {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		height: 100%;
	}

	.item-title {
		font-size: 15px;
		font-weight: bold;
		color: #333;
		margin-bottom: 5px;
		/* Limit to two lines */
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.item-subtitle {
		font-size: 12px;
		color: #666;
		margin-bottom: 5px;
	}

	.item-meta {
		display: flex;
		align-items: center;
		font-size: 12px;
		color: #999;
	}

	.item-meta .cuIcon-attentionfill {
		font-size: 14px;
		margin-right: 3px;
		color: #999;
	}

	.item-meta .views {
		margin-right: 10px;
	}

	.item-meta .date {
		margin-left: auto; /* Push date to the right */
	}

	/* 加载状态样式 */
	.loading-container {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 50px 0;
	}

	.loading-text {
		font-size: 16px;
		color: #999;
	}

	/* 空状态样式 */
	.empty-container {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 100px 0;
	}

	.empty-text {
		font-size: 16px;
		color: #999;
	}
</style>
