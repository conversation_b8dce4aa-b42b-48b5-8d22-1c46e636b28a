<template>
	<view class="order-container">
		<!-- 订单状态切换栏 -->
		<view class="order-tabs">
			<view
				v-for="(tab, index) in tabs"
				:key="index"
				class="tab-item"
				:class="{ active: currentTab === index }"
				@click="switchTab(index)"
			>
				<text>{{ tab }}</text>
			</view>
		</view>

		<!-- 搜索框（仅在全部标签下显示） -->
		<view class="search-container" v-if="currentTab === 0">
			<view class="search-box">
				<uni-icons type="search" size="18" color="#999"></uni-icons>
				<input
					class="search-input"
					type="text"
					placeholder="搜索订单商品名称"
					v-model="searchKeyword"
					@input="handleSearch"
					@confirm="handleSearch"
				/>
				<view class="clear-btn" v-if="searchKeyword" @click="clearSearch">
					<uni-icons type="clear" size="16" color="#999"></uni-icons>
				</view>
			</view>
		</view>

		<!-- 我的评价模块（仅在评价标签下显示） -->
		<view class="my-review-section" v-if="currentTab === 4">
			<view class="review-header" @click="goToReviewCenter">
				<view class="review-info">
					<image :src="userInfo.avatar || '/static/images/profile.jpg'" class="user-avatar" />
					<view class="user-detail">
						<text class="username">{{ userInfo.nickname || '用户' }}</text>
						<text class="review-stats">已评价 {{ reviewStats.totalReviews }} 条</text>
					</view>
				</view>
				<view class="review-action">
					<text class="action-text">我的评价</text>
					<uni-icons type="right" size="16" color="#999"></uni-icons>
				</view>
			</view>
		</view>

		<!-- 订单列表 -->
		<scroll-view 
			class="order-list"
			scroll-y
			@scrolltolower="loadMore"
			refresher-enabled
			:refresher-triggered="isRefreshing"
			@refresherrefresh="onRefresh"
		>
			<view v-if="orderList.length === 0" class="empty-order">
				<text class="iconfontA icon-gouwuche empty-image"></text>
				<text class="empty-text">暂无相关订单</text>
			</view>

			<view v-else class="order-item" v-for="(order, index) in orderList" :key="index">
				<view class="order-header">
					<text class="order-time">{{ order.createTime }}</text>
					<text class="order-status" :class="order.statusClass">{{ order.statusText }}</text>
				</view>

				<!-- 订单商品列表 -->
				<view class="order-content" v-for="(product, pIndex) in order.products" :key="pIndex" @click="toProductDetail(product.id)" style="cursor:pointer;">
					<image :src="product.image" mode="aspectFill" class="product-image" @error="handleImageError"></image>
					<view class="product-info">
						<view class="product-title">
							<text class="product-name">{{ product.name }}</text>
							<view class="price-count">
								<text class="price">¥{{ Number(product.price).toFixed(2) }}</text>
								<text class="count">x{{ product.count }}</text>
							</view>
						</view>
						<text class="product-spec" v-if="product.specification">{{ product.specification }}</text>
						<view class="service-tags">
							<text class="tag">降价补差</text>
							<text class="tag">7天无理由退货</text>
							<text class="tag">退货包运费</text>
						</view>
					</view>
				</view>

				<view class="order-footer">
					<view class="order-summary">
						<text class="total-text">共{{ order.totalCount }}件商品 合计：</text>
						<text class="total-price">¥{{ order.totalPrice }}</text>
					</view>
					<view class="order-actions">
						<!-- 待付款状态 -->
						<template v-if="order.status === 'pending'">
							<button class="action-btn cancel" @click="cancelOrder(order.id)">取消订单</button>
							<button class="action-btn primary" @click="payOrder(order.id)">立即付款</button>
						</template>

						<!-- 待发货状态 -->
						<template v-if="order.status === 'paid'">
							<button class="action-btn remind" @click="remindShipping(order)" :disabled="order.lastRemindTime && isRemindDisabled(order.lastRemindTime)">提醒发货</button>
							<button class="action-btn test-ship" @click="testShipOrder(order.id)">模拟发货</button>
						</template>

						<!-- 待收货状态 -->
						<template v-if="order.status === 'shipping'">
							<view class="more-actions" @click="showMoreActions(order)">
								<text>更多</text>
							</view>
							<button class="action-btn secondary" @click="applyAfterSale(order.id)">申请售后</button>
							<button class="action-btn secondary" @click="viewLogistics(order)">查看物流</button>
							<button class="action-btn primary" @click="confirmReceive(order.id)">确认收货</button>
						</template>

						<!-- 待评价状态 -->
						<template v-if="order.status === 'received' && !order.isReviewed">
							<view class="more-actions" @click="showMoreActions(order)">
								<text>更多</text>
							</view>
							<button class="action-btn secondary" @click="applyAfterSale(order.id)">申请售后</button>
							<button class="action-btn secondary" @click="viewLogistics(order)">查看物流</button>
							<button class="action-btn review" @click="goToReview(order.id)">立即评价</button>
						</template>

						<!-- 已评价状态 -->
						<template v-if="(order.status === 'received' || order.status === 'completed') && order.isReviewed">
							<view class="more-actions" @click="showMoreActions(order)">
								<text>更多</text>
							</view>
							<button class="action-btn secondary" @click="applyAfterSale(order.id)">申请售后</button>
							<button class="action-btn secondary" @click="viewLogistics(order)">查看物流</button>
							<button class="action-btn reviewed" @click="viewMyReview(order.id)">查看评价</button>
						</template>

						<!-- 已完成状态（未评价的完成订单） -->
						<template v-if="order.status === 'completed' && !order.isReviewed">
							<view class="more-actions" @click="showMoreActions(order)">
								<text>更多</text>
							</view>
							<button class="action-btn secondary" @click="applyAfterSale(order.id)">申请售后</button>
							<button class="action-btn secondary" @click="viewLogistics(order)">查看物流</button>
						</template>
					</view>
				</view>

				<!-- 物流信息 -->
				<view class="logistics-info" v-if="order.status === 'shipping' || order.status === 'received' || order.status === 'completed'" @click="viewLogistics(order)">
					<view class="logistics-header">
						<uni-icons type="car" size="16" color="#67c23a"></uni-icons>
						<text class="logistics-status">{{ getLogisticsStatus(order) }}</text>
						<text class="logistics-detail">{{ getLogisticsDetail(order) }}</text>
					</view>
				</view>
			</view>

			<!-- 加载状态 -->
			<view class="loading-more" v-if="isLoading">
				<uni-icons type="spinner-cycle" size="24" color="#999"></uni-icons>
				<text>加载中...</text>
			</view>
			<view class="no-more" v-if="!hasMore && !isLoading && orderList.length > 0">
				<text>没有更多订单了</text>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				tabs: ['全部', '待付款', '待发货', '待收货', '评价'],
				currentTab: 0,
				orderList: [],
				allOrders: [], // 存储所有订单数据
				searchKeyword: '', // 搜索关键词
				page: 1,
				pageSize: 10,
				isLoading: false,
				hasMore: true,
				isRefreshing: false,
					userInfo: {}, // 用户信息
					reviewStats: { // 评价统计
						totalReviews: 0
					},
				reminderCooldown: 4 * 60 * 60 * 1000, // 4小时冷却时间
				// 订单状态映射
				statusMap: {
					pending: { text: '待付款', class: 'status-pending' },
					paid: { text: '待发货', class: 'status-paid' },
					shipping: { text: '待收货', class: 'status-shipping' },
					received: { text: '待评价', class: 'status-received' },
					completed: { text: '已完成', class: 'status-completed' },
					cancelled: { text: '已取消', class: 'status-cancelled' },
					refunded: { text: '退款成功', class: 'status-refunded' }
				}
			}
		},
		onLoad(options) {
			// 如果有状态参数，切换到对应标签
			if (options.status) {
				this.currentTab = parseInt(options.status)
			}
			this.loadUserInfo()
			this.loadReviewStats()
			this.loadOrders()

			// 监听订单更新事件
			uni.$on('orderUpdated', this.handleOrderUpdated)
		},

		onShow() {
			// 页面显示时刷新评价统计和订单数据
			this.loadReviewStats()
			// 强制刷新订单数据
			this.refreshOrders()
		},

		onUnload() {
			// 移除事件监听
			uni.$off('orderUpdated', this.handleOrderUpdated)
		},
		methods: {
			// 处理订单更新事件
			handleOrderUpdated(data) {
				console.log('收到订单更新事件:', data)
				// 刷新评价统计
				this.loadReviewStats()
				// 强制刷新订单数据
				this.refreshOrders()
			},

			// 切换标签
			switchTab(index) {
				if (this.currentTab === index) return
				this.currentTab = index
				this.page = 1
				this.orderList = []
				this.hasMore = true
				// 切换标签时清空搜索
				if (index !== 0) {
					this.searchKeyword = ''
				}
				this.loadOrders()
			},

			// 处理搜索
			handleSearch() {
				if (this.currentTab !== 0) return // 只在全部标签下搜索
				this.page = 1
				this.hasMore = true
				this.orderList = []
				this.loadOrders()
			},

			// 清空搜索
			clearSearch() {
				this.searchKeyword = ''
				this.handleSearch()
			},

			// 强制刷新订单数据
			refreshOrders() {
				// 重置分页状态
				this.page = 1
				this.hasMore = true
				this.isLoading = false
				this.isRefreshing = false

				// 强制重新加载
				this.loadOrders()
			},

			// 加载订单数据
			loadOrders() {
				if (this.isLoading || !this.hasMore) return
				this.isLoading = true

				// 从本地存储获取订单数据
				this.allOrders = uni.getStorageSync('orders') || []

				// 根据当前标签筛选订单
				let filteredOrders = this.allOrders
				if (this.currentTab !== 0) {
					const statusArray = ['pending', 'paid', 'shipping', 'received']
					if (this.currentTab === 4) {
						// 评价标签页：只显示待评价的订单（received状态且未评价）
						filteredOrders = this.allOrders.filter(order =>
							order.status === 'received' && !order.isReviewed
						)
					} else {
						// 其他标签页：按状态筛选
						filteredOrders = this.allOrders.filter(order => order.status === statusArray[this.currentTab - 1])
					}
				}

				// 搜索过滤（仅在全部标签下生效）
				if (this.currentTab === 0 && this.searchKeyword.trim()) {
					const keyword = this.searchKeyword.toLowerCase()
					filteredOrders = filteredOrders.filter(order => {
						// 搜索订单中的商品名称
						return order.products.some(product =>
							product.name.toLowerCase().includes(keyword)
						)
					})
				}

				// 分页处理
				const start = (this.page - 1) * this.pageSize
				const end = start + this.pageSize
				const pageOrders = filteredOrders.slice(start, end)

				// 更新列表数据
				if (this.page === 1) {
					this.orderList = pageOrders
				} else {
					this.orderList = [...this.orderList, ...pageOrders]
				}

				this.hasMore = end < filteredOrders.length
				this.isLoading = false
				this.isRefreshing = false
			},

			// 加载更多
			loadMore() {
				if (!this.hasMore) return
				this.page++
				this.loadOrders()
			},

			// 下拉刷新
			onRefresh() {
				this.isRefreshing = true
				this.page = 1
				this.hasMore = true
				this.loadOrders()
			},

			// 支付订单
			async payOrder(orderId) {
				const addresses = uni.getStorageSync('addresses') || [];
				if (!addresses.length) {
					uni.showModal({
						title: '提示',
						content: '您还没有收货地址，请填写。',
						confirmText: '去填写',
						success: (res) => {
							if (res.confirm) {
								uni.navigateTo({ url: '/pages/shopping/address/address' });
							}
						}
					});
					return;
				}
				uni.showModal({
					title: '确认支付',
					content: '是否确认支付该订单？',
					success: (res) => {
						if (res.confirm) {
							// 更新订单状态
							const orders = uni.getStorageSync('orders') || []
							const orderIndex = orders.findIndex(order => order.id === orderId)
							if (orderIndex !== -1) {
								orders[orderIndex].status = 'paid'
								orders[orderIndex].statusText = this.statusMap.paid.text
								orders[orderIndex].statusClass = this.statusMap.paid.class
								orders[orderIndex].payTime = new Date().toLocaleString()
								uni.setStorageSync('orders', orders)

								// 强制重新加载订单数据
								this.refreshOrders()

								uni.showToast({
									title: '支付成功',
									icon: 'success'
								})
							}
						}
					}
				})
			},

			// 确认收货
			confirmReceive(orderId) {
				uni.showModal({
					title: '确认收货',
					content: '是否确认收到商品？',
					success: (res) => {
						if (res.confirm) {
							const orders = uni.getStorageSync('orders') || []
							const orderIndex = orders.findIndex(order => order.id === orderId)
							
							if (orderIndex !== -1) {
								orders[orderIndex].status = 'received'
								orders[orderIndex].statusText = this.statusMap.received.text
								orders[orderIndex].statusClass = this.statusMap.received.class
								orders[orderIndex].receiveTime = new Date().toLocaleString()

								uni.setStorageSync('orders', orders)

								// 强制重新加载订单数据
								this.refreshOrders()

								uni.showToast({
									title: '确认收货成功',
									icon: 'success'
								})
							}
						}
					}
				})
			},

			// 评价订单
			goToReview(orderId) {
				// 跳转到评价页面
				uni.navigateTo({
					url: `/pages/shopping/appraise/photo?orderId=${orderId}`
				})
			},

			// 跳转到评价中心
			goToReviewCenter() {
				uni.navigateTo({
					url: '/pages/shopping/review/center'
				})
			},

			// 查看我的评价
			viewMyReview(orderId) {
				// 查找该订单的评价记录
				const userReviews = uni.getStorageSync('userReviews') || [];
				const review = userReviews.find(r => r.orderId === orderId);

				if (review) {
					// 跳转到评价详情页面，传递评价ID
					uni.navigateTo({
						url: `/pages/shopping/review/detail?reviewId=${review.id}`
					});
				} else {
					uni.showToast({
						title: '评价记录不存在',
						icon: 'none'
					});
				}
			},



			// 加载用户信息
			loadUserInfo() {
				this.userInfo = uni.getStorageSync('userInfo') || {
					nickname: '用户',
					avatar: '/static/images/profile.jpg'
				}
			},

			// 加载评价统计
			loadReviewStats() {
				const reviews = uni.getStorageSync('userReviews') || []
				this.reviewStats.totalReviews = reviews.length
			},

			// 取消订单
			cancelOrder(orderId) {
				uni.showModal({
					title: '取消订单',
					content: '确定要取消该订单吗？',
					success: (res) => {
						if (res.confirm) {
							const orders = uni.getStorageSync('orders') || []
							const orderIndex = orders.findIndex(order => order.id === orderId)
							
							if (orderIndex !== -1) {
								orders[orderIndex].status = 'cancelled'
								orders[orderIndex].statusText = this.statusMap.cancelled.text
								orders[orderIndex].statusClass = this.statusMap.cancelled.class
								orders[orderIndex].cancelTime = new Date().toLocaleString()
								
								// 如果已支付，则自动添加退款信息
								if (orders[orderIndex].payTime) {
									orders[orderIndex].status = 'refunded'
									orders[orderIndex].statusText = this.statusMap.refunded.text
									orders[orderIndex].statusClass = this.statusMap.refunded.class
									orders[orderIndex].refundTime = new Date().toLocaleString()
								}
								
								uni.setStorageSync('orders', orders)

								// 强制重新加载订单数据
								this.refreshOrders()

								uni.showToast({
									title: orders[orderIndex].payTime ? '退款成功' : '取消成功',
									icon: 'success'
								})
							}
						}
					}
				})
			},

			// 提醒发货
			remindShipping(order) {
				// 检查是否在冷却时间内
				if (order.lastRemindTime && this.isRemindDisabled(order.lastRemindTime)) {
					const remainingTime = this.getRemainingCooldownTime(order.lastRemindTime);
					uni.showToast({
						title: `${remainingTime}后可再次提醒`,
						icon: 'none'
					});
					return;
				}
				
				uni.showModal({
					title: '提醒发货',
					content: '确定要提醒商家发货吗？',
					success: (res) => {
						if (res.confirm) {
							// 更新订单的最后提醒时间
							const orders = uni.getStorageSync('orders') || [];
							const orderIndex = orders.findIndex(o => o.id === order.id);
							
							if (orderIndex !== -1) {
								orders[orderIndex].lastRemindTime = new Date().getTime();
								uni.setStorageSync('orders', orders);
								
								// 刷新订单列表
								this.loadOrders();
								
								uni.showToast({
									title: '已提醒商家发货',
									icon: 'success'
								});
							}
						}
					}
				});
			},
			
			// 检查是否在冷却时间内
			isRemindDisabled(lastRemindTime) {
				const now = new Date().getTime();
				return (now - lastRemindTime) < this.reminderCooldown;
			},
			
			// 获取剩余冷却时间的文字描述
			getRemainingCooldownTime(lastRemindTime) {
				const now = new Date().getTime();
				const remaining = this.reminderCooldown - (now - lastRemindTime);
				const hours = Math.floor(remaining / (60 * 60 * 1000));
				const minutes = Math.floor((remaining % (60 * 60 * 1000)) / (60 * 1000));

				if (hours > 0) {
					return `${hours}小时${minutes}分钟`;
				}
				return `${minutes}分钟`;
			},

			// 处理图片加载错误
			handleImageError(e) {
				console.log('图片加载失败:', e);
				// 可以设置默认图片
				// e.target.src = '/static/images/default-product.png';
			},

			// 测试功能：模拟发货
			testShipOrder(orderId) {
				uni.showModal({
					title: '模拟发货',
					content: '确定要模拟商家发货吗？（测试功能）',
					success: (res) => {
						if (res.confirm) {
							const orders = uni.getStorageSync('orders') || []
							const orderIndex = orders.findIndex(order => order.id === orderId)

							if (orderIndex !== -1) {
								orders[orderIndex].status = 'shipping'
								orders[orderIndex].statusText = this.statusMap.shipping.text
								orders[orderIndex].statusClass = this.statusMap.shipping.class
								orders[orderIndex].shipTime = new Date().toLocaleString()
								orders[orderIndex].trackingNumber = 'YT' + Date.now() // 模拟快递单号
								orders[orderIndex].showMore = false // 初始化更多操作状态

								uni.setStorageSync('orders', orders)

								// 强制重新加载订单数据
								this.refreshOrders()

								uni.showToast({
									title: '模拟发货成功',
									icon: 'success'
								})
							}
						}
					}
				})
			},

			// 切换更多操作
			showMoreActions(order) {
				const options = ['再次购买'];
				if (order.status === 'shipping') {
					options.push('延长收货');
				}
				if (order.status === 'received') {
					options.push('删除订单');
				}
				uni.showActionSheet({
					itemList: options,
					success: (res) => {
						if (res.tapIndex === 0) {
							this.buyAgain(order);
						} else if (order.status === 'shipping' && res.tapIndex === 1) {
							this.extendReceive(order.id);
						} else if (order.status === 'received' && ((order.status === 'shipping' && res.tapIndex === 2) || (order.status !== 'shipping' && res.tapIndex === 1))) {
							this.deleteOrder(order.id);
						}
					}
				});
			},

			// 申请售后
			applyAfterSale(orderId) {
				uni.showModal({
					title: '申请售后',
					content: '确定要申请售后服务吗？',
					success: (res) => {
						if (res.confirm) {
							// 这里可以跳转到售后申请页面
							uni.showToast({
								title: '跳转到售后页面',
								icon: 'none'
							})
							// uni.navigateTo({
							//     url: `/pages/shopping/afterSale/afterSale?orderId=${orderId}`
							// })
						}
					}
				})
			},

			// 查看物流
			viewLogistics(order) {
				if (!order.trackingNumber) {
					uni.showToast({
						title: '暂无物流信息',
						icon: 'none'
					})
					return
				}

				// 跳转到物流详情页
				uni.navigateTo({
					url: `/pages/shopping/logistics/logistics?trackingNumber=${order.trackingNumber}`
				})
			},

			// 再次购买
			buyAgain(order) {
				uni.showModal({
					title: '再次购买',
					content: '确定要将这些商品加入购物车吗？',
					success: (res) => {
						if (res.confirm) {
							// 将订单商品加入购物车
							const cart = uni.getStorageSync('cart') || []
							order.products.forEach(product => {
								const existingItem = cart.find(item => item.name === product.name)
								if (existingItem) {
									existingItem.count += product.count
								} else {
									cart.push({
										...product,
										id: Date.now() + Math.random(),
										selected: true
									})
								}
							})
							uni.setStorageSync('cart', cart)

							uni.showToast({
								title: '已加入购物车',
								icon: 'success'
							})
						}
					}
				})
			},

			// 延长收货
			extendReceive(orderId) {
				uni.showModal({
					title: '延长收货',
					content: '确定要延长收货时间吗？延长后将有额外7天确认收货时间。',
					success: (res) => {
						if (res.confirm) {
							const orders = uni.getStorageSync('orders') || []
							const orderIndex = orders.findIndex(order => order.id === orderId)

							if (orderIndex !== -1) {
								orders[orderIndex].extendReceive = true
								orders[orderIndex].extendTime = new Date().toLocaleString()
								uni.setStorageSync('orders', orders)
								this.loadOrders()

								uni.showToast({
									title: '延长收货成功',
									icon: 'success'
								})
							}
						}
					}
				})
			},

			// 获取物流状态
			getLogisticsStatus(order) {
				if (order.status === 'shipping') {
					return order.extendReceive ? '派件中（已延长收货）' : '派件中'
				} else if (order.status === 'received') {
					return '已签收'
				} else if (order.status === 'completed') {
					return '已完成'
				}
				return '运输中'
			},

			// 获取物流详情
			getLogisticsDetail(order) {
				const today = new Date().toLocaleDateString()
				if (order.status === 'shipping') {
					return `预计今天送达 | 极兔速递：【${order.trackingNumber || 'YT1234567890'}】`
				} else if (order.status === 'received') {
					return `已于 ${order.receiveTime || today} 签收 | 极兔速递：【${order.trackingNumber || 'YT1234567890'}】`
				} else if (order.status === 'completed') {
					return `交易完成 | 极兔速递：【${order.trackingNumber || 'YT1234567890'}】`
				}
				return ''
			},

			
			// 删除订单
			deleteOrder(orderId) {
				uni.showModal({
					title: '删除订单',
					content: '确定要删除该订单吗？',
					success: (res) => {
						if (res.confirm) {
							let orders = uni.getStorageSync('orders') || [];
							orders = orders.filter(order => order.id !== orderId);
							uni.setStorageSync('orders', orders);
							this.loadOrders();
							uni.showToast({
								title: '删除成功',
								icon: 'success'
							});
						}
					}
				});
			},

			// 跳转到商品详情页
			toProductDetail(id) {
				uni.navigateTo({
					url: `/pages/shopping/detail/detail?id=${id}`
				});
			}
		}
	}
</script>

<style lang="scss">
	.order-container {
		min-height: 100vh;
		background-color: #f8f8f8;
		display: flex;
		flex-direction: column;
	}

	.order-tabs {
		display: flex;
		background-color: #fff;
		padding: 10rpx 0;
		position: sticky;
		top: 0;
		z-index: 100;

		.tab-item {
			flex: 1;
			text-align: center;
			font-size: 28rpx;
			color: #666;
			position: relative;
			padding: 20rpx 0;

			&.active {
				color: #3ec6c6;
				font-weight: 500;

				&::after {
					content: '';
					position: absolute;
					bottom: 0;
					left: 50%;
					transform: translateX(-50%);
					width: 40rpx;
					height: 4rpx;
					background-color: #3ec6c6;
					border-radius: 2rpx;
				}
			}
		}
	}

	// 搜索框样式
	.search-container {
		background: #fff;
		padding: 20rpx 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
		position: sticky;
		top: 80rpx;
		z-index: 99;
		.search-box {
			display: flex;
			align-items: center;
			background: #f8f9fa;
			border-radius: 24rpx;
			padding: 16rpx 20rpx;
			position: relative;

			.search-input {
				flex: 1;
				font-size: 28rpx;
				color: #333;
				margin-left: 16rpx;
				background: transparent;
				border: none;
				outline: none;

				&::placeholder {
					color: #999;
				}
			}

			.clear-btn {
				margin-left: 16rpx;
				padding: 8rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}


	}

	// 我的评价模块样式
	.my-review-section {
		background: #fff;
		margin: 0 20rpx 20rpx;
		border-radius: 16rpx;
		box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.05);

		.review-header {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 32rpx 24rpx;
		}

		.review-info {
			display: flex;
			align-items: center;
			gap: 24rpx;
		}

		.user-avatar {
			width: 100rpx;
			height: 100rpx;
			border-radius: 50rpx;
			border: 2rpx solid #f0f0f0;
		}

		.user-detail {
			display: flex;
			flex-direction: column;
			gap: 8rpx;
		}

		.username {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
		}

		.review-stats {
			font-size: 24rpx;
			color: #999;
		}

		.review-action {
			display: flex;
			align-items: center;
			gap: 8rpx;
		}

		.action-text {
			font-size: 28rpx;
			color: #666;
		}
	}

	.order-list {
		flex: 1;
		padding: 20rpx;
	}

	.empty-order {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 200rpx 0;

		.empty-image {
			font-size: 200rpx;
			margin-bottom: 30rpx;
			color: rgba(255, 107, 129, 0.7);
		}

		.empty-text {
			font-size: 30rpx;
			color: rgba(138, 138, 138, 0.8);
			letter-spacing: 2rpx;
		}
	}

	.order-item {
		background-color: #fff;
		border-radius: 16rpx;
		margin-bottom: 20rpx;
		padding: 20rpx;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);

		.order-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding-bottom: 20rpx;
			border-bottom: 2rpx solid #f5f5f5;

			.order-time {
				font-size: 24rpx;
				color: #999;
			}

			.order-status {
				font-size: 28rpx;
				
				&.status-pending {
					color: #ff9900;
				}
				&.status-paid {
					color: #2979ff;
				}
				&.status-shipping {
					color: #0ac160;
				}
				&.status-received {
					color: #9c27b0;
				}
				&.status-completed {
					color: #8f8f8f;
				}
				&.status-cancelled {
					color: #999999;
				}
				&.status-refunded {
					color: #ff5722;
				}
			}
		}

		.order-content {
			display: flex;
			padding: 20rpx 30rpx;
			background: #fff;
			margin-bottom: 2rpx;
			
			.product-image {
				width: 160rpx;
				height: 160rpx;
				border-radius: 8rpx;
				margin-right: 20rpx;
				flex-shrink: 0;
			}
			
			.product-info {
				flex: 1;
				display: flex;
				flex-direction: column;
				
				.product-title {
					display: flex;
					justify-content: space-between;
					align-items: flex-start;
					margin-bottom: 12rpx;
					
					.product-name {
						flex: 1;
						font-size: 28rpx;
						color: #333;
						line-height: 1.4;
						margin-right: 20rpx;
						display: -webkit-box;
						-webkit-box-orient: vertical;
						-webkit-line-clamp: 2;
						line-clamp: 2;
						overflow: hidden;
					}
					
					.price-count {
						flex-shrink: 0;
						text-align: right;
						
						.price {
							font-size: 28rpx;
							color: #ff5555;
							font-weight: 500;
						}
						
						.count {
							font-size: 24rpx;
							color: #999;
							margin-left: 8rpx;
						}
					}
				}
				
				.product-spec {
					font-size: 24rpx;
					color: #999;
					margin-bottom: 12rpx;
				}
				
				.service-tags {
					display: flex;
					flex-wrap: wrap;
					gap: 8rpx;
					
					.tag {
						font-size: 22rpx;
						color: #67c23a;
						background: #f0f9eb;
						padding: 4rpx 12rpx;
						border-radius: 4rpx;
					}
				}
			}
		}

		.order-footer {
			display: flex;
			flex-direction: column;
			align-items: flex-end;
			justify-content: flex-end;
			border-top: 2rpx solid #f5f5f5;
			padding-top: 10rpx;
			.order-summary {
				width: 100%;
				display: flex;
				justify-content: flex-end;
				align-items: center;
				margin-bottom: 12rpx;
				.total-text {
					font-size: 26rpx;
					color: #666;
				}
				.total-price {
					font-size: 32rpx;
					color: #ff5555;
					font-weight: bold;
					margin-left: 8rpx;
				}
			}
			.order-actions {
				display: flex;
				justify-content: flex-end;
				gap: 20rpx;
				.action-btn {
					padding: 0 38rpx;
					height: 64rpx;
					line-height: 64rpx;
					font-size: 28rpx;
					border-radius: 36rpx;
					font-weight: 600;
					box-shadow: 0 2rpx 8rpx rgba(62,198,198,0.08);
					transition: box-shadow 0.2s, background 0.2s, color 0.2s;
					margin-right: 0;
					margin-left: 0;
					margin-bottom: 0;
					margin-top: 0;
					min-width: 120rpx;
					white-space: nowrap;
					text-overflow: initial;
					&:active {
						box-shadow: 0 1rpx 4rpx rgba(62,198,198,0.12);
						opacity: 0.92;
					}
					&.primary {
						color: #fff;
						background: #ff5555;
						border: none;
					}
					&.cancel, &.secondary, &.remind, &.review, &.test-ship, &.test-review {
						background: #fff;
						border: 2rpx solid #e0e0e0;
						color: #3ec6c6;
					}
					&.cancel {
						color: #999;
						border-color: #e0e0e0;
					}
					&.remind {
						color: #ff9900;
						border-color: #ffe0b2;
					}
					&.review {
						color: #9c27b0;
						border-color: #e1bee7;
					}
					&.reviewed {
						color: #666;
						border-color: #ddd;
						background: #f8f8f8;
					}
					&.test-ship {
						color: #3ec6c6;
						border-color: #b2ebf2;
					}
					&.test-review {
						color: #67c23a;
						border-color: #d0f5e0;
					}
					&[disabled] {
						color: #bbb !important;
						background: #f5f5f5 !important;
						border-color: #eee !important;
					}
				}
			}
		}

		.logistics-info {
			margin-top: 20rpx;
			padding: 24rpx 20rpx;
			background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
			border-radius: 12rpx;
			border-left: 4rpx solid #67c23a;
			position: relative;
			overflow: hidden;
			cursor: pointer;

			&::before {
				content: '';
				position: absolute;
				top: 0;
				right: 0;
				width: 60rpx;
				height: 60rpx;
				background: rgba(103, 194, 58, 0.1);
				border-radius: 0 12rpx 0 60rpx;
			}

			.logistics-header {
				display: flex;
				align-items: flex-start;
				gap: 12rpx;
				position: relative;
				z-index: 1;

				.logistics-status {
					font-size: 28rpx;
					color: #67c23a;
					font-weight: 600;
					white-space: nowrap;
				}

				.logistics-detail {
					font-size: 24rpx;
					color: #666;
					flex: 1;
					line-height: 1.4;
					margin-top: 2rpx;
				}
			}
		}
	}

	.loading-more, .no-more {
		text-align: center;
		padding: 30rpx;
		font-size: 24rpx;
		color: #999;
		display: flex;
		align-items: center;
		justify-content: center;

		uni-icons {
			margin-right: 10rpx;
		}
	}

	.order-card {
		background: #fff;
		border-radius: 18rpx;
		box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.04);
		margin: 24rpx 20rpx;
		padding: 28rpx 24rpx 20rpx 24rpx;
		font-size: 28rpx;
		.order-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 18rpx;
			.order-status {
				color: #ff9800;
				font-weight: bold;
				font-size: 30rpx;
			}
			.order-number {
				color: #bbb;
				font-size: 24rpx;
			}
		}
		.order-body {
			.order-info {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 10rpx;
				.order-total {
					color: #333;
					.price {
						color: #ff5555;
						font-weight: bold;
						font-size: 32rpx;
					}
				}
			}
			.order-logistics {
				color: #888;
				font-size: 26rpx;
				.logistics-number {
					color: #3ec6c6;
					font-weight: bold;
				}
			}
		}
		.order-actions {
			display: flex;
			justify-content: flex-end;
			margin-top: 18rpx;
			button {
				margin-left: 16rpx;
				border-radius: 32rpx;
				font-size: 26rpx;
				padding: 0 32rpx;
				height: 56rpx;
				line-height: 56rpx;
				border: none;
				&.btn-more {
					background: #f5f5f5;
					color: #666;
				}
				&.btn-normal {
					background: #fff;
					color: #3ec6c6;
					border: 1rpx solid #3ec6c6;
				}
				&.btn-confirm {
					background: linear-gradient(90deg, #3ec6c6, #36b3b3);
					color: #fff;
					font-weight: bold;
					box-shadow: 0 2rpx 8rpx rgba(62,198,198,0.12);
				}
			}
		}
	}

	.more-actions {
		display: flex;
		align-items: center;
		height: 60rpx;
		font-size: 26rpx;
		color: #666;
		
	}
</style>
