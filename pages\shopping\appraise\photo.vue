<template>
  <view class="photo-container">
    <view class="media-list">
      <view v-for="(item, idx) in mediaList" :key="idx" class="media-item">
        <image v-if="item.type === 'image'" :src="item.url" class="media-thumb" @click="previewMedia(idx)" />
        <video v-else :src="item.url" class="media-thumb" controls />
        <view class="media-delete" @click.stop="removeMedia(idx)"><uni-icons type="closeempty" size="20" color="#fff" /></view>
      </view>
      <view v-if="imageCount < 6" class="media-add" @click="chooseImage">
        <uni-icons type="camera" size="40" color="#bbb" />
        <text>还可上传{{6-imageCount}}张</text>
      </view>
      <view v-if="!hasVideo" class="media-add" @click="chooseVideo">
        <uni-icons type="videocam" size="40" color="#bbb" />
        <text>添加短视频</text>
      </view>
    </view>
    <view class="photo-bottom">
      <button class="skip-btn" @click="skipToAppraise">暂不拍摄，先去评分</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      mediaList: [], // {type: 'image'|'video', url: ''}
      orderId: '',
      productId: ''
    }
  },

  onLoad(options) {
    // 接收订单和商品信息
    this.orderId = options.orderId || '';
    this.productId = options.productId || '';
  },
  computed: {
    imageCount() {
      return this.mediaList.filter(m => m.type === 'image').length;
    },
    hasVideo() {
      return this.mediaList.some(m => m.type === 'video');
    }
  },
  methods: {
    closePage() {
      uni.navigateBack();
    },
    chooseImage() {
      const remain = 6 - this.imageCount;
      uni.chooseImage({
        count: remain,
        success: (res) => {
          const imgs = res.tempFilePaths.map(url => ({type: 'image', url}));
          this.mediaList = this.mediaList.concat(imgs).slice(0, 6);
        }
      });
    },
    chooseVideo() {
      if (this.hasVideo) return;
      uni.chooseVideo({
        maxDuration: 30,
        success: (res) => {
          this.mediaList.push({type: 'video', url: res.tempFilePath});
        }
      });
    },
    removeMedia(idx) {
      this.mediaList.splice(idx, 1);
    },
    previewMedia(idx) {
      const item = this.mediaList[idx];
      if (item.type === 'image') {
        uni.previewImage({
          urls: this.mediaList.filter(m=>m.type==='image').map(m=>m.url),
          current: item.url
        });
      } else if (item.type === 'video') {
        uni.previewMedia({
          sources: [{url: item.url, type: 'video'}],
          current: 0
        });
      }
    },
    skipToAppraise() {
      // 构建跳转参数
      let url = `/pages/shopping/appraise/appraise?mediaList=${encodeURIComponent(JSON.stringify(this.mediaList))}&fromPhotoPage=true`;

      if (this.orderId) {
        url += `&orderId=${this.orderId}`;
      }
      if (this.productId) {
        url += `&productId=${this.productId}`;
      }

      uni.navigateTo({
        url: url
      });
    }
  }
}
</script>

<style scoped>
.photo-container {
  background: #fff;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.photo-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
}
.media-list {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
  padding: 32rpx 24rpx 0 24rpx;
}
.media-item {
  position: relative;
}
.media-thumb {
  width: 180rpx;
  height: 180rpx;
  border-radius: 12rpx;
  object-fit: cover;
}
.media-delete {
  position: absolute;
  top: 0;
  right: 0;
  background: rgba(0,0,0,0.5);
  border-radius: 50%;
  padding: 6rpx;
}
.media-add {
  width: 180rpx;
  height: 180rpx;
  border: 2rpx dashed #bbb;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #bbb;
  font-size: 26rpx;
  gap: 8rpx;
}
.photo-bottom {
  margin-top: auto;
  padding: 40rpx 24rpx;
}
.skip-btn {
  width: 100%;
  background: #f5f5f5;
  color: #ff5555;
  border-radius: 32rpx;
  font-size: 30rpx;
  font-weight: bold;
  padding: 24rpx 0;
  border: none;
}
</style>