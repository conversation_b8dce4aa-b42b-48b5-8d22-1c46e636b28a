<template>
  <view class="report-container">
    <!-- 头部区域 -->
    <view class="header-section">
      <view class="report-title">舌象分析报告</view>
      <view class="report-subtitle">本报告由AI生成，仅供参考，最终诊断请以医生意见为准。</view>
    </view>

    <!-- 舌象图片区域 -->
    <view class="photo-section">
      <!-- 舌诊示意图 -->
      <view class="tongue-diagram">
        <view class="diagram-container">
          <!-- 使用 image.png 作为背景示意图 -->
          <image src="/static/images/image.png" class="tongue-diagram-bg" mode="aspectFit" />

          <!-- 实际舌象图片位置 - 右下角圆形 -->
          <view class="actual-photo-container" @click="viewOriginalPhoto">
            <image
              v-if="photo"
              :src="photo"
              class="actual-photo"
              mode="aspectFit"
              @load="onImageLoad"
              @error="onImageError"
            />
            <view class="photo-placeholder" v-else>
              <!-- <view class="placeholder-icon">📷</view> -->
              <view class="placeholder-text" v-if="!loading">舌象图片</view>
              <view class="placeholder-text" v-else>分析中...</view>
            </view>
            <view class="view-original-hint">点击查看分析图</view>
          </view>
        </view>
      </view>
    </view>

    <view v-if="loading" class="loading">
      <view class="loading-icon"></view>
      <view class="loading-text">分析中...</view>
    </view>

    <view v-else class="content-section">
      <!-- 舌象特征分析与描述合并模块 -->
      <view class="tongue-feature-block">
        <view class="section-title">
           <view class="section-bar"></view> 
          <view class="section-text">舌象特征分析</view>
        </view>
        <view class="feature-table">
          <view class="feature-row feature-row-desc" v-for="item in filteredFeatureList" :key="item.label">
            <text class="feature-label">{{ item.label }}</text>
            <text class="feature-value">{{ item.value }}</text>
            <text class="feature-desc" v-if="getFeatureDesc(item)">{{ getFeatureDesc(item) }}</text>
          </view>
        </view>
      </view>

      <!-- 体质分析 -->
      <view class="constitution-block">
        <view class="section-title">
          <view class="section-bar"></view>
          <view class="section-text">健康状态分析</view>
        </view>
        <view class="constitution-card">
          <view class="constitution-type">{{ getConstitutionTypeDisplay() }}</view>
        </view>
        <view class="constitution-performance" v-if="getConstitutionInfo('Performance')">
          <view class="performance-title">证候表现</view>
          <view class="performance-content">{{ getConstitutionInfo('Performance') }}</view>
        </view>
      </view>

      <!-- 养生方案 -->
      <view class="wellness-block">
        <view class="section-title">
          <view class="section-bar"></view>
          <view class="section-text">养生方案</view>
        </view>
        <view class="wellness-content">
          <view v-if="getConstitutionInfo('Diet')" class="wellness-item">
            <view class="wellness-item-title-row">
              <view class="wellness-item-bar"></view>
              <view class="wellness-title">饮食调养</view>
            </view>
            <view class="wellness-desc">{{ getConstitutionInfo('Diet') }}</view>
          </view>
          <view v-if="getConstitutionInfo('Exercise')" class="wellness-item">
            <view class="wellness-item-title-row">
              <view class="wellness-item-bar"></view>
              <view class="wellness-title">运动养生</view>
            </view>
            <view class="wellness-desc">{{ getConstitutionInfo('Exercise') }}</view>
          </view>
          <view v-if="getConstitutionInfo('Life')" class="wellness-item">
            <view class="wellness-item-title-row">
              <view class="wellness-item-bar"></view>
              <view class="wellness-title">生活起居</view>
            </view>
            <view class="wellness-desc">{{ getConstitutionInfo('Life') }}</view>
          </view>
          <view v-if="getConstitutionInfo('Massage_Moxibustion')" class="wellness-item">
            <view class="wellness-item-title-row">
              <view class="wellness-item-bar"></view>
              <view class="wellness-title">按摩调理</view>
            </view>
            <view class="wellness-desc">{{ getConstitutionInfo('Massage_Moxibustion') }}</view>
          </view>
          <view v-if="getConstitutionInfo('Cupping_Scraping')" class="wellness-item">
            <view class="wellness-item-title-row">
              <view class="wellness-item-bar"></view>
              <view class="wellness-title">拔罐刮痧</view>
            </view>
            <view class="wellness-desc">{{ getConstitutionInfo('Cupping_Scraping') }}</view>
          </view>
          <view v-if="getConstitutionInfo('rhymes')" class="wellness-item">
            <view class="wellness-item-title-row">
              <view class="wellness-item-bar"></view>
              <view class="wellness-title">五韵调元</view>
            </view>
            <view class="wellness-desc">{{ getConstitutionInfo('rhymes') }}</view>
          </view>
        </view>
      </view>

      <!-- 节气养生 -->
      <view class="card-group" v-if="getSeasonalInfo()">
        <view class="section-title">
          <view class="section-bar"></view>
          <view class="section-text">节气养生</view>
        </view>
        <view class="seasonal-content">
          <view class="seasonal-header">
            <view class="seasonal-term">{{ getSolarTerms() }}</view>
            <view class="seasonal-time">当前节气养生建议</view>
          </view>
          <view class="seasonal-advice">{{ getSeasonalInfo() }}</view>
        </view>
      </view>

      <!-- 分析时间 -->
      <view class="footer-info">
        <view class="analysis-time">分析时间：{{ getAnalysisTime() }}</view>
        <view class="disclaimer">*本报告仅供参考，具体诊断请咨询专业医师</view>
      </view>
    </view>
  </view>
</template>

<script>
import config from '@/config'
import { getToken, handleTokenExpired, getTokenExpiredHandlingStatus } from '@/utils/auth'
export default {
  data() {
    return {
      result: {},
      loading: false,
      photo: '', // 显示用的图片（可能是本地或服务器图片）
      serverAnalysisImage: '', // 服务器分析后的图片（用于大图预览）
      analysisTime: '' // 分析时间
    }
  },
  watch: {
    photo(newVal, oldVal) {
      console.log('photo 变量变化:', {
        oldVal,
        newVal,
        hasValue: !!newVal,
        length: newVal ? newVal.length : 0
      });
    }
  },
  onLoad(options) {
    console.log('舌诊报告页面接收到的参数:', options)

    if (options && options.result) {
      // 优先使用传递的分析结果，避免重复API调用
      let parsed = null
      try {
        parsed = JSON.parse(decodeURIComponent(options.result))
        this.result = parsed
        console.log('后端返回的完整数据报告:', parsed)
      } catch (e) {
        console.error('解析结果数据失败:', e)
      }

      // 处理图片URL - 支持多种数据结构
      let imageUrl = null

      // 1. 直接在根级别的 image_A（优先使用分析后的图片）
      if (parsed && parsed.image_A) {
        imageUrl = parsed.image_A
        console.log('使用根级别 image_A:', imageUrl)
      }

      // 设置图片URL
      if (imageUrl) {
        this.photo = imageUrl
        this.serverAnalysisImage = imageUrl
        console.log('最终设置的图片URL:', imageUrl)
      } else {
        console.warn('未找到有效的图片URL')
      }

      // 提取分析时间
      this.extractAnalysisTime(parsed)
    }

    // 处理单独传递的图片参数
    if (options && options.photo) {
      const photoUrl = decodeURIComponent(options.photo)
      console.log('接收到单独的图片参数:', photoUrl)

      if (!this.photo) {
        // 如果还没有设置图片，使用传递的图片
        this.photo = photoUrl
        this.serverAnalysisImage = photoUrl
        console.log('使用传递的图片URL:', photoUrl)
      }

      // 如果没有结果数据，需要重新分析
      if (!options.result) {
        console.log('没有结果数据，开始重新分析图片')
        this.analyzePhoto(photoUrl)
      }
    }

    // 处理单独传递的时间参数
    if (options && (options.analysis_time || options.request_time || options.creatime)) {
      const timeValue = options.analysis_time || options.request_time || options.creatime
      if (timeValue) {
        this.analysisTime = this.formatTime(decodeURIComponent(timeValue))
        console.log('接收到单独的时间参数:', this.analysisTime)
      }
    }

    // 如果还没有设置分析时间，使用当前时间
    if (!this.analysisTime) {
      this.analysisTime = this.getCurrentTime()
      console.log('使用当前时间作为分析时间:', this.analysisTime)
    }
  },
  methods: {
    async analyzePhoto(localPhotoPath) {
      this.loading = true
      try {
        // 获取用户信息
        const userInfo = uni.getStorageSync('User-Informations') || {}

        // 直接使用 uni.uploadFile 进行文件上传
        const uploadResult = await new Promise((resolve, reject) => {
          uni.uploadFile({
            timeout: 20000,
            url: config.baseUrl + '/Moudles/uploadAPP',
            filePath: localPhotoPath,
            name: 'file',
            header: {
              'Authorization': 'Bearer ' + getToken()
            },
            formData: {
              // 使用用户填写的真实信息
              TimeStamp: userInfo.TimeStamp || new Date().getTime(),
              person_name: userInfo.name || '未知',
              sex: userInfo.sex || '未知',
              dateBirth: userInfo.dateBirth || '',
              phone: userInfo.phonenumber || '',
              occupation: userInfo.occupation || '',
              maritalStatus: userInfo.maritalStatus || '',
              height: userInfo.height || '',
              weight: userInfo.weight || '',
              age: userInfo.age || 0,
              diastolicPressure: userInfo.diastolicPressure || '',
              systolicPressure: userInfo.systolicPressure || '',
              allergy: userInfo.allergy || '',
              medicalHistory: userInfo.medicalHistory || '',
              UserseeionType:3
            },
            success: (response) => {
              console.log("上传后返回的结果：", response);
              try {
                const res = JSON.parse(response.data)
                console.log("解析后的结果：", res);
                console.log("res.code的类型和值：", typeof res.code, res.code);

                // 优先检查token过期情况 - 使用宽松比较
                if (res.code == 401 || res.code === 401) {
                  console.log("检测到401状态码，token过期")
                  uni.hideLoading()

                  // 检查是否已在处理token过期，避免重复弹窗
                  if (!getTokenExpiredHandlingStatus()) {
                    console.log("调用统一的token过期处理方法")
                    handleTokenExpired()
                  } else {
                    console.log("Token过期处理已在进行中，跳过重复处理")
                  }

                  return
                }

                // 检查是否是舌体检测失败的特殊情况
                if (res.msg && (res.msg.includes('未检测到舌体') || res.msg.includes('检测失败'))) {
                  reject(new Error('TONGUE_NOT_DETECTED'))
                  return
                }

                if (res.code === 500) {
                  reject(new Error(res.msg || '服务器内部错误'))
                  return
                }

                if (res.code !== 200) {
                  reject(new Error(res.msg || '请求失败'))
                  return
                }

                if (!res.externalResponse) {
                  reject(new Error('TONGUE_NOT_DETECTED'))
                  return
                }

                const externalResponse = res.externalResponse
                const hasValidTongueData = (externalResponse.tongue_color && externalResponse.tongue_color.trim() !== '') ||
                                         (externalResponse.tongue_shape && externalResponse.tongue_shape.trim() !== '') ||
                                         (externalResponse.fur_color && externalResponse.fur_color.trim() !== '') ||
                                         (externalResponse.fur_shape && externalResponse.fur_shape.trim() !== '') ||
                                         (externalResponse.type_name && externalResponse.type_name.trim() !== '')

                if (!hasValidTongueData) {
                  reject(new Error('TONGUE_NOT_DETECTED'))
                  return
                }

                resolve(res)
              } catch (e) {
                reject(new Error('解析响应数据失败'))
              }
            },
            fail: (error) => {
              console.log("上传失败：", error);
              reject(error)
            }
          })
        })
        this.result = uploadResult.data || uploadResult
        console.log('后端分析数据:', this.result)

        // 设置服务器分析图片
        if (this.result && this.result.externalResponse && this.result.externalResponse.image_A) {
          this.serverAnalysisImage = this.result.externalResponse.image_A
          this.photo = this.result.externalResponse.image_A
        }
      } catch (e) {
        console.error('分析失败:', e)

        // 检查是否是舌体检测失败
        if (e.message === 'TONGUE_NOT_DETECTED') {
          uni.showModal({
            title: '分析失败',
            content: '未检测到舌体，请重新拍摄或上传图片',
            showCancel: false,
            confirmText: '重新拍摄',
            success: () => {
              // 重新定向到舌诊拍摄页面
              uni.redirectTo({
                url: '/pages/gather/diagnosis/diagnosis'
              })
            }
          })
        } else {
          uni.showToast({ title: '分析失败', icon: 'none' })
        }
      } finally {
        this.loading = false
      }
    },
    // 获取数据值 - 支持多种数据结构
    getExternalValue(key) {
      if (!this.result) return '无';

      let value = null;

      // 1. 优先从 externalResponse 中获取
      if (this.result.externalResponse && this.result.externalResponse[key]) {
        value = this.result.externalResponse[key];
      }

      // 2. 从根级别获取
      if (!value && this.result[key]) {
        value = this.result[key];
      }

      // 3. 从 data 字段获取（如果存在）
      if (!value && this.result.data && this.result.data[key]) {
        value = this.result.data[key];
      }

      // 4. 从 result 字段获取（如果存在嵌套结构）
      if (!value && this.result.result && this.result.result[key]) {
        value = this.result.result[key];
      }

      // 5. 特殊处理一些字段的别名
      if (!value) {
        const aliasMap = {
          'type_name': ['type', 'constitution_type', 'constitution_name', 'secondary'],
          'tongue_color': ['tongue_colour', 'color'],
          'tongue_shape': ['shape'],
          'fur_color': ['fur_colour', 'coating_color'],
          'fur_shape': ['coating_shape', 'coating_texture'],
          'greasy_fur': ['greasy', 'greasy_coating'],
          'peel_fur': ['peel', 'peeling'],
          'crack': ['cracks', 'fissure'],
          'indentation': ['tooth_mark', 'teeth_mark'],
          'secondary': ['type_name', 'constitution_type']
        };

        const aliases = aliasMap[key];
        if (aliases) {
          for (const alias of aliases) {
            // 从各个可能的位置查找别名
            if (this.result.externalResponse && this.result.externalResponse[alias]) {
              value = this.result.externalResponse[alias];
              break;
            }
            if (this.result[alias]) {
              value = this.result[alias];
              break;
            }
            if (this.result.data && this.result.data[alias]) {
              value = this.result.data[alias];
              break;
            }
            if (this.result.result && this.result.result[alias]) {
              value = this.result.result[alias];
              break;
            }
          }
        }
      }

      // 返回有效值或默认值
      if (value !== null && value !== undefined && value !== '') {
        return value;
      }

      return '无';
    },

    // 获取当前时间
    getCurrentTime() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    },

    // 提取分析时间
    extractAnalysisTime(data) {
      if (!data) {
        this.analysisTime = this.getCurrentTime()
        return
      }

      // 尝试从多个可能的字段中提取时间
      let timeValue = null

      // 1. 直接在根级别的时间字段
      timeValue = data.analysis_time || data.request_time || data.creatime || data.createTime

      // 2. 在 externalResponse 中的时间字段
      if (!timeValue && data.externalResponse) {
        timeValue = data.externalResponse.analysis_time ||
                   data.externalResponse.request_time ||
                   data.externalResponse.creatime
      }

      // 3. 在 data 中的时间字段
      if (!timeValue && data.data) {
        timeValue = data.data.analysis_time ||
                   data.data.request_time ||
                   data.data.creatime
      }

      if (timeValue) {
        this.analysisTime = this.formatTime(timeValue)
        console.log('提取到的分析时间:', this.analysisTime)
      } else {
        this.analysisTime = this.getCurrentTime()
        console.log('未找到分析时间，使用当前时间:', this.analysisTime)
      }
    },

    // 格式化时间
    formatTime(timeValue) {
      if (!timeValue) return this.getCurrentTime()

      try {
        let date

        // 如果是时间戳（数字）
        if (typeof timeValue === 'number') {
          date = new Date(timeValue)
        }
        // 如果是字符串
        else if (typeof timeValue === 'string') {
          // 处理各种时间格式
          if (timeValue.includes('T') || timeValue.includes('-')) {
            date = new Date(timeValue)
          } else {
            // 可能是时间戳字符串
            const timestamp = parseInt(timeValue)
            if (!isNaN(timestamp)) {
              date = new Date(timestamp)
            } else {
              date = new Date(timeValue)
            }
          }
        }
        // 如果已经是Date对象
        else if (timeValue instanceof Date) {
          date = timeValue
        }
        else {
          throw new Error('无法识别的时间格式')
        }

        // 检查日期是否有效
        if (isNaN(date.getTime())) {
          throw new Error('无效的日期')
        }

        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        const hours = String(date.getHours()).padStart(2, '0')
        const minutes = String(date.getMinutes()).padStart(2, '0')

        return `${year}-${month}-${day} ${hours}:${minutes}`
      } catch (error) {
        console.error('时间格式化失败:', error, '原始值:', timeValue)
        return this.getCurrentTime()
      }
    },

    // 获取分析时间（优先使用传入的时间，否则使用当前时间）
    getAnalysisTime() {
      return this.analysisTime || this.getCurrentTime()
    },

    // 查看分析图 - 只使用服务器分析图片
    viewOriginalPhoto() {
      // 只使用服务器分析后的图片（image_A）
      if (this.serverAnalysisImage) {
        console.log('预览分析图片URL:', this.serverAnalysisImage);
        uni.previewImage({
          urls: [this.serverAnalysisImage],
          current: this.serverAnalysisImage
        });
      } else if (this.photo) {
        console.log('预览当前显示图片URL:', this.photo);
        uni.previewImage({
          urls: [this.photo],
          current: this.photo
        });
      } else {
        uni.showToast({
          title: '暂无分析图片可预览',
          icon: 'none'
        });
      }
    },

    // 图片加载成功事件
    onImageLoad(e) {
      console.log('舌诊图片加载成功:', this.photo);
      console.log('图片加载事件详情:', e);
    },

    // 图片加载失败事件
    onImageError(e) {
      console.error('舌诊图片加载失败:', this.photo);
      console.error('图片加载错误详情:', e);
      uni.showToast({
        title: '图片加载失败',
        icon: 'none'
      });
    },

    // 获取特征详细描述 - 支持多种数据结构
    getFeatureDescriptions() {
      const descriptions = {};

      if (!this.result) {
        console.log('result 数据不存在');
        return descriptions;
      }

      // 1. 从 getanalysis 获取（新格式）
      if (this.result.getanalysis) {
        const analysis = this.result.getanalysis;
        Object.keys(analysis).forEach(key => {
          if (key !== '__ob__' && typeof analysis[key] === 'string' && analysis[key].trim() !== '') {
            descriptions[key] = analysis[key];
          }
        });
      }

      // 2. 从 externalResponse.getanalysis 获取
      if (this.result.externalResponse && this.result.externalResponse.getanalysis) {
        const analysis = this.result.externalResponse.getanalysis;
        Object.keys(analysis).forEach(key => {
          if (key !== '__ob__' && typeof analysis[key] === 'string' && analysis[key].trim() !== '' && !descriptions[key]) {
            descriptions[key] = analysis[key];
          }
        });
        console.log('从 externalResponse.getanalysis 补充描述');
      }

      // 3. 从根级别的分析字段获取（历史数据可能的格式）
      const analysisFields = ['analysis', 'description', 'details', 'feature_analysis'];
      for (const field of analysisFields) {
        if (this.result[field] && typeof this.result[field] === 'object') {
          Object.keys(this.result[field]).forEach(key => {
            const value = this.result[field][key];
            if (typeof value === 'string' && value.trim() !== '' && !descriptions[key]) {
              descriptions[key] = value;
            }
          });
        }
      }

      // 4. 从 data 字段获取
      if (this.result.data && this.result.data.getanalysis) {
        const analysis = this.result.data.getanalysis;
        Object.keys(analysis).forEach(key => {
          if (key !== '__ob__' && typeof analysis[key] === 'string' && analysis[key].trim() !== '' && !descriptions[key]) {
            descriptions[key] = analysis[key];
          }
        });
      }
      return descriptions;
    },

    // 获取体质信息 - 支持多种数据结构
    getConstitutionInfo(key) {
      if (!this.result) return '';

      let value = '';

      // 1. 从 constitution 对象获取
      if (this.result.constitution && this.result.constitution[key]) {
        value = this.result.constitution[key];
      }

      // 2. 从 externalResponse.constitution 获取
      if (!value && this.result.externalResponse && this.result.externalResponse.constitution && this.result.externalResponse.constitution[key]) {
        value = this.result.externalResponse.constitution[key];
      }

      // 3. 从根级别直接获取（某些历史数据可能直接存储在根级别）
      if (!value && this.result[key]) {
        value = this.result[key];
      }

      // 4. 从 data 字段获取
      if (!value && this.result.data && this.result.data[key]) {
        value = this.result.data[key];
      }

      // 5. 特殊字段映射
      if (!value) {
        const fieldMap = {
          'Diet': ['diet', 'dietary', 'food_advice', 'diet_advice'],
          'Exercise': ['exercise', 'sport', 'movement', 'exercise_advice'],
          'Life': ['life', 'lifestyle', 'living', 'life_advice'],
          'Performance': ['performance', 'symptoms', 'manifestation', 'clinical_manifestation'],
          'Message_Motivation': ['message_motivation', 'motivation', 'message'],
          'Rhymes': ['rhymes', 'poem', 'verse']
        };

        const alternatives = fieldMap[key];
        if (alternatives) {
          for (const alt of alternatives) {
            // 从各个可能的位置查找
            if (this.result.constitution && this.result.constitution[alt]) {
              value = this.result.constitution[alt];
              break;
            }
            if (this.result.externalResponse && this.result.externalResponse.constitution && this.result.externalResponse.constitution[alt]) {
              value = this.result.externalResponse.constitution[alt];
              break;
            }
            if (this.result[alt]) {
              value = this.result[alt];
              break;
            }
            if (this.result.data && this.result.data[alt]) {
              value = this.result.data[alt];
              break;
            }
          }
        }
      }

      return value || '';
    },

    // 获取体质类型显示文本
    getConstitutionTypeDisplay() {
      const typeName = this.getExternalValue('type_name');
      const secondary = this.getExternalValue('secondary');
      const type = this.getExternalValue('type');

      let display = '';

      // 优先使用 type_name
      if (typeName && typeName !== '无') {
        display = typeName;
      } else if (type && type !== '无') {
        display = type;
      }

      // 如果有 secondary 字段且不同于主类型，则添加
      if (secondary && secondary !== '无' && secondary !== display) {
        if (display) {
          display += ` ${secondary}`;
        } else {
          display = secondary;
        }
      }

      // 如果都没有，返回默认值
      if (!display || display === '无') {
        display = '体质分析';
      }

      console.log('体质类型显示:', display, '原始数据:', { typeName, secondary, type });
      return display;
    },


    // 获取节气信息
    getSeasonalInfo() {
      if (this.result && this.result.solar) {
        return this.result.solar;
      }
      return '';
    },

    // 获取节气名称
    getSolarTerms() {
      if (this.result && this.result.solarterms) {
        return this.result.solarterms;
      }
      return '当前节气';
    },

   
    getFeatureDesc(item) {
      // 优先用值查描述，否则用label查
      const descMap = this.getFeatureDescriptions();

      // 建立字段映射关系，将后端字段名映射到前端描述字段名
      const fieldMapping = {
        // 舌色映射 - 更全面的映射
        '淡红': '淡红舌',
        '淡白': '淡白舌',
        '红': '红舌',
        '绛': '绛舌',
        '紫': '紫舌',
        '青': '青舌',
        '淡红舌': '淡红舌',
        '淡白舌': '淡白舌',
        '红舌': '红舌',
        '绛舌': '绛舌',
        '紫舌': '紫舌',
        '青舌': '青舌',

        // 舌形映射
        '苍老': '苍老舌',
        '娇嫩': '娇嫩舌',
        '胖大': '胖大舌',
        '瘦薄': '瘦薄舌',
        '点刺': '点刺舌',
        '裂纹': '裂纹舌',
        '齿痕': '齿痕舌',
        '苍老舌': '苍老舌',
        '娇嫩舌': '娇嫩舌',
        '胖大舌': '胖大舌',
        '瘦薄舌': '瘦薄舌',
        '点刺舌': '点刺舌',
        '裂纹舌': '裂纹舌',
        '齿痕舌': '齿痕舌',

        // 苔色映射
        '白苔': '白苔',
        '黄苔': '黄苔',
        '灰苔': '灰黑苔',
        '黑苔': '灰黑苔',
        '灰黑苔': '灰黑苔',
        '白': '白苔',
        '黄': '黄苔',
        '灰': '灰黑苔',
        '黑': '灰黑苔',
        '灰黑': '灰黑苔',

        // 苔质映射
        '薄': '薄苔',
        '厚': '厚苔',
        '润': '润苔',
        '滑': '滑苔',
        '燥': '燥苔',
        '糙': '糙苔',
        '腻': '腻苔',
        '腐': '腐苔',
        '剥': '剥苔',
        '薄苔': '薄苔',
        '厚苔': '厚苔',
        '润苔': '润苔',
        '滑苔': '滑苔',
        '燥苔': '燥苔',
        '糙苔': '糙苔',
        '腻苔': '腻苔',
        '腐苔': '腐苔',
        '剥苔': '剥苔',

        // 其他特征映射
        '有': '有',
        '无': '无'
      };

      // 尝试多种方式查找描述
      let description = '';

      // 1. 直接用值查找
      description = descMap[item.value];
      if (description) {
        return description;
      }

      // 2. 用映射后的值查找
      const mappedValue = fieldMapping[item.value];
      if (mappedValue) {
        description = descMap[mappedValue];
        if (description) {
          console.log(`找到描述(映射值匹配): ${description.substring(0, 50)}...`);
          return description;
        }
      }

      // 3. 用label查找
      description = descMap[item.label];
      if (description) {
        return description;
      }

      // 4. 用映射后的label查找
      const mappedLabel = fieldMapping[item.label];
      if (mappedLabel) {
        description = descMap[mappedLabel];
        if (description) {
          return description;
        }
      }

      // 5. 模糊匹配：在描述字段中查找包含当前值的项
      for (const [key, desc] of Object.entries(descMap)) {
        if (key.includes(item.value) || item.value.includes(key)) {
          return desc;
        }
      }

      // 6. 智能匹配：去除常见后缀再匹配
      const cleanValue = item.value.replace(/[舌苔]$/, ''); // 去除末尾的"舌"或"苔"
      const cleanLabel = item.label.replace(/[舌苔]$/, '');

      for (const [key, desc] of Object.entries(descMap)) {
        const cleanKey = key.replace(/[舌苔]$/, '');
        if (cleanKey === cleanValue || cleanKey === cleanLabel) {
          console.log(`找到描述(智能匹配): ${desc.substring(0, 50)}...`);
          return desc;
        }
      }

      // 7. 最后尝试：检查是否是组合特征（如"薄白苔"）
      if (item.value.length > 1) {
        // 尝试拆分组合特征
        const possibleKeys = [];

        // 对于苔相关的特征
        if (item.label === '苔色' || item.label === '苔质') {
          // 尝试提取苔的颜色和质地
          const colors = ['白', '黄', '灰', '黑', '灰黑'];
          const qualities = ['薄', '厚', '润', '滑', '燥', '糙', '腻', '腐', '剥'];

          for (const color of colors) {
            if (item.value.includes(color)) {
              possibleKeys.push(color + '苔');
            }
          }

          for (const quality of qualities) {
            if (item.value.includes(quality)) {
              possibleKeys.push(quality + '苔');
            }
          }
        }

        // 尝试用拆分出的关键词查找
        for (const possibleKey of possibleKeys) {
          if (descMap[possibleKey]) {
            console.log(`找到描述(组合特征匹配): ${descMap[possibleKey].substring(0, 50)}...`);
            return descMap[possibleKey];
          }
        }
      }

      console.log(`未找到描述 - label: ${item.label}, value: ${item.value}`);
      return '';
    }
  },
  computed: {
    featureList() {
      return [
        { label: '舌色', value: this.getExternalValue('tongue_color') },
        { label: '舌形', value: this.getExternalValue('tongue_shape') },
        { label: '裂纹', value: this.getExternalValue('crack') },
        { label: '齿痕', value: this.getExternalValue('indentation') },
        { label: '苔色', value: this.getExternalValue('fur_color') },
        { label: '苔质', value: this.getExternalValue('fur_shape') },
        { label: '腻苔', value: this.getExternalValue('greasy_fur') },
        { label: '剥苔', value: this.getExternalValue('peel_fur') },
      ];
    },
    filteredFeatureList() {
      // 只显示有值且不为"无"的特征
      return this.featureList.filter(item => item.value && item.value !== '无');
    }
  }
}
</script>

<style scoped>
.report-container {
  min-height: 100vh;
  background: #fff;
  padding-bottom: 40rpx;
  font-size: 32rpx;
}

/* 头部区域 */
.header-section {
  background: #ffffff;
  padding: 60rpx 32rpx 40rpx;
  text-align: center;
  border-bottom: 1rpx solid #e9ecef;
}

.report-title {
  font-size: 44rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  color: #2c3e50;
}

.report-subtitle {
  font-size: 26rpx;
  color: #6c757d;
  font-weight: 400;
}

/* 图片区域 */
.photo-section {
  padding: 40rpx 32rpx;
  background: white;
}

/* 舌诊示意图 */
.tongue-diagram {
  display: flex;
  justify-content: center;
  padding: 20rpx;
}

.diagram-container {
  position: relative;
  width: 600rpx;
  height: 400rpx;
}

/* 舌诊示意图背景 */
.tongue-diagram-bg {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 实际图片容器 - 位于舌体右侧中下部 */
.actual-photo-container {
  position: absolute;
  bottom: 60rpx;
  right:140rpx;
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 6rpx solid white;
  background: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.actual-photo {
  width: 108rpx;
  height: 108rpx;
  border-radius: 50%;
  object-fit: cover;
}

.photo-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: #f8f9fa;
}

.placeholder-icon {
  font-size: 32rpx;
  margin-bottom: 4rpx;
  color: #6c757d;
}

.placeholder-text {
  font-size: 18rpx;
  color: #6c757d;
  text-align: center;
}

.view-original-hint {
  position: absolute;
  bottom: -35rpx;
  left: 50%;
  transform: translateX(-50%);
  font-size: 20rpx;
  color: #495057;
  background: white;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  border: 1rpx solid #dee2e6;
  white-space: nowrap;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 加载状态 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 0;
  background: white;
  margin: 0 32rpx;
  border-radius: 16rpx;
}

.loading-icon {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #e9ecef;
  border-top: 6rpx solid #2196f3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 24rpx;
}

.loading-text {
  color: #6c757d;
  font-size: 28rpx;
}

/* 内容区域 */
.content-section {
  padding: 0 32rpx;
}

/* 舌象特征分析与描述合并模块 */
.tongue-feature-block {
  margin-bottom: 36rpx;
}
.feature-table {
  margin-bottom: 32rpx;
}
.feature-row-desc {
  display: flex;
  align-items: flex-start;
  margin-bottom: 18rpx;
}
.feature-label {
  width: 80rpx;
  color: #444c56;
  font-size: 26rpx;
  font-weight: 500;
  flex-shrink: 0;
  line-height: 44rpx;
  padding-top: 8rpx;
  padding-bottom: 8rpx;
  display: flex;
  align-items: center;
}
.feature-value {
  min-width: 100rpx;
  max-width: 120rpx;
  color: #3ec6c6;
  font-size: 26rpx;
  font-weight: bold;
  background: #f4f8fb;
  border-radius: 12rpx;
  padding: 8rpx 24rpx;
  margin-right: 12rpx;
  flex-shrink: 0;
  align-self: flex-start;
  line-height: 44rpx;
  display: flex;
  align-items: center;
}
.feature-desc {
  color: #888;
  font-size: 24rpx;
  line-height: 1.7;
  flex: 1;
  word-break: break-all;
  margin-left: 8rpx;
}
.section-title {
  display: flex;
  align-items: center;
  margin: 0 0 18rpx 0;
}
.section-bar {
  width: 8rpx;
  height: 38rpx;
  background: #3ec6c6;
  border-radius: 4rpx;
  margin-right: 16rpx;
}
.section-text {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  line-height: 1;
}

/* 体质分析特殊样式 */
.constitution-group .group-title {
  background: #2196f3;
}

.constitution-content {
  padding: 32rpx;
  text-align: center;
}

.constitution-result {
  font-size: 32rpx;
  font-weight: bold;
  color: #1565c0;
  background: #e3f2fd;
  padding: 20rpx 32rpx;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  border: 2rpx solid #2196f3;
}

.constitution-desc {
  font-size: 24rpx;
  color: #6c757d;
}

/* 底部信息 */
.footer-info {
  margin-top: 32rpx;
  padding: 24rpx 32rpx;
  background: white;
  border-radius: 16rpx;
  border: 1rpx solid #e9ecef;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.06);
}

.analysis-time {
  font-size: 24rpx;
  color: #6c757d;
  margin-bottom: 12rpx;
  text-align: center;
}

.disclaimer {
  font-size: 22rpx;
  color: #adb5bd;
  text-align: center;
  line-height: 1.5;
}

/* 不同分析组的主题色 */
.description-title {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.constitution-title {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.wellness-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #009688;
  background: none !important;
  padding: 0;
  border-radius: 0;
}

.seasonal-title {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;
}

/* 特征详细描述样式 */
.description-group .group-content {
  padding: 0;
}

.description-item {
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f1f3f4;
}

.description-item:last-child {
  border-bottom: none;
}

.feature-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.feature-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.feature-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #2c3e50;
}

.feature-description {
  font-size: 26rpx;
  color: #5a6c7d;
  line-height: 1.6;
  text-align: justify;
}

/* 体质分析增强样式 */
.constitution-type {
  font-size: 36rpx;
  font-weight: bold;
  color: #e74c3c;
  margin-bottom: 8rpx;
}

.constitution-rhyme {
  font-size: 24rpx;
  color: #7f8c8d;
  font-style: italic;
}

.constitution-performance {
  margin-top: 24rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid #ecf0f1;
}

.performance-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #34495e;
  margin-bottom: 16rpx;
}

.performance-content {
  font-size: 26rpx;
  color: #5a6c7d;
  line-height: 1.7;
  text-align: justify;
}

/* 养生方案样式 */
.wellness-content {
  margin-top: 8rpx;
  margin-left: 40rpx;
}
.wellness-item {
  margin-bottom: 22rpx;
}
.wellness-item-title-row {
  display: flex;
  align-items: center;
  margin-bottom: 6rpx;
}
.wellness-item-bar {
  width: 5rpx;
  height: 30rpx;
  background: #3ec6c6;
  border-radius: 4rpx;
  margin-right: 10rpx;
  flex-shrink: 0;
}
.wellness-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #393939;
  background: none !important;
  padding: 0;
  border-radius: 0;
  margin-right: 30rpx;
}
.wellness-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.7;
  margin-left: 22rpx;
}

/* 节气养生样式 */
.seasonal-content {
  padding: 32rpx;
}

.seasonal-header {
  text-align: center;
  margin-bottom: 24rpx;
}

.seasonal-term {
  font-size: 32rpx;
  font-weight: bold;
  color: #27ae60;
  margin-bottom: 8rpx;
}

.seasonal-time {
  font-size: 24rpx;
  color: #7f8c8d;
}

.seasonal-advice {
  font-size: 26rpx;
  color: #5a6c7d;
  line-height: 1.7;
  text-align: justify;
  background: #f8f9fa;
  padding: 24rpx;
  border-radius: 12rpx;
  border-left: 4rpx solid #27ae60;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes slideInUp {
  0% {
    opacity: 0;
    transform: translateY(30rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.card-group {
  background: none;
  border-radius: 0;
  box-shadow: none;
  margin-bottom: 28rpx;
  overflow: visible;
}
.card-title,
.card-title-basic,
.card-title-special,
.card-title-desc {
  background: none !important;
  color: #333 !important;
}
.card-title-icon { display: none; }
.card-content {
  padding: 18rpx 24rpx 18rpx 24rpx;
}
.card-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.card-label {
  width: 120rpx;
  color: #444c56;
  font-size: 26rpx;
  font-weight: 500;
}
.card-value {
  flex: 1;
  text-align: right;
  background: #f4f8fb;
  color: #3ec6c6;
  font-size: 26rpx;
  border-radius: 12rpx;
  padding: 8rpx 24rpx;
  font-weight: bold;
}
.card-value-empty {
  color: #bbb;
  background: #f5f5f5;
}
.card-desc-content {
  padding: 0 24rpx 18rpx 24rpx;
}
.desc-row {
  margin-bottom: 18rpx;
}
.desc-feature {
  display: flex;
  align-items: center;
  margin-bottom: 6rpx;
}
.desc-feature-icon { display: none; }
.desc-feature-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
}
.desc-feature-desc {
  font-size: 24rpx;
  color: #5a6c7d;
  line-height: 1.6;
  text-align: justify;
  padding-left: 34rpx;
}
.section-title {
  display: flex;
  align-items: center;
  margin: 0 0 18rpx 0;
}
.section-bar {
  width: 8rpx;
  height: 38rpx;
  background: #3ec6c6;
  border-radius: 4rpx;
  margin-right: 16rpx;
}
.section-text {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  line-height: 1;
}

.constitution-block {
  margin-bottom: 36rpx;
}
.constitution-card {
  background: #f7fbff;
  border: 2rpx solid #b3d8f7;
  border-radius: 16rpx;
  padding: 32rpx 24rpx 18rpx 24rpx;
  margin-bottom: 18rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.constitution-type {
  font-size: 32rpx;
  font-weight: bold;
  color: #d32f2f;
  margin-bottom: 8rpx;
  text-align: center;
}
.constitution-rhyme {
  font-size: 22rpx;
  color: #2196f3;
  font-style: italic;
  text-align: center;
}
.performance-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  text-align: center;
}
.performance-content {
  font-size: 24rpx;
  color: #666;
  line-height: 1.7;
  text-align: justify;
}
</style>