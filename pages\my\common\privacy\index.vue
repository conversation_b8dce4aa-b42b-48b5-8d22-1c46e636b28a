<template>
	<view class="protocol-container">
		<scroll-view scroll-y class="protocol-content">
			<view v-for="(para, idx) in paragraphs" :key="idx" class="protocol-paragraph">
				<text v-if="isMainTitle(para)" class="protocol-main-title">{{ para }}</text>
				<text v-else-if="isSectionTitle(para)" class="protocol-section-title">{{ para }}</text>
				<text v-else class="protocol-text">{{ para }}</text>
			</view>
		</scroll-view>
	</view>
</template>

<script>
import privacyText from '../../../../utils/privacy.js'

export default {
	data() {
		return {
			paragraphs: []
		}
	},
	mounted() {
		this.paragraphs = privacyText.split(/\n+/).filter(p => p.trim())
	},
	methods: {
		goBack() {
			uni.navigateBack()
		},
		isMainTitle(para) {
			return this.paragraphs[0] === para
		},
		isSectionTitle(para) {
			return /^([一二三四五六七八九十]+、|[A-Za-z0-9]+[.])/.test(para) || para.length < 14
		}
	}
}
</script>

<style scoped>
.protocol-content {
	flex: 1;
	padding: 36rpx 24rpx 40rpx 24rpx;
	background: #f8f9fc;
	box-sizing: border-box;
	max-height: calc(100vh - 88rpx);
}
.protocol-paragraph {
	margin-bottom: 24rpx;
}
.protocol-main-title {
	display: block;
	font-size: 34rpx;
	font-weight: 900;
	color: #222;
	text-align: center;
	margin-bottom: 12rpx;
}
.protocol-section-title {
	display: block;
	font-size: 28rpx;
	font-weight: 700;
	color: #222;
	margin-bottom: 8rpx;
}
.protocol-text {
	font-size: 26rpx;
	color: #444;
	line-height: 1.8;
	white-space: pre-line;
}
</style>