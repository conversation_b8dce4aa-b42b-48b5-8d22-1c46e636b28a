<template>
	<view class="health-record-container">
		<!-- 顶部选项卡导航 -->
		<view class="tab-navigation">
			<scroll-view class="tab-scroll" scroll-x="true" show-scrollbar="false">
				<view class="tab-list">
				<view
					v-for="(tab, idx) in tabs"
					:key="tab.key"
						:class="['tab-item', { active: currentTab === idx }]"
					@click="switchTab(idx)"
				>
						<view class="tab-content">
							<view class="tab-label">{{ tab.label }}</view>
							
						</view>
					</view>
				</view>
			</scroll-view>
			</view>

		<!-- 内容区域 -->
		<view class="content-area">
				<view class="content-header">
				<view class="section-title">{{ tabs[currentTab].label }}记录</view>
				<view class="record-summary">
					<view class="record-count">共 {{ getReportCount(currentTab) }} 条记录</view>
				</view>
				</view>

				<!-- 统一的记录列表 -->
				<view class="report-list">
					<view v-if="getCurrentReports().length">
						<view v-for="(item, i) in getCurrentReports()" :key="i" class="report-card" @click="viewReportDetail(item)">
							<view class="report-content">
								<view class="report-title">
									{{ item.title }}
									<!-- 医疗档案标识 -->
									<text v-if="item.isMedicalRecord" class="medical-tag">舌诊仪</text>
								</view>
								<view class="report-date">{{ item.date }}</view>
								<view class="report-desc">{{ item.desc }}</view>
							</view>
							<view class="report-status-area">
								<view class="report-status">{{ item.status || '已完成' }}</view>
								<view class="view-detail">查看详情</view>
							</view>
						</view>
					</view>
					<view v-else class="empty-state">
						<view class="empty-text">{{ getEmptyText() }}</view>
						<view class="empty-desc">{{ getEmptyDesc() }}</view>
					</view>
				</view>
		</view>
	</view>
	
</template>

<script>
	import {
		history,
		getTongueHistory,
		getConstitutionHistory,
		getComprehensiveHistory,
		getPsychologyHistory
	} from '@/api/system/history.js'

	export default {
		data() {
			return {
				tabs: [
					{ key: 'tongue', label: 'AI舌诊' },
					{ key: 'constitution', label: '体质检测' },
					{ key: 'comprehensive', label: '综合问诊' },
					{ key: 'psychology', label: '心理评测' }
				],
				currentTab: 0,
				tongueReports: [],
				constitutionReports: [],
				comprehensiveReports: [],
				psychologyReports: []
			
			}
		},
		onLoad(options) {
			if (options.tab !== undefined) {
				this.currentTab = parseInt(options.tab)
			}
		},
		onShow() {
			this.loadAllReports()
		},
		methods: {
			// 切换选项卡
			switchTab(index) {
				this.currentTab = index
			},

			// 获取报告数量
			getReportCount(tabIndex) {
				return this.getCurrentReports(tabIndex).length
			},

			// 获取当前标签页的报告数据
			getCurrentReports(tabIndex = null) {
				const index = tabIndex !== null ? tabIndex : this.currentTab
				switch(index) {
					case 0: return this.tongueReports
					case 1: return this.constitutionReports
					case 2: return this.comprehensiveReports
					case 3: return this.psychologyReports
					default: return []
				}
			},

			// 获取空状态文本
			getEmptyText() {
				const texts = [
					'暂无AI舌诊记录',
					'暂无体质检测记录',
					'暂无综合问诊记录',
					'暂无心理评测记录'
				]
				return texts[this.currentTab] || '暂无记录'
			},

			// 获取空状态描述
			getEmptyDesc() {
				const descs = [
					'完成舌诊检测后，记录将显示在这里',
					'完成体质检测后，记录将显示在这里',
					'完成综合问诊后，记录将显示在这里',
					'完成心理评测后，记录将显示在这里'
				]
				return descs[this.currentTab] || '记录将显示在这里'
			},

			// 统一的报告详情查看方法
			viewReportDetail(item) {
				switch(this.currentTab) {
					case 0: this.viewTongueDetail(item); break
					case 1: this.viewConstitutionDetail(item); break
					case 2: this.viewComprehensiveDetail(item); break
					case 3: this.viewPsychologyDetail(item); break
				}
			},
			// 加载所有类型的历史记录
			async loadAllReports() {
				try {
					uni.showLoading({ title: '加载中...' })

					// 调用API获取所有历史记录
					const response = await history()
					console.log('历史记录API响应:', response)

					if (response && response.data) {
						this.parseHistoryData(response.data)
					} else {
						console.log('API返回数据为空')
					}
				} catch (error) {
					console.error('加载历史记录失败:', error)
				} finally {
					uni.hideLoading()
				}
			},

			// 解析API返回的历史数据
			parseHistoryData(data) {
				// 初始化数组
				this.tongueReports = []
				this.constitutionReports = []
				this.comprehensiveReports = []
				this.psychologyReports = []

				// 根据后端数据结构解析不同类型的记录
				if (Array.isArray(data)) {
					data.forEach(record => {
						this.categorizeRecord(record)
					})
				} else if (data.records && Array.isArray(data.records)) {
					data.records.forEach(record => {
						this.categorizeRecord(record)
					})
				}

				// 按时间排序所有记录，最新的在前面
				this.sortRecordsByTime()

				console.log('解析后的数据:', {
					tongue: this.tongueReports.length,
					constitution: this.constitutionReports.length,
					comprehensive: this.comprehensiveReports.length,
					psychology: this.psychologyReports.length
				})
			},

			// 根据记录类型分类数据
			categorizeRecord(record) {
				// 根据后端数据结构提取关键字段
				const creaTime = record.creatime
				const groupName = record.group_name
				const userseeionType = record.UserseeionType
				const date = this.formatDate(creaTime)
				const formattedDate = this.formatDisplayDate(creaTime)

				// 检查是否为医疗档案（UserseeionType为空的记录）
				const isMedicalRecord = !userseeionType || userseeionType === null || userseeionType === undefined || userseeionType === ''

				// 根据 group_name 或其他标识字段判断记录类型
				if (this.isTongueRecord(record, groupName)) {
					this.tongueReports.push({
						phone: record.phone || '',
						title: `${date.year}年${date.month}月 AI舌诊报告`,
						date: formattedDate,
						desc: this.getTongueDescription(record),
						status: '已完成',
						isMedicalRecord: isMedicalRecord, // 添加医疗档案标识
						rawData: record
					})
				} else if (this.isConstitutionRecord(record, groupName)) {
					this.constitutionReports.push({
						phone: record.phone || '',
						title: `${date.year}年${date.month}月 体质检测报告`,
						date: formattedDate,
						desc: this.getConstitutionDescription(record),
						status: '已完成',
						isMedicalRecord: isMedicalRecord, // 添加医疗档案标识
						rawData: record
					})
				} else if (this.isComprehensiveRecord(record, groupName)) {
					this.comprehensiveReports.push({
						phone: record.phone || '',
						person_name: record.person_name || '',
						title: `${date.year}年${date.month}月 综合问诊报告`,
						date: formattedDate,
						desc: this.getComprehensiveDescription(record),
						status: '已完成',
						isMedicalRecord: isMedicalRecord, // 添加医疗档案标识
						rawData: record
					})
				} else if (this.isPsychologyRecord(record, groupName)) {
					this.psychologyReports.push({
						phone: record.phone || '',
						person_name: record.person_name || '',
						title: `${date.year}年${date.month}月 心理评测报告`,
						date: formattedDate,
						desc: this.getPsychologyDescription(record),
						status: '已完成',
						isMedicalRecord: isMedicalRecord, // 添加医疗档案标识
						rawData: record
					})
				}
			},

			// 判断是否为舌诊记录
			isTongueRecord(record,groupName) {
				return groupName === 'type_name'
			},

			// 判断是否为体质检测记录
			isConstitutionRecord(record,groupName) {
				return groupName === 'Consultation'
			},

			// 判断是否为综合问诊记录
			isComprehensiveRecord(record,groupName) {
				return groupName === 'synthesis'
			},

			// 判断是否为心理评测记录
			isPsychologyRecord(record,groupName) {
				return groupName === 'psychology'
			},

			// 获取舌诊描述
			getTongueDescription(record) {
				console.log('舌诊记录数据:', record)

				let description = ''

				// 优先使用 type_name（体质类型）
				if (record.type_name) {
					description = record.type_name
				}

				// 添加 secondary 字段（如"肾阴虚"）
				if (record.secondary) {
					if (description) {
						description += ` ${record.secondary}`
					} else {
						description = record.secondary
					}
				}

				// 添加舌色和舌形信息
				const features = []
				if (record.tongue_color) {
					features.push(record.tongue_color)
				}
				if (record.tongue_shape) {
					features.push(record.tongue_shape)
				}

				if (features.length > 0) {
					if (description) {
						description += ` (${features.join(' ')})`
					} else {
						description = features.join(' ')
					}
				}

				// 如果都没有，使用默认描述
				if (!description) {
					description = '舌象分析'
				}

				console.log('生成的舌诊描述:', description)
				return description
			},

			// 获取体质检测描述
			getConstitutionDescription(record) {
				if (record.consultation){
					return record.consultation
				} 
				return '平和体质'
			},

			// 获取综合问诊描述
			getComprehensiveDescription(record) {
				if (record.consultation && record.type_name) {
					return `${record.consultation} ${record.type_name}`
				}
				
				return '综合分析'
			},

			// 获取心理评测描述
			getPsychologyDescription(record) {
				if (record.psychology !== null) {
					const depressionIndex = parseFloat(record.psychology) / 80
					if (depressionIndex < 0.5) return '无抑郁'
					else if (depressionIndex >= 0.5 && depressionIndex < 0.6) return '轻度抑郁'
					else if (depressionIndex >= 0.6 && depressionIndex < 0.7) return '中重度抑郁'
					else return '重度抑郁'
				}
				return '心理评估'
			},



			// 格式化显示日期
			formatDisplayDate(dateStr) {
				if (!dateStr) return '未知时间'

				try {
					// 处理 "2025-07-03T10:50:19.000+08:00" 格式
					const date = new Date(dateStr)
					if (isNaN(date.getTime())) {
						return dateStr // 如果解析失败，返回原始字符串
					}

					const year = date.getFullYear()
					const month = String(date.getMonth() + 1).padStart(2, '0')
					const day = String(date.getDate()).padStart(2, '0')
					const hours = String(date.getHours()).padStart(2, '0')
					const minutes = String(date.getMinutes()).padStart(2, '0')

					return `${year}-${month}-${day} ${hours}:${minutes}`
				} catch (error) {
					console.error('日期格式化失败:', error)
					return dateStr
				}
			},

			// 格式化日期
			formatDate(dateStr) {
				if (!dateStr) return { year: '未知', month: '未知' }

				const date = new Date(dateStr)
				if (isNaN(date.getTime())) {
					// 如果日期格式不标准，尝试解析
					const parts = dateStr.split(/[-\s:]/)
					return {
						year: parts[0] || '未知',
						month: parseInt(parts[1]) || '未知'
					}
				}

				return {
					year: date.getFullYear(),
					month: date.getMonth() + 1
				}
			},


			// 查看舌诊详情
			async viewTongueDetail(item) {
				try {
					uni.showLoading({ title: '加载详情...' })

					console.log('查看舌诊详情，传入参数:', item)
					console.log('调用API参数 - phone:', item.phone, 'creatime:', item.rawData?.creatime)

					// 验证必要参数
					if (!item.phone || !item.rawData?.creatime) {
						console.error('缺少必要参数:', { phone: item.phone, creatime: item.rawData?.creatime })
						throw new Error('缺少必要的查询参数')
					}

					// 调用舌诊历史记录API获取详细数据
					const response = await getTongueHistory(item.phone, item.rawData.creatime)
					console.log('舌诊历史记录API响应:', response)

					// 处理不同的响应格式
					let reportData = null

					if (response && response.data) {
						// 标准响应格式
						const data = response.data
						let result = data.result || data

						// 确保结果数据包含正确的分析时间和列表数据
						if (result && typeof result === 'object') {
							result.analysis_time = item.rawData.creatime
							result.request_time = item.rawData.creatime
							result.creatime = item.rawData.creatime

							// 从列表数据中补充关键字段（如果API返回的数据中没有）
							if (item.rawData.type_name && !result.type_name) {
								result.type_name = item.rawData.type_name
							}
							if (item.rawData.secondary && !result.secondary) {
								result.secondary = item.rawData.secondary
							}
							if (item.rawData.tongue_color && !result.tongue_color) {
								result.tongue_color = item.rawData.tongue_color
							}
							if (item.rawData.tongue_shape && !result.tongue_shape) {
								result.tongue_shape = item.rawData.tongue_shape
							}
						}

						// 如果result是字符串，尝试解析
						if (typeof result === 'string') {
							try {
								result = JSON.parse(result)
								result.analysis_time = item.rawData.creatime
								result.request_time = item.rawData.creatime
								result.creatime = item.rawData.creatime

								// 从列表数据中补充关键字段
								if (item.rawData.type_name && !result.type_name) {
									result.type_name = item.rawData.type_name
								}
								if (item.rawData.secondary && !result.secondary) {
									result.secondary = item.rawData.secondary
								}
								if (item.rawData.tongue_color && !result.tongue_color) {
									result.tongue_color = item.rawData.tongue_color
								}
								if (item.rawData.tongue_shape && !result.tongue_shape) {
									result.tongue_shape = item.rawData.tongue_shape
								}
							} catch (e) {
								console.warn('解析result字符串失败:', e)
							}
						}

						reportData = {
							result: result,
							photo: data.image_A ||
								   data.image ||
								   data.photo ||
								   (data.result && data.result.image_A) ||
								   (data.result && data.result.externalResponse && data.result.externalResponse.image_A)
						}
					} else if (response) {
						// 直接在response根级别的数据
						let result = response.result || response

						// 确保结果数据包含正确的分析时间和列表数据
						if (result && typeof result === 'object') {
							result.analysis_time = item.rawData.creatime
							result.request_time = item.rawData.creatime
							result.creatime = item.rawData.creatime

							// 从列表数据中补充关键字段
							if (item.rawData.type_name && !result.type_name) {
								result.type_name = item.rawData.type_name
							}
							if (item.rawData.secondary && !result.secondary) {
								result.secondary = item.rawData.secondary
							}
							if (item.rawData.tongue_color && !result.tongue_color) {
								result.tongue_color = item.rawData.tongue_color
							}
							if (item.rawData.tongue_shape && !result.tongue_shape) {
								result.tongue_shape = item.rawData.tongue_shape
							}
						}

						// 如果result是字符串，尝试解析
						if (typeof result === 'string') {
							try {
								result = JSON.parse(result)
								result.analysis_time = item.rawData.creatime
								result.request_time = item.rawData.creatime
								result.creatime = item.rawData.creatime

								// 从列表数据中补充关键字段
								if (item.rawData.type_name && !result.type_name) {
									result.type_name = item.rawData.type_name
								}
								if (item.rawData.secondary && !result.secondary) {
									result.secondary = item.rawData.secondary
								}
								if (item.rawData.tongue_color && !result.tongue_color) {
									result.tongue_color = item.rawData.tongue_color
								}
								if (item.rawData.tongue_shape && !result.tongue_shape) {
									result.tongue_shape = item.rawData.tongue_shape
								}
							} catch (e) {
								console.warn('解析result字符串失败:', e)
							}
						}

						reportData = {
							result: result,
							photo: response.image_A ||
								   response.image ||
								   response.photo ||
								   (response.result && response.result.image_A) ||
								   (response.result && response.result.externalResponse && response.result.externalResponse.image_A) ||
								   (response.externalResponse && response.externalResponse.image_A)
						}
					}

					console.log('提取的图片URL:', reportData?.photo)
					console.log('完整的API响应数据:', response)
					console.log('处理后的reportData:', reportData)

					if (reportData && reportData.result) {
						console.log('构造的报告数据:', reportData)
						console.log('报告数据的result字段:', reportData.result)

						const resultStr = encodeURIComponent(JSON.stringify(reportData.result))
						let url = `/pages/gather/diagnosis/report?result=${resultStr}`

						if (reportData.photo) {
							const photoStr = encodeURIComponent(reportData.photo)
							url += `&photo=${photoStr}`
						}

						// console.log('跳转URL:', url)
						uni.navigateTo({ url })
					} else {
						console.warn('API返回数据格式异常，尝试使用原始数据')
						// 如果API数据格式异常，尝试使用原始记录数据
						const fallbackData = {
							result: item.rawData || {
								type_name: item.desc || '舌象分析',
								tongue_color: '未知',
								tongue_shape: '未知',
								analysis_time: item.rawData.creatime,
								request_time: item.rawData.creatime
							},
							photo: null
						}

						const resultStr = encodeURIComponent(JSON.stringify(fallbackData.result))
						const url = `/pages/gather/diagnosis/report?result=${resultStr}`

						// console.log('使用备用数据跳转:', url)
						uni.navigateTo({ url })
					}
				} catch (error) {
					console.error('获取舌诊详情失败:', error)

					// 提供更详细的错误信息
					let errorMessage = '获取详情失败'
					if (error.message.includes('缺少必要')) {
						errorMessage = '数据不完整，无法查看详情'
					} else if (error.message.includes('网络')) {
						errorMessage = '网络连接失败，请检查网络'
					}

					uni.showToast({
						title: errorMessage,
						icon: 'none',
						duration: 3000
					})
				} finally {
					uni.hideLoading()
				}
			},

			// 查看体质检测详情
			async viewConstitutionDetail(item) {
				try {
					uni.showLoading({ title: '加载详情...' })

					console.log('查看体质检测详情，传入参数:', item)
					console.log('调用API参数 - phone:', item.phone, 'creatime:', item.rawData?.creatime)

					// 验证必要参数
					if (!item.phone || !item.rawData?.creatime) {
						console.error('缺少必要参数:', { phone: item.phone, creatime: item.rawData?.creatime })
						throw new Error('缺少必要的查询参数')
					}

					// 调用体质检测历史记录API获取详细数据
					const response = await getConstitutionHistory(item.phone, item.rawData.creatime)
					console.log('体质检测历史记录API响应:', response)

					// 处理不同的响应格式
					let detailData = null

					if (response && response.data) {
						detailData = response.data
					} else if (response) {
						detailData = response
					}

					if (detailData) {
						// 确保详情数据包含正确的检测时间
						if (detailData && typeof detailData === 'object') {
							detailData.testTime = item.rawData.creatime
							detailData.request_time = item.rawData.creatime
							detailData.analysis_time = item.rawData.creatime
						}
						console.log('构造的详情数据:', detailData)
						const dataStr = encodeURIComponent(JSON.stringify(detailData))
						uni.navigateTo({
							url: `/pages/gather/healthy/result?data=${dataStr}`
						})
					} else {
						console.warn('API返回数据格式异常，尝试使用原始数据')
						// 如果API数据格式异常，尝试使用原始记录数据
						const fallbackData = {
							constitution: item.desc || '未知体质',
							testTime: item.rawData.creatime,
							request_time: item.rawData.creatime,
							analysis_time: item.rawData.creatime,
							rawData: item.rawData
						}

						const dataStr = encodeURIComponent(JSON.stringify(fallbackData))
						uni.navigateTo({
							url: `/pages/gather/healthy/result?data=${dataStr}`
						})
					}
				} catch (error) {
					console.error('获取体质检测详情失败:', error)

					// 提供更详细的错误信息
					let errorMessage = '获取详情失败'
					if (error.message.includes('缺少必要')) {
						errorMessage = '数据不完整，无法查看详情'
					} else if (error.message.includes('网络')) {
						errorMessage = '网络连接失败，请检查网络'
					}

					uni.showToast({
						title: errorMessage,
						icon: 'none',
						duration: 3000
					})
				} finally {
					uni.hideLoading()
				}
			},

			// 查看综合问诊详情
			async viewComprehensiveDetail(item) {
				try {
					uni.showLoading({ title: '加载详情...' })

					console.log('查看综合问诊详情，传入参数:', item)
					console.log('调用API参数 - phone:', item.phone, 'creatime:', item.rawData?.creatime)

					// 验证必要参数
					if (!item.phone || !item.rawData?.creatime) {
						console.error('缺少必要参数:', { phone: item.phone, creatime: item.rawData?.creatime })
						throw new Error('缺少必要的查询参数')
					}

					// 调用综合问诊历史记录API获取详细数据
					const response = await getComprehensiveHistory(item.phone, item.rawData.creatime)
					console.log('综合问诊历史记录API响应:', response)

					// 处理不同的响应格式
					let reportData = null

					if (response && response.data) {
						// 标准响应格式
						const data = response.data.result || response.data
						const apiResponse = response.data.apiResponse || response.data
						// 确保数据包含正确的分析时间
						if (data && typeof data === 'object') {
							data.analysis_time = item.rawData.creatime
							data.request_time = item.rawData.creatime
						}
						if (apiResponse && typeof apiResponse === 'object') {
							apiResponse.analysis_time = item.rawData.creatime
							apiResponse.request_time = item.rawData.creatime
						}
						reportData = {
							data: data,
							apiResponse: apiResponse
						}
					} else if (response) {
						// 直接在response根级别的数据
						const data = response.result || response
						// 确保数据包含正确的分析时间
						if (data && typeof data === 'object') {
							data.analysis_time = item.rawData.creatime
							data.request_time = item.rawData.creatime
						}
						if (response && typeof response === 'object') {
							response.analysis_time = item.rawData.creatime
							response.request_time = item.rawData.creatime
						}
						reportData = {
							data: data,
							apiResponse: response
						}
					}

					if (reportData) {
						console.log('构造的报告数据:', reportData)
						const resultStr = encodeURIComponent(JSON.stringify(reportData))
						uni.navigateTo({
							url: `/pages/gather/comprehensive/result?result=${resultStr}`
						})
					} else {
						console.warn('API返回数据格式异常，尝试使用原始数据')
						// 如果API数据格式异常，尝试使用原始记录数据
						const fallbackData = {
							data: item.rawData || {
								consultation: item.desc || '综合分析',
								analysis_time: item.rawData.creatime,
								request_time: item.rawData.creatime
							},
							apiResponse: item.rawData
						}

						const resultStr = encodeURIComponent(JSON.stringify(fallbackData))
						uni.navigateTo({
							url: `/pages/gather/comprehensive/result?result=${resultStr}`
						})
					}
				} catch (error) {
					console.error('获取综合问诊详情失败:', error)

					// 提供更详细的错误信息
					let errorMessage = '获取详情失败'
					if (error.message.includes('缺少必要')) {
						errorMessage = '数据不完整，无法查看详情'
					} else if (error.message.includes('网络')) {
						errorMessage = '网络连接失败，请检查网络'
					}

					uni.showToast({
						title: errorMessage,
						icon: 'none',
						duration: 3000
					})
				} finally {
					uni.hideLoading()
				}
			},

			// 查看心理评测详情
			async viewPsychologyDetail(item) {
				try {
					uni.showLoading({ title: '加载详情...' })

					console.log('查看心理评测详情，传入参数:', item)
					console.log('调用API参数 - phone:', item.phone, 'creatime:', item.rawData?.creatime)

					// 验证必要参数
					if (!item.phone || !item.rawData?.creatime) {
						console.error('缺少必要参数:', { phone: item.phone, creatime: item.rawData?.creatime })
						throw new Error('缺少必要的查询参数')
					}

					// 调用心理评测历史记录API获取详细数据
					const response = await getPsychologyHistory(item.phone, item.rawData.creatime)
					console.log('心理评测历史记录API响应:', response)

					// 处理不同的响应格式
					let reportData = null

					if (response && response.data) {
						// 标准响应格式
						reportData = {
							totalScore: response.data.totalScore || response.data.psychology || 0,
							testTime: item.rawData.creatime, // 使用API请求时的时间
							apiResponse: response.data.apiResponse || response.data,
							level: response.data.level || null,
							severityIndex: response.data.severityIndex || null,
							analysis_time: item.rawData.creatime,
							request_time: item.rawData.creatime
						}
					} else if (response) {
						// 直接在response根级别的数据
						reportData = {
							totalScore: response.totalScore || response.psychology || 0,
							testTime: item.rawData.creatime, // 使用API请求时的时间
							apiResponse: response,
							level: response.level || null,
							severityIndex: response.severityIndex || null,
							analysis_time: item.rawData.creatime,
							request_time: item.rawData.creatime
						}
					}

					if (reportData) {
						console.log('构造的报告数据:', reportData)
						const dataStr = encodeURIComponent(JSON.stringify(reportData))
						uni.navigateTo({
							url: `/pages/heart/heart/report?data=${dataStr}&from=health`
						})
					} else {
						console.warn('API返回数据格式异常，尝试使用原始数据')
						// 如果API数据格式异常，尝试使用原始记录数据
						const fallbackData = {
							totalScore: 0,
							testTime: item.rawData.creatime, // 使用API请求时的时间
							apiResponse: item.rawData,
							level: null,
							severityIndex: null,
							analysis_time: item.rawData.creatime,
							request_time: item.rawData.creatime
						}

						const dataStr = encodeURIComponent(JSON.stringify(fallbackData))
						uni.navigateTo({
							url: `/pages/heart/heart/report?data=${dataStr}&from=health`
						})
					}
				} catch (error) {
					console.error('获取心理评测详情失败:', error)

					// 提供更详细的错误信息
					let errorMessage = '获取详情失败'
					if (error.message.includes('缺少必要')) {
						errorMessage = '数据不完整，无法查看详情'
					} else if (error.message.includes('网络')) {
						errorMessage = '网络连接失败，请检查网络'
					}

					uni.showToast({
						title: errorMessage,
						icon: 'none',
						duration: 3000
					})
				} finally {
					uni.hideLoading()
				}
			},



			// 按时间排序所有记录，最新的在前面
			sortRecordsByTime() {
				try {
					// 排序函数：根据rawData.creatime进行排序
					const sortByTime = (a, b) => {
						const timeA = new Date(a.rawData?.creatime || 0).getTime()
						const timeB = new Date(b.rawData?.creatime || 0).getTime()
						return timeB - timeA // 降序排列，最新的在前面
					}

					// 对所有类型的记录进行排序
					this.tongueReports.sort(sortByTime)
					this.constitutionReports.sort(sortByTime)
					this.comprehensiveReports.sort(sortByTime)
					this.psychologyReports.sort(sortByTime)

					console.log('记录排序完成，按时间降序排列')
				} catch (error) {
					console.error('记录排序失败:', error)
				}
			}
		}
	}
</script>

<style scoped>
/* 页面容器 */
	.health-record-container {
		height: 100vh;
		display: flex;
		flex-direction: column;
  background: #f5f7fa;
		padding: 0;
	}

/* 顶部选项卡 */
.tab-navigation {
  background: #fff;
  border-radius: 0;
  padding: 30rpx 20rpx;
		margin-bottom: 10rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.tab-scroll {
  width: 100%;
}

.tab-list {
		display: flex;
		align-items: center;
  justify-content: space-between;
  width: 100%;
}

.tab-item {
  position: relative;
  padding: 16rpx 0;
  border-radius: 30rpx;
  transition: all 0.3s ease;
  text-align: center;
  flex: 1;
}

.tab-item.active {
  background: #3ec6c6;
  color: #fff;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(62, 198, 198, 0.2);
}

.tab-item:not(.active) {
  color: #666;
  background: #f5f7fa;
}

.tab-item:not(.active):hover {
  background: #edf2f7;
  transform: translateY(-2rpx);
}

.tab-label {
		font-size: 28rpx;
  line-height: 1.2;
}

/* 隐藏滚动条但保持可滚动 */
.tab-navigation::-webkit-scrollbar {
  display: none;
}

/* 内容区域 */
.content-area {
  padding: 0 20rpx;
  margin-top: 10rpx;
	flex: 1;
	display: flex;
	flex-direction: column;
	overflow: hidden;
	}

	.content-header {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
	flex-shrink: 0;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
		color: #2c3e50;
	}

.record-summary {
  /* background: linear-gradient(135deg, #3ec6c6 0%, #6ee7e7 100%); */
  border-radius: 15rpx;
  padding: 15rpx 20rpx;
  box-shadow: 0 4rpx 15rpx rgba(62, 198, 198, 0.3);
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.record-count {
		font-size: 24rpx;
  color: #fff;
  background: #3ec6c6;
  padding: 8rpx 30rpx;
  border-radius: 24rpx;
  width: auto;
  margin-left: auto;
  box-shadow: 0 2rpx 8rpx rgba(62, 198, 198, 0.2);
}

/* 记录卡片 */
	.report-list {
		flex: 1;
		overflow-y: auto;
		padding-bottom: 20rpx;
	}

	.report-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  position: relative;
		transition: all 0.3s ease;
  display: flex;
  justify-content: space-between;
}

/* 左侧内容区域 */
.report-content {
  flex: 1;
  padding-right: 20rpx;
  position: relative;
  padding-left: 16rpx;
}

.report-content::before {
  content: '';
  position: absolute;
  left: 0;
  top: 4rpx;
  bottom: 4rpx;
  width: 6rpx;
  background: #3ec6c6;
  border-radius: 3rpx;
	}

	.report-title {
		font-size: 32rpx;
  font-weight: 600;
		color: #2c3e50;
  margin-bottom: 12rpx;
		position: relative;
		display: flex;
		align-items: center;
		flex-wrap: wrap;
	}

	.medical-tag {
		font-size: 20rpx;
		color: #ff4757;
		background: rgba(255, 71, 87, 0.1);
		padding: 4rpx 12rpx;
		border-radius: 12rpx;
		margin-left: 16rpx;
		border: 1rpx solid #ff4757;
		font-weight: 500;
		white-space: nowrap;
	}

	.report-date {
		font-size: 24rpx;
  color: #94a3b8;
  margin-bottom: 12rpx;
	}

	.report-desc {
		font-size: 28rpx;
  color: #64748b;
  line-height: 1.5;
}

/* 右侧状态区域 */
.report-status-area {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: space-between;
  min-width: 120rpx;
}

.report-status {
  font-size: 24rpx;
  color: #fff;
  background: #27ae60;
  padding: 6rpx 20rpx;
  border-radius: 20rpx;
  text-align: center;
	}

	.view-detail {
		font-size: 26rpx;
		color: #3ec6c6;
  display: flex;
  align-items: center;
  padding: 6rpx 0;
}

.view-detail:after {
  content: '>';
  margin-left: 4rpx;
  font-family: "PingFang SC", sans-serif;
	}

	/* 空状态 */
	.empty-state {
		text-align: center;
  padding: 60rpx 30rpx;
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	}

	.empty-text {
		font-size: 32rpx;
  color: #64748b;
		font-weight: 500;
  margin-bottom: 30rpx;
	}

	.empty-desc {
  font-size: 28rpx;
  color: #94a3b8;
  line-height: 1.6;
}

/* 响应式调整 */
@media screen and (max-width: 750px) {
  .health-record-container {
    padding: 0;
  }

  .tab-navigation {
    padding: 20rpx;
    margin-bottom: 20rpx;
    border-radius: 0;
  }

  .content-header,
  .report-card {
    padding: 20rpx;
    margin-bottom: 20rpx;
  }
  
  .report-content {
    padding-right: 16rpx;
  }
  
  .report-status-area {
    min-width: 100rpx;
		}
	}
</style>
