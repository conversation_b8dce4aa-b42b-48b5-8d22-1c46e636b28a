@font-face {
  font-family: "iconfontB"; /* Project id 4974744 */
  src: url('@/static/fontB/iconfont.woff2') format('woff2'),
       url('@/static/fontB/iconfont.ttf') format('truetype');
}

.iconfontB {
  font-family: "iconfontB" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-shezhiye_yuedushezhi:before {
  content: "\e654";
}

.icon-fanhui:before {
  content: "\e61a";
}

.icon-yejianmoshi:before {
  content: "\e633";
}

.icon-yueduye-zidongfanyex:before {
  content: "\e605";
}

.icon-fanhui1:before {
  content: "\e62c";
}

.icon-icon_mulu:before {
  content: "\e60c";
}

.icon-fanhui2:before {
  content: "\e612";
}

.icon-rijian:before {
  content: "\e648";
}

.icon-shangcheng:before {
  content: "\e606";
}

.icon-jiaolv:before {
  content: "\e638";
}

.icon-xinwen:before {
  content: "\e7f7";
}

.icon-shipin:before {
  content: "\e656";
}

.icon-xinwen1:before {
  content: "\eb42";
}

.icon-wp-sj-2:before {
  content: "\e720";
}

.icon-jineng:before {
  content: "\e61e";
}

.icon-tianping:before {
  content: "\e746";
}

.icon-xuexijincheng:before {
  content: "\e626";
}

.icon-xinlipingce:before {
  content: "\e6df";
}

.icon-jincheng:before {
  content: "\e66f";
}

.icon-jifen:before {
  content: "\e61f";
}

.icon-jiashujia-weijiashujia:before {
  content: "\e602";
}

.icon-xuexijindu:before {
  content: "\e71f";
}

