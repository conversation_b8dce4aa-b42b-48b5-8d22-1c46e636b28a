<template>
	<view class="container">
		<!-- 页面内容（支持下拉刷新） -->
		<scroll-view
			class="content-scroll"
			scroll-y
			refresher-enabled
			:refresher-triggered="refreshing"
			@refresherrefresh="handleRefresh"
		>
		<!-- 搜索框部分 -->
		<view class="search-wrapper">
			<view class="search-box">
				<view class="search-input-box">
					<uni-icons type="search" size="16" color="#C0C4CC"></uni-icons>
					<input
						class="search-input"
						type="text"
						v-model="searchKeyword"
						placeholder="搜索舌质名称、特征或主病"
						placeholder-class="input-placeholder"
						confirm-type="search"
						@input="onSearchInput"
						@focus="onSearchFocus"
						@blur="onSearchBlur"
						@confirm="onSearch"
					/>
					<uni-icons
						v-if="searchKeyword"
						type="clear"
						size="16"
						color="#C0C4CC"
						@click="clearSearch"
					></uni-icons>
				</view>
			</view>
		</view>

		<!-- 搜索历史 -->
		<view v-if="showSearchHistory && searchHistory.length > 0" class="search-history">
			<view class="history-header">
				<text class="history-title">搜索历史</text>
				<text class="clear-history" @click="clearSearchHistory">清空</text>
			</view>
			<view class="history-tags">
				<view
					class="history-tag"
					v-for="(item, index) in searchHistory"
					:key="index"
					@click="selectHistoryItem(item)"
				>
					<text class="history-text">{{item}}</text>
				</view>
			</view>
		</view>

		<!-- 搜索建议 -->
		<view v-if="searchKeyword && searchSuggestions.length > 0" class="search-suggestions">
			<view class="suggestion-header">
				<text class="suggestion-title">搜索建议</text>
			</view>
			<view class="suggestion-list">
				<view
					class="suggestion-item"
					v-for="(item, index) in searchSuggestions"
					:key="index"
					@click="selectSuggestion(item)"
				>
					<uni-icons type="search" size="14" color="#999"></uni-icons>
					<text class="suggestion-text">{{item}}</text>
				</view>
			</view>
		</view>

		<!-- 无搜索结果提示 -->
		<view v-if="searchKeyword && !hasSearchResults && !showSearchHistory" class="no-result">
			<text class="no-result-text">未找到相关舌象信息</text>
			<text class="no-result-tip">试试搜索：红舌、胖舌、薄苔等</text>
		</view>

		<!-- 动态渲染分类 -->
		<view v-for="category in visibleCategories" :key="category.key" class="category-section">
			<view class="category-title">
				<text class="title-text">{{category.title}}</text>
			</view>
			<view class="tag-list">
				<view class="tag-item" v-for="item in getFilteredData(category.key)" :key="item.id" @click="onTagClick(item)">
					<text class="tag-text">{{item.name}}</text>
				</view>
			</view>
		</view>
		</scroll-view>
	</view>
</template>

<script>
import { tongueList } from '@/api/system/tongue.js'
import TongueCache from '@/utils/tongueCache'

export default {
	data() {
		return {
			searchKeyword: '',
			loading: false,
			refreshing: false, // 下拉刷新状态
			searchHistory: [], // 搜索历史
			showSearchHistory: false, // 是否显示搜索历史
			searchDebounceTimer: null, // 防抖定时器
			// 分类配置
			categories: [
				{
					key: 'tongueSpirit',
					title: '舌神',
					data: []
				},
				{
					key: 'tongueColors',
					title: '舌色',
					data: []
				},
				{
					key: 'tongueShapes',
					title: '舌形',
					data: []
				},
				{
					key: 'tongueStates',
					title: '舌态',
					data: []
				}
			],
			// API数据存储
			tongueSpirit: [],
			tongueColors: [],
			tongueShapes: [],
			tongueStates: []
		}
	},

	mounted() {
		this.loadTongueData()
		this.loadSearchHistory()
	},
	computed: {
		// 可见的分类（有数据或搜索结果）
		visibleCategories() {
			return this.categories.filter(category => {
				const filteredData = this.getFilteredData(category.key)
				return !this.searchKeyword || filteredData.length > 0
			})
		},
		// 是否有搜索结果
		hasSearchResults() {
			return this.categories.some(category =>
				this.getFilteredData(category.key).length > 0
			)
		},
		// 搜索建议
		searchSuggestions() {
			if (!this.searchKeyword || this.searchKeyword.length < 1) {
				return []
			}

			const keyword = this.searchKeyword.toLowerCase()
			const suggestions = new Set()

			// 从所有数据中提取建议
			const allData = [
				...this.tongueSpirit,
				...this.tongueColors,
				...this.tongueShapes,
				...this.tongueStates
			]

			allData.forEach(item => {
				// 名称匹配
				if (item.name && item.name.toLowerCase().includes(keyword)) {
					suggestions.add(item.name)
				}

				// 特征描述中的关键词
				if (item.characteristics) {
					const chars = item.characteristics.match(/[\u4e00-\u9fa5]{2,4}/g) || []
					chars.forEach(char => {
						if (char.toLowerCase().includes(keyword)) {
							suggestions.add(char)
						}
					})
				}
			})

			// 预定义的常用搜索词
			const commonTerms = [
				'红舌', '淡舌', '紫舌', '绛舌', '青舌',
				'胖舌', '瘦舌', '厚舌', '薄舌', '裂纹舌', '齿痕舌',
				'舌神荣', '舌神枯', '舌态强硬', '舌态痿软',
				'热证', '寒证', '虚证', '实证', '湿证', '燥证'
			]

			commonTerms.forEach(term => {
				if (term.toLowerCase().includes(keyword)) {
					suggestions.add(term)
				}
			})

			return Array.from(suggestions).slice(0, 8) // 最多显示8个建议
		}
	},
	methods: {
		// 加载舌象数据（支持缓存）
		async loadTongueData(forceRefresh = false) {
			try {
				this.loading = true
				uni.showLoading({ title: '加载中...' })

				// 1. 优先从缓存加载（除非强制刷新）
				if (!forceRefresh) {
					const cacheResult = TongueCache.loadRawData()
					if (cacheResult.success) {
						console.log('从缓存加载舌质数据成功')
						this.processCachedData(cacheResult.data)
						return
					}
				}

				// 2. 缓存无效或强制刷新，请求API
				console.log('缓存无效，请求API获取舌质数据')
				const params = {
					pageNum: 1,
					pageSize: 150
				}
				const response = await tongueList(params)
				console.log('舌象特征API响应:', response)

				if (response && response.rows) {
					// 3. 保存原始数据到缓存
					TongueCache.saveRawData(response.rows)
					// 4. 处理数据
					this.processTongueData(response.rows)
				} else if (response && Array.isArray(response)) {
					// 3. 保存原始数据到缓存
					TongueCache.saveRawData(response)
					// 4. 处理数据
					this.processTongueData(response)
				} else {
					console.warn('API返回数据格式异常:', response)
					// 如果API失败，尝试使用缓存数据
					const cacheResult = TongueCache.loadRawData()
					if (cacheResult.success) {
						console.log('API失败，使用缓存数据')
						this.processCachedData(cacheResult.data)
						uni.showToast({
							title: '使用缓存数据',
							icon: 'none'
						})
					} else {
						this.initCategories() // 使用空数据初始化
					}
				}
			} catch (error) {
				console.error('加载舌象数据失败:', error)

				// API失败时尝试使用缓存数据
				const cacheResult = TongueCache.loadRawData()
				if (cacheResult.success) {
					console.log('API失败，使用缓存数据')
					this.processCachedData(cacheResult.data)
					uni.showToast({
						title: '网络异常，显示缓存数据',
						icon: 'none'
					})
				} else {
					uni.showToast({
						title: '加载失败',
						icon: 'none'
					})
					this.initCategories() // 使用空数据初始化
				}
			} finally {
				this.loading = false
				uni.hideLoading()
			}
		},

		// 处理缓存数据（使用统一的缓存管理器）
		processCachedData(rawData) {
			console.log('处理缓存的舌质数据:', rawData.length, '条')

			// 使用缓存管理器的统一处理方法，只处理舌质数据
			const processedData = TongueCache.processTongueData(rawData, 'tongue')

			// 更新页面数据
			this.tongueSpirit = processedData.tongueData.spirits
			this.tongueColors = processedData.tongueData.colors
			this.tongueShapes = processedData.tongueData.shapes
			this.tongueStates = processedData.tongueData.states

			// 输出统计信息
			console.log('缓存舌质数据分类统计:')
			console.log('舌神:', this.tongueSpirit.length, '条')
			console.log('舌色:', this.tongueColors.length, '条')
			console.log('舌形:', this.tongueShapes.length, '条')
			console.log('舌态:', this.tongueStates.length, '条')
		},

		// 处理API返回的舌象数据
		processTongueData(data) {
			console.log('处理舌象数据:', data)
			console.log('数据总数:', Array.isArray(data) ? data.length : 0)

			// 清空现有数据
			this.tongueSpirit = []
			this.tongueColors = []
			this.tongueShapes = []
			this.tongueStates = []

			// 根据API返回的数据结构进行分类
			if (Array.isArray(data)) {
				data.forEach(item => {

					// 过滤条件：只处理舌质相关的数据，排除舌苔内容
					const category = item.category || ''
					const subCategory = item.subCategory || ''

					// 只处理舌质相关的数据，排除舌苔
					if (category !== '舌质' && !category.includes('舌质')) {
						return
					}

					// 根据API返回的数据结构处理
					const processedItem = {
						id: item.id || Math.random().toString(36).substring(2, 11),
						name: item.name || '未知舌象',
						characteristics: item.description || '暂无特征描述',
						clinicalSignificance: item.clinicalMeaning || '暂无临床意义',
						mechanism: item.pathogenesis || '暂无机理说明',
						mainDisease: item.mainDisease || '暂无主病信息',
						referencesinfo:item.referencesinfo || '暂无参考书籍信息',
						subCategory: subCategory,
						category: category
					}


					// 根据subCategory字段进行分类
					switch (subCategory) {
						case '舌神':
							this.tongueSpirit.push(processedItem)
							break
						case '舌色':
							this.tongueColors.push(processedItem)
							break
						case '舌形':
							this.tongueShapes.push(processedItem)
							break
						case '舌态':
							this.tongueStates.push(processedItem)
							break
						default:
							// 如果subCategory为空或未知，跳过该数据
							console.log(`跳过未知subCategory的数据: ${item.name}, subCategory: ${subCategory}`)
							break
					}
				})
			}

			console.log('分类后的数据统计:')
			console.log('舌神:', this.tongueSpirit.length, '条')
			console.log('舌色:', this.tongueColors.length, '条')
			console.log('舌形:', this.tongueShapes.length, '条')
			console.log('舌态:', this.tongueStates.length, '条')
			console.log('总计:', this.tongueSpirit.length + this.tongueColors.length + this.tongueShapes.length + this.tongueStates.length, '条舌质数据')

			// 初始化分类数据
			this.initCategories()
		},



		// 初始化分类数据
		initCategories() {
			// 更新各分类的数据
			this.categories[0].data = this.tongueSpirit // 舌神
			this.categories[1].data = this.tongueColors // 舌色
			this.categories[2].data = this.tongueShapes // 舌形
			this.categories[3].data = this.tongueStates // 舌态

			console.log('分类数据更新完成:', this.categories.map(cat => ({
				title: cat.title,
				count: cat.data.length
			})))
		},

		// 获取过滤后的数据
		getFilteredData(categoryKey) {
			// 根据categoryKey获取对应的数据
			let data = []
			switch (categoryKey) {
				case 'tongueSpirit':
					data = this.tongueSpirit
					break
				case 'tongueColors':
					data = this.tongueColors
					break
				case 'tongueShapes':
					data = this.tongueShapes
					break
				case 'tongueStates':
					data = this.tongueStates
					break
				default:
					data = []
			}

			if (!this.searchKeyword) return data

			const keyword = this.searchKeyword.toLowerCase()
			return data.filter(item => {
				// 只搜索名称、特征和主病
				const searchFields = [
					item.name,
					item.characteristics,
					item.mainDisease
				]

				return searchFields.some(field =>
					field && field.toLowerCase().includes(keyword)
				)
			})
		},

		// 搜索输入处理（实时搜索）
		onSearchInput() {
			this.showSearchHistory = false

			// 防抖处理
			if (this.searchDebounceTimer) {
				clearTimeout(this.searchDebounceTimer)
			}

			this.searchDebounceTimer = setTimeout(() => {
				this.performSearch()
			}, 300) // 300ms 防抖
		},

		// 搜索框获得焦点
		onSearchFocus() {
			if (!this.searchKeyword) {
				this.showSearchHistory = true
			}
		},

		// 搜索框失去焦点
		onSearchBlur() {
			// 延迟隐藏，避免点击搜索历史时立即隐藏
			setTimeout(() => {
				this.showSearchHistory = false
			}, 200)
		},

		// 执行搜索
		performSearch() {
			if (this.searchKeyword.trim()) {
				console.log('执行搜索:', this.searchKeyword)
				// 触发视图更新
				this.$forceUpdate()
			}
		},

		// 清除搜索
		clearSearch() {
			this.searchKeyword = ''
			this.showSearchHistory = false
			this.performSearch()
		},

		// 搜索处理（确认搜索）
		onSearch() {
			const keyword = this.searchKeyword.trim()
			if (keyword) {
				console.log('确认搜索关键词:', keyword)
				this.addToSearchHistory(keyword)
				this.showSearchHistory = false
				this.performSearch()
			}
		},

		// 添加到搜索历史
		addToSearchHistory(keyword) {
			if (!keyword || keyword.length < 1) return

			// 移除重复项
			const index = this.searchHistory.indexOf(keyword)
			if (index > -1) {
				this.searchHistory.splice(index, 1)
			}

			// 添加到开头
			this.searchHistory.unshift(keyword)

			// 限制历史记录数量
			if (this.searchHistory.length > 10) {
				this.searchHistory = this.searchHistory.slice(0, 10)
			}

			// 保存到本地存储
			this.saveSearchHistory()
		},

		// 选择搜索历史项
		selectHistoryItem(item) {
			this.searchKeyword = item
			this.showSearchHistory = false
			this.performSearch()
		},

		// 选择搜索建议
		selectSuggestion(suggestion) {
			this.searchKeyword = suggestion
			this.addToSearchHistory(suggestion)
			this.performSearch()
		},

		// 清空搜索历史
		clearSearchHistory() {
			this.searchHistory = []
			this.saveSearchHistory()
		},

		// 加载搜索历史
		loadSearchHistory() {
			try {
				const history = uni.getStorageSync('tongue_search_history')
				if (history) {
					this.searchHistory = JSON.parse(history)
				}
			} catch (error) {
				console.error('加载搜索历史失败:', error)
				this.searchHistory = []
			}
		},

		// 保存搜索历史
		saveSearchHistory() {
			try {
				uni.setStorageSync('tongue_search_history', JSON.stringify(this.searchHistory))
			} catch (error) {
				console.error('保存搜索历史失败:', error)
			}
		},
		// 标签点击处理
		onTagClick(tag) {
			console.log('选中标签:', tag.name)
			// 跳转到详情页面，传递完整的舌象数据
			uni.navigateTo({
				url: `/pages/gather/tongue/detail?id=${tag.id}&category=${tag.subCategory || tag.category}&data=${encodeURIComponent(JSON.stringify(tag))}`
			})
		},

		// 下拉刷新
		handleRefresh() {
			console.log('触发下拉刷新')
			this.refreshing = true

			// 强制刷新数据
			this.loadTongueData(true).then(() => {
				uni.showToast({
					title: '刷新成功',
					icon: 'success',
					duration: 1500
				})
			}).catch((error) => {
				console.error('下拉刷新失败:', error)
				uni.showToast({
					title: '刷新失败',
					icon: 'none'
				})
			}).finally(() => {
				this.refreshing = false
			})
		},

		// 检查缓存状态（调试用）
		checkCacheStatus() {
			const status = TongueCache.getCacheStatus()
			console.log('舌质缓存状态:', status)
			return status
		},

		// 清除缓存（调试用）
		clearCache() {
			TongueCache.clearCache()
			uni.showToast({
				title: '缓存已清除',
				icon: 'success'
			})
		}
	}
}
</script>

<style lang="scss" scoped>
/* 滚动容器 */
.content-scroll {
	height: 100vh;
}

.container {
	min-height: 100vh;
	background: #FFFFFF;
}

.search-wrapper {
	padding: 20rpx 30rpx;
	background-color: #FFFFFF;
}

.search-box {
	position: relative;
	width: 100%;
}

.search-input-box {
	display: flex;
	align-items: center;
	height: 72rpx;
	padding: 0 24rpx;
	background-color: #faf6f2;
	border-radius: 36rpx;

	.uni-icons {
		display: flex;
		align-items: center;
		justify-content: center;
	}
}

.search-input {
	flex: 1;
	height: 100%;
	margin: 0 20rpx;
	font-size: 28rpx;
	color: #333333;
	background-color: transparent;
}

.input-placeholder {
	color: #C0C4CC;
	font-size: 28rpx;
}

/* 分类区域 */
.category-section {
	margin: 30rpx 0;
	background: #fff;
	padding: 30rpx;
}

.category-title {
	margin-bottom: 30rpx;
}

.title-text {
	font-size: 30rpx;
	color: #7e6b5a;
	font-weight: normal;
}

/* 标签列表 */
.tag-list {
	display: flex;
	flex-wrap: wrap;
	margin: -10rpx;
}

.tag-item {
	margin: 10rpx;
	padding: 16rpx 30rpx;
	background: #f8f9fc;
	border-radius: 40rpx;
	transition: all 0.3s ease;
}

.tag-item:active {
	opacity: 0.8;
	transform: scale(0.98);
}

.tag-text {
	font-size: 28rpx;
	color: #666;
}

.no-result {
	padding: 40rpx 0;
	text-align: center;
	.no-result-text {
		color: #999;
		font-size: 28rpx;
		margin-bottom: 10rpx;
	}
	.no-result-tip {
		color: #ccc;
		font-size: 24rpx;
	}
}

/* 搜索历史样式 */
.search-history {
	background: #fff;
	margin: 20rpx 30rpx;
	border-radius: 16rpx;
	padding: 30rpx;
	box-shadow: 0 2px 8px rgba(0,0,0,0.06);
}

.history-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.history-title {
	font-size: 28rpx;
	color: #333;
	font-weight: 600;
}

.clear-history {
	font-size: 24rpx;
	color: #3ec6c6;
	padding: 8rpx 16rpx;
	border-radius: 12rpx;
	background: rgba(62, 198, 198, 0.1);
}

.history-tags {
	display: flex;
	flex-wrap: wrap;
	gap: 16rpx;
}

.history-tag {
	padding: 12rpx 20rpx;
	background: #f8f9fc;
	border-radius: 20rpx;
	border: 1px solid #e6f7fa;
}

.history-text {
	font-size: 26rpx;
	color: #666;
}

/* 搜索建议样式 */
.search-suggestions {
	background: #fff;
	margin: 0 30rpx 20rpx;
	border-radius: 16rpx;
	padding: 30rpx;
	box-shadow: 0 2px 8px rgba(0,0,0,0.06);
}

.suggestion-header {
	margin-bottom: 20rpx;
}

.suggestion-title {
	font-size: 28rpx;
	color: #333;
	font-weight: 600;
}

.suggestion-list {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.suggestion-item {
	display: flex;
	align-items: center;
	padding: 16rpx 20rpx;
	border-radius: 12rpx;
	background: #f8f9fc;
	gap: 16rpx;
	transition: background 0.2s;
}

.suggestion-item:active {
	background: #e6f7fa;
}

.suggestion-text {
	font-size: 26rpx;
	color: #666;
	flex: 1;
}

.count-text {
	font-size: 24rpx;
	color: #666;
	margin-left: 10rpx;
}
</style>