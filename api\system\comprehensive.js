import config from '@/config'
import { getToken, handleTokenExpired, getTokenExpiredHandlingStatus } from '@/utils/auth'

/**
 * 综合检测API - 完全按照舌诊上传的模式重写
 */

/**
 * 提交综合检测数据
 * @param {Object} data 综合检测数据
 * @param {Object} data.userInfo 用户信息
 * @param {Object} data.tongueData 舌诊数据
 * @param {Object} data.constitutionData 体质检测数据
 * @returns {Promise} API响应
 */
export function submitComprehensiveDetection(data) {
  return new Promise((resolve, reject) => {
    // 获取数据
    const userInfo = data.userInfo || {}
    const tongueData = data.tongueData || {}
    const constitutionData = data.constitutionData || {}

    // 获取舌象照片路径
    const photoPath = tongueData.photoPath

    if (!photoPath) {
      reject(new Error('综合检测需要舌象照片'))
      return
    }

    // 计算年龄的辅助函数
    const calculateAge = (dateBirth) => {
      if (!dateBirth) return 0
      const birth = new Date(dateBirth)
      const now = new Date()
      let age = now.getFullYear() - birth.getFullYear()
      const monthDiff = now.getMonth() - birth.getMonth()
      if (monthDiff < 0 || (monthDiff === 0 && now.getDate() < birth.getDate())) {
        age--
      }
      return age
    }

    // 构建formData - 完全按照舌诊上传的格式
    const formData = {
      // 用户基本信息（与舌诊上传完全一致）
      TimeStamp: userInfo.TimeStamp || new Date().getTime(),
      person_name: userInfo.name,
      sex: userInfo.sex,
      dateBirth: userInfo.dateBirth,
      phone: userInfo.phonenumber,
      occupation: userInfo.occupation || '',
      maritalStatus: userInfo.maritalStatus || '',
      height: userInfo.height,
      weight: userInfo.weight,
      age: userInfo.age || calculateAge(userInfo.dateBirth), // 优先使用存储的年龄，否则计算年龄
      diastolicPressure: userInfo.diastolicPressure || '',
      systolicPressure: userInfo.systolicPressure || '',
      allergy: userInfo.allergy || '',
      medicalHistory: userInfo.medicalHistory || '',
      UserseeionType: 3

    }

    // 添加体质检测数据到formData中
    if (constitutionData && constitutionData.apiData) {
      const apiData = constitutionData.apiData
      console.log('体质检测apiData:', apiData)

      // 添加体质计分数据
      formData.pinghe = apiData.pinghe || 0
      formData.qixu_zhi = apiData.qixu_zhi || 0
      formData.qixu_zheng = apiData.qixu_zheng || 0
      formData.yangxu_zhi = apiData.yangxu_zhi || 0
      formData.yangxu_zheng = apiData.yangxu_zheng || 0
      formData.yinxu_zhi = apiData.yinxu_zhi || 0
      formData.yinxu_zheng = apiData.yinxu_zheng || 0
      formData.tanshi_zhi = apiData.tanshi_zhi || 0
      formData.tanshi_zheng = apiData.tanshi_zheng || 0
      formData.xueyu_zhi = apiData.xueyu_zhi || 0
      formData.xueyu_zheng = apiData.xueyu_zheng || 0
      formData.shire_zhi = apiData.shire_zhi || 0
      formData.shire_zheng = apiData.shire_zheng || 0

      // 添加用户答题数据（从apiData中获取，已经是JSON字符串格式）
      formData.answers = apiData.answers || ''
      formData.followups = apiData.followups || ''
    } else {
      console.warn('没有找到体质检测数据:', constitutionData)
    }

    // 如果体质检测数据中没有答题数据，尝试从constitutionData根级别获取
    if (!formData.answers && constitutionData && constitutionData.answers) {
      formData.answers = constitutionData.answers
    }
    if (!formData.followups && constitutionData && constitutionData.followups) {
      formData.followups = constitutionData.followups
    }

    // console.log('添加的体质计分数据:', {
    //   pinghe: formData.pinghe,
    //   qixu_zhi: formData.qixu_zhi,
    //   qixu_zheng: formData.qixu_zheng,
    //   yangxu_zhi: formData.yangxu_zhi,
    //   yangxu_zheng: formData.yangxu_zheng,
    //   yinxu_zhi: formData.yinxu_zhi,
    //   yinxu_zheng: formData.yinxu_zheng,
    //   tanshi_zhi: formData.tanshi_zhi,
    //   tanshi_zheng: formData.tanshi_zheng,
    //   xueyu_zhi: formData.xueyu_zhi,
    //   xueyu_zheng: formData.xueyu_zheng,
    //   shire_zhi: formData.shire_zhi,
    //   shire_zheng: formData.shire_zheng
    // })

    console.log('=== 综合检测数据发送详情 ===')
    console.log('API地址:', config.baseUrl + '/common/uploadAppsynthesis')
    console.log('舌象照片路径:', photoPath)
    console.log('原始用户信息:', userInfo)
    console.log('原始舌诊数据:', tongueData)
    console.log('原始体质数据:', constitutionData)
    console.log('用户信息字段:', {
      TimeStamp: formData.TimeStamp,
      name: formData.name,
      sex: formData.sex,
      dateBirth: formData.dateBirth,
      phonenumber: formData.phonenumber
    })
    console.log('体质检测字段:', {
      pinghe: formData.pinghe,
      qixu_zhi: formData.qixu_zhi,
      yangxu_zhi: formData.yangxu_zhi,
      yinxu_zhi: formData.yinxu_zhi,
      tanshi_zhi: formData.tanshi_zhi,
      xueyu_zhi: formData.xueyu_zhi,
      shire_zhi: formData.shire_zhi,
      answers: formData.answers,
      followups: formData.followups
    })
    console.log('完整formData:', formData)
    console.log('formData字段详情:')
    Object.keys(formData).forEach(key => {
      console.log(`  ${key}: ${formData[key]} (${typeof formData[key]})`)
    })

    // 使用uni.uploadFile进行文件上传 - 完全按照舌诊上传的模式
    uni.uploadFile({
      timeout: 20000,
      url: config.baseUrl + '/common/uploadAppsynthesis',
      filePath: photoPath,
      name: 'file',
      header: {
        'Authorization': 'Bearer ' + getToken()
      },
      formData: formData,
      success: (response) => {
        console.log("综合检测上传后返回的结果：", response);
        try {
          const res = JSON.parse(response.data)
          console.log("综合检测解析后的结果：", res);

          // 优先检查token过期情况
          if (res.code === 401) {
            console.log("检测到401状态码，token过期")
            uni.hideLoading()

            // 检查是否已在处理token过期，避免重复弹窗
            if (!getTokenExpiredHandlingStatus()) {
              console.log("综合检测中调用统一的token过期处理方法")
              handleTokenExpired()
            } else {
              console.log("Token过期处理已在进行中，跳过重复处理")
            }
            return
          }

          // 检查是否是舌体检测失败的特殊情况
          if (res.msg && (res.msg.includes('未检测到舌体') || res.msg.includes('检测失败'))) {
            console.error('=== 舌体检测失败详情 ===')
            console.error('错误消息:', res.msg)
            console.error('完整响应:', res)
            reject(new Error('未能检测到舌体'))
            return
          }

          if (res.code === 500) {
            console.warn('服务器返回500错误，但继续处理以获取更多信息:', res)
            // 暂时不拒绝，让我们看看是否有有用的数据
            if (res.msg === 'null' || !res.msg) {
              console.log('500错误但msg为null，可能是数据格式问题')
              // 检查是否有其他有用的响应数据
              if (res.data || res.result || res.externalResponse) {
                console.log('发现可能的有效数据，尝试处理')
                resolve(res)
                return
              }
            }
            reject(new Error(res.msg || '服务器内部错误'))
            return
          }

          if (res.code !== 200) {
            reject(new Error(res.msg || '请求失败'))
            return
          }

          // 成功返回结果
          resolve(res)

        } catch (error) {
          console.error("综合检测解析响应数据失败：", error);
          reject(new Error('响应数据解析失败'))
        }
      },
      fail: (error) => {
        console.error("综合检测上传失败：", error);
        reject(error)
      }
    })
  })
}
