<template>
  <view class="analyse-container">
    <view class="form-title">用户问诊信息</view>
    <view class="form-desc">请填写用户问诊的基本信息</view>

    <view class="form-content">
      <!-- 必填信息 -->
      <view class="section-title">基本信息（<text class="required">*</text>为必填项）</view>

      <view class="form-item">
        <view class="label">姓名 <text class="required">*</text></view>
        <input v-model="userInfo.name" placeholder="请输入姓名" class="input" />
      </view>

      <view class="form-item">
        <view class="label">性别 <text class="required">*</text></view>
        <radio-group @change="onSexChange" class="radio-group">
          <label class="radio-item">
            <radio value="男" :checked="userInfo.sex === '男'" />
            <text>男</text>
          </label>
          <label class="radio-item">
            <radio value="女" :checked="userInfo.sex === '女'" />
            <text>女</text>
          </label>
        </radio-group>
      </view>

      <view class="form-item date-picker-form-item">
        <view class="label">出生年月 <text class="required">*</text></view>
        <view class="date-picker-container" @click="showDatePicker">
          <view class="picker">{{ formatDisplayDate(userInfo.dateBirth) || '请选择出生年月' }}</view>
          <view class="picker-arrow">></view>
        </view>
      </view>

      <view class="form-item">
        <view class="label">手机号 <text class="required">*</text></view>
        <input v-model="userInfo.phonenumber" @input="handlePhoneInput" placeholder="请输入手机号" type="number" class="input" />
      </view>

      <view class="form-item">
        <view class="label">身高(cm) <text class="required">*</text></view>
        <input v-model="userInfo.height" placeholder="请输入身高" type="number" class="input" />
      </view>

      <view class="form-item">
        <view class="label">体重(kg) <text class="required">*</text></view>
        <input v-model="userInfo.weight" placeholder="请输入体重" type="number" class="input" />
      </view>
      <view class="form-item">
        <view class="label">职业</view>
        <input v-model="userInfo.occupation" placeholder="请输入职业" class="input" />
      </view>

      <view class="form-item">
        <view class="label">婚姻状况</view>
        <radio-group @change="onMaritalChange" class="radio-group">
          <label class="radio-item">
            <radio value="未婚" :checked="userInfo.maritalStatus === '未婚'" />
            <text>未婚</text>
          </label>
          <label class="radio-item">
            <radio value="已婚" :checked="userInfo.maritalStatus === '已婚'" />
            <text>已婚</text>
          </label>
        </radio-group>
      </view>
      <!-- 可选信息 -->
      <view class="section-title">健康信息（选填）</view>

      <view class="form-item">
        <view class="label">舒张压(mmHg)</view>
        <input v-model="userInfo.diastolicPressure" placeholder="选填" type="digit" class="input" />
      </view>

      <view class="form-item">
        <view class="label">收缩压(mmHg)</view>
        <input v-model="userInfo.systolicPressure" placeholder="选填" type="digit" class="input" />
      </view>

      <view class="form-item">
        <view class="label">过敏史</view>
        <textarea v-model="userInfo.allergy" placeholder="请描述过敏史（选填）" class="textarea" />
      </view>

      <view class="form-item">
        <view class="label">病史</view>
        <textarea v-model="userInfo.medicalHistory" placeholder="请描述病史（选填）" class="textarea" />
      </view>
    </view>

    <view class="button-group">
      <button @click="saveUserInfo" class="save-btn">{{ getButtonText }}</button>
      <button @click="goBack" class="cancel-btn">取消</button>
    </view>

    <!-- 日期选择器弹窗 -->
    <view v-if="showPicker" class="picker-overlay" @click="hideDatePicker">
      <view class="picker-popup" @click.stop>
        <view class="picker-header">
          <view class="picker-cancel" @click="hideDatePicker">取消</view>
          <view class="picker-title">选择出生年月</view>
          <view class="picker-confirm" @click="confirmDateSelection">确定</view>
        </view>
        <picker-view
          class="date-picker-view"
          :value="pickerValue"
          @change="onPickerChange"
          :indicator-style="indicatorStyle"
        >
          <picker-view-column>
            <view v-for="(year, index) in years" :key="index" class="picker-item">
              {{ year }}年
            </view>
          </picker-view-column>
          <picker-view-column>
            <view v-for="(month, index) in months" :key="index" class="picker-item">
              {{ month }}月
            </view>
          </picker-view-column>
          <picker-view-column>
            <view v-for="(day, index) in days" :key="index" class="picker-item">
              {{ day }}日
            </view>
          </picker-view-column>
        </picker-view>
      </view>
    </view>
  </view>
</template>

<script>
import { addUser, updateUser, getUser } from '@/api/system/userManagement.js'

export default {
  data() {
    return {
      userInfo: {
        name: '',
        sex: '',
        dateBirth: '',
        phonenumber: '',
        occupation: '',
        maritalStatus: '',
        height: '',
        weight: '',
        diastolicPressure: '',
        systolicPressure: '',
        allergy: '',
        medicalHistory: '',
      },
      originalUserInfo: {}, // 保存原始用户信息，用于比较是否有变更
      fromPage: '', // 记录来源页面
      hasExistingUserInfo: false, // 是否存在用户信息

      // picker-view 相关数据
      showPicker: false,
      pickerValue: [0, 0, 0], // 年、月、日的索引
      years: [],
      months: [],
      days: [],
      selectedYear: new Date().getFullYear(),    // 默认当前年份
      selectedMonth: new Date().getMonth() + 1,  // 默认当前月份
      selectedDay: new Date().getDate(),         // 默认当前日期
      indicatorStyle: 'height: 80rpx; background: transparent;'
    }
  },

  computed: {
    // 获取按钮文本 - 基于本地缓存是否已有用户信息
    getButtonText() {
      // 检查本地缓存中是否已有用户信息
      const cachedUserInfo = uni.getStorageSync('userInfo') || {}
      const hasCachedInfo = cachedUserInfo.name || cachedUserInfo.phonenumber

      if (hasCachedInfo) {
        return '更新信息'
      } else {
        return '保存信息'
      }
    }
  },

  onLoad(options) {
    // 记录来源页面
    this.fromPage = options.from || ''

    // 初始化日期选择器数据
    this.initDatePicker()

    // 加载用户信息：优先从缓存，缓存没有则调用API
    this.loadUserInfoWithCache()
  },



  methods: {
    // 处理手机号输入，自动去除空格
    handlePhoneInput(e) {
      // 去除所有空格
      const cleanValue = e.detail.value.replace(/\s/g, '');
      this.userInfo.phonenumber = cleanValue;
    },
    // 加载用户信息：优先从缓存，缓存没有则调用API
    async loadUserInfoWithCache() {
      console.log('开始加载用户信息...')

      // 1. 首先从缓存中获取用户信息
      this.loadUserInfoFromCache()

      // 2. 如果缓存中有用户信息，直接使用
      if (this.userInfo.name || this.userInfo.phonenumber) {
        console.log('从缓存加载用户信息成功')
        return
      }

      // 3. 缓存中没有用户信息，检查网络状态
      console.log('缓存中没有用户信息，尝试从服务器获取...')
      const networkType = await this.checkNetworkStatus()
      if (!networkType || networkType === 'none') {
        console.log('无网络连接，无法获取用户信息')
        return
      }

      // 4. 从服务器获取用户信息并存入缓存
      try {
        uni.showLoading({ title: '加载用户信息...' })
        await this.fetchUserInfoFromServer()
        uni.hideLoading()
      } catch (error) {
        console.error('从服务器获取用户信息失败:', error)
        uni.hideLoading()
      }
    },

    // 从缓存加载用户信息
    loadUserInfoFromCache() {
      // 优先从 userInfo 存储读取（统一的用户信息存储）
      let savedInfo = uni.getStorageSync('userInfo')

      // 如果没有，则从旧的存储位置读取
      if (!savedInfo || Object.keys(savedInfo).length === 0) {
        savedInfo = uni.getStorageSync('User-Informations')
      }

      if (savedInfo && (savedInfo.name || savedInfo.phonenumber)) {
        this.userInfo = { ...this.userInfo, ...savedInfo }
        this.originalUserInfo = JSON.parse(JSON.stringify(this.userInfo))
        this.hasExistingUserInfo = true
        console.log('从缓存加载用户信息:', this.userInfo)
      } else {
        this.hasExistingUserInfo = false
        this.originalUserInfo = JSON.parse(JSON.stringify(this.userInfo))
        console.log('缓存中没有用户信息')
      }
    },

    // 从服务器获取用户信息
    async fetchUserInfoFromServer() {
      try {
        const response = await getUser()
        console.log('服务器响应:', response)

        // 检查响应数据结构
        let serverData = null
        if (response && response.data) {
          if (Array.isArray(response.data) && response.data.length > 0) {
            serverData = response.data[0]
          } else if (typeof response.data === 'object' && Object.keys(response.data).length > 0) {
            serverData = response.data
          }
        }

        if (serverData && (serverData.person_name || serverData.name || serverData.phonenumber || serverData.phone)) {
          // 转换服务器数据到本地格式
          const convertedData = this.convertServerToLocal(serverData)

          // 更新用户信息
          this.userInfo = { ...this.userInfo, ...convertedData }
          this.originalUserInfo = JSON.parse(JSON.stringify(this.userInfo))
          this.hasExistingUserInfo = true

          // 保存到缓存
          this.saveUserInfoToCache()

          console.log('从服务器获取并缓存用户信息成功:', this.userInfo)
        } else {
          console.log('服务器返回的用户信息为空')
        }
      } catch (error) {
        console.error('从服务器获取用户信息失败:', error)
        throw error
      }
    },

    // 保存用户信息到缓存
    saveUserInfoToCache() {
      uni.setStorageSync('userInfo', this.userInfo)
      uni.setStorageSync('User-Informations', this.userInfo)
      console.log('用户信息已保存到缓存')
    },

    // 检查网络状态
    async checkNetworkStatus() {
      return new Promise((resolve) => {
        uni.getNetworkType({
          success: (res) => {
            resolve(res.networkType)
          },
          fail: () => {
            resolve('none')
          }
        })
      })
    },

    // 转换服务器字段到本地字段
    convertServerToLocal(serverData) {
      return {
        name: serverData.person_name || serverData.name || '',
        sex: serverData.sex || '',
        dateBirth: serverData.dateBirth || '',
        phonenumber: serverData.phone || serverData.phonenumber || '',
        occupation: serverData.occupation || '',
        maritalStatus: serverData.maritalStatus || '',
        height: serverData.height || '',
        weight: serverData.weight || '',
        age: serverData.age || '',
        diastolicPressure: serverData.diastolicPressure || '',
        systolicPressure: serverData.systolicPressure || '',
        allergy: serverData.allergy || '',
        medicalHistory: serverData.medicalHistory || '',
        UserseeionType: 3
      }
    },

    // 转换本地字段到服务器字段
    convertLocalToServer(localData) {
      const apiData = {
        person_name: localData.name || '',
        sex: localData.sex || '',
        dateBirth: localData.dateBirth || '',
        phone: localData.phonenumber || '',
        occupation: localData.occupation || '',
        maritalStatus: localData.maritalStatus || '',
        height: localData.height || '',
        weight: localData.weight || '',
        age: localData.age || '',
        diastolicPressure: localData.diastolicPressure || '',
        systolicPressure: localData.systolicPressure || '',
        allergy: localData.allergy || '',
        medicalHistory: localData.medicalHistory || '',
        UserseeionType: 3
      }



      // 验证必填字段
      if (!apiData.person_name) {
        console.warn('警告: 姓名为空')
      }
      if (!apiData.phone) {
        console.warn('警告: 手机号为空')
      }

      return apiData
    },





    onSexChange(e) {
      this.userInfo.sex = e.detail.value
    },

    // 初始化日期选择器
    initDatePicker() {
      const date = new Date()
      const currentYear = date.getFullYear()

      // 生成年份数组（1920年-当前年份）
      this.years = []
      for (let i = 1920; i <= currentYear; i++) {
        this.years.push(i)
      }

      // 生成月份数组
      this.months = []
      for (let i = 1; i <= 12; i++) {
        this.months.push(i)
      }

      // 生成日期数组（1-31）
      this.days = []
      for (let i = 1; i <= 31; i++) {
        this.days.push(i)
      }

      // 设置默认选中值
      if (this.userInfo.dateBirth) {
        this.parseDateBirth(this.userInfo.dateBirth)
      } else {
        // 默认选择当前年月日
        const date = new Date()
        this.selectedYear = date.getFullYear()
        this.selectedMonth = date.getMonth() + 1
        this.selectedDay = date.getDate()
        this.updatePickerValue()
      }
    },

    // 检查日期是否超过当前日期
    isDateExceedCurrent(year, month, day) {
      const currentDate = new Date()
      const selectedDate = new Date(year, month - 1, day)
      return selectedDate > currentDate
    },

    // 获取指定年月的最大可选日期
    getMaxSelectableDay(year, month) {
      const currentDate = new Date()
      const currentYear = currentDate.getFullYear()
      const currentMonth = currentDate.getMonth() + 1
      const currentDay = currentDate.getDate()

      // 如果是当前年月，最大日期为当前日期
      if (year === currentYear && month === currentMonth) {
        return currentDay
      }

      // 如果是未来年月，返回0（不可选）
      if (year > currentYear || (year === currentYear && month > currentMonth)) {
        return 0
      }

      // 如果是过去年月，返回该月的最大天数
      return new Date(year, month, 0).getDate()
    },

    // 更新月份数组（根据选中年份限制月份）
    updateMonths() {
      const currentDate = new Date()
      const currentYear = currentDate.getFullYear()
      const currentMonth = currentDate.getMonth() + 1

      this.months = []

      if (this.selectedYear === currentYear) {
        // 如果是当前年份，只能选择到当前月份
        for (let i = 1; i <= currentMonth; i++) {
          this.months.push(i)
        }
      } else if (this.selectedYear < currentYear) {
        // 如果是过去年份，可以选择全年12个月
        for (let i = 1; i <= 12; i++) {
          this.months.push(i)
        }
      }
      // 如果是未来年份，months数组为空（不可选）
    },

    // 解析已有的出生日期
    parseDateBirth(dateStr) {
      if (!dateStr) return

      const date = new Date(dateStr)
      this.selectedYear = date.getFullYear()
      this.selectedMonth = date.getMonth() + 1
      this.selectedDay = date.getDate()
      this.updatePickerValue()
    },

    // 更新日期数组（根据年月动态计算天数，限制到当前日期）
    updateDays() {
      // 获取最大可选日期
      const maxSelectableDay = this.getMaxSelectableDay(this.selectedYear, this.selectedMonth)

      this.days = []
      if (maxSelectableDay > 0) {
        for (let i = 1; i <= maxSelectableDay; i++) {
          this.days.push(i)
        }
      }
    },

    // 更新picker-view的选中值
    updatePickerValue() {
      const yearIndex = this.years.findIndex(year => year === this.selectedYear)
      const monthIndex = this.selectedMonth - 1  // 月份索引从0开始
      const dayIndex = this.selectedDay - 1      // 日期索引从0开始

      this.pickerValue = [
        yearIndex >= 0 ? yearIndex : 0,
        monthIndex >= 0 ? monthIndex : 0,
        dayIndex >= 0 ? dayIndex : 0
      ]
    },

    // 显示日期选择器
    showDatePicker() {
      this.showPicker = true
      // 重新解析当前日期
      if (this.userInfo.dateBirth) {
        this.parseDateBirth(this.userInfo.dateBirth)
      }
      // 更新所有数组
      this.updateMonths()
      this.updateDays()
      this.updatePickerValue()
    },

    // 隐藏日期选择器
    hideDatePicker() {
      this.showPicker = false
    },

    // picker-view 值改变事件
    onPickerChange(e) {
      const val = e.detail.value
      const newYear = this.years[val[0]]

      // 先更新年份
      this.selectedYear = newYear

      // 更新月份数组（根据年份限制）
      this.updateMonths()

      // 处理月份选择
      if (val[1] < this.months.length) {
        this.selectedMonth = this.months[val[1]]
      } else {
        // 如果选中的月份索引超出范围，选择最后一个可用月份
        this.selectedMonth = this.months[this.months.length - 1] || 1
      }

      // 更新日期数组
      this.updateDays()

      // 处理日期选择
      if (val[2] < this.days.length) {
        this.selectedDay = this.days[val[2]]
      } else {
        // 如果选中的日期索引超出范围，选择最后一个可用日期
        this.selectedDay = this.days[this.days.length - 1] || 1
      }
    },

    // 确认选择日期
    confirmDateSelection() {
      const dateStr = `${this.selectedYear}-${String(this.selectedMonth).padStart(2, '0')}-${String(this.selectedDay).padStart(2, '0')}`
      this.userInfo.dateBirth = dateStr
      this.hideDatePicker()
    },

    // 格式化显示日期
    formatDisplayDate(dateStr) {
      if (!dateStr) return ''

      const date = new Date(dateStr)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')

      return `${year}-${month}-${day}`
    },

    onMaritalChange(e) {
      this.userInfo.maritalStatus = e.detail.value
    },

    validateRequired() {
      // 必填字段（职业和婚姻状况改为选填）
      const required = ['name', 'sex', 'dateBirth', 'phonenumber', 'height', 'weight']

      for (let field of required) {
        if (!this.userInfo[field]) {
          let fieldName = {
            name: '姓名',
            sex: '性别',
            dateBirth: '出生年月',
            phonenumber: '手机号',
            height: '身高',
            weight: '体重'
          }[field]

          uni.showToast({
            title: `请填写${fieldName}`,
            icon: 'none'
          })
          return false
        }
      }

      // 验证手机号格式
      if (!/^1[3-9]\d{9}$/.test(this.userInfo.phonenumber)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
        return false
      }
       const height = parseInt(this.userInfo.height, 10)
      if (isNaN(height) || height < 50 || height > 250) {
      uni.showToast({ title: '请输入有效的身高（50-250cm）', icon: 'none' })
      return false
      }

     // ✅ 体重范围验证（支持小数）
    const weight = parseFloat(this.userInfo.weight)
    if (isNaN(weight) || weight < 1 || weight > 500) {
    uni.showToast({ title: '请输入有效的体重（1-500kg）', icon: 'none' })
    return false
    }

      return true
    },

    // 检查数据是否有变更
    hasDataChanged() {
      // 比较关键字段是否有变更
      const fieldsToCompare = [
        'name', 'sex', 'dateBirth', 'phonenumber', 'occupation',
        'maritalStatus', 'height', 'weight', 'diastolicPressure',
        'systolicPressure', 'allergy', 'medicalHistory'
      ]

      for (const field of fieldsToCompare) {
        const currentValue = this.userInfo[field] || ''
        const originalValue = this.originalUserInfo[field] || ''
        if (currentValue !== originalValue) {
          return true
        }
      }

      return false
    },

    async saveUserInfo() {
      console.log('=== 开始保存用户信息 ===')

      if (!this.validateRequired()) {
        console.log('表单验证失败，停止保存')
        return
      }
      console.log('表单验证通过')

      // 检查本地缓存是否已有用户信息，决定是新增还是更新
      const cachedUserInfo = uni.getStorageSync('userInfo') || {}
      const isUpdate = !!(cachedUserInfo.name || cachedUserInfo.phonenumber)
      console.log('是否为更新模式:', isUpdate)

      // 检查数据是否有变更（仅在更新模式下检查）
      if (isUpdate && !this.hasDataChanged()) {
        console.log('数据未发生变更，停止保存')
        uni.showToast({
          title: '数据未发生变更',
          icon: 'none'
        })
        return
      }
      console.log('数据检查通过，准备保存')

      // 计算年龄
      if (this.userInfo.dateBirth) {
        const birthYear = new Date(this.userInfo.dateBirth).getFullYear()
        const currentYear = new Date().getFullYear()
        this.userInfo.age = currentYear - birthYear
      }

      // 添加时间戳和UserseeionType
      this.userInfo.TimeStamp = new Date().getTime()
      this.userInfo.UserseeionType = 3

      // 检查网络状态
      const networkType = await this.checkNetworkStatus()
      if (!networkType || networkType === 'none') {
        uni.showToast({
          title: '网络不可用，请检查网络连接后重试',
          icon: 'none',
          duration: 3000
        })
        return
      }

      // 在线模式：直接同步到服务器
      uni.showLoading({
        title: '保存中...'
      })

      try {
        // 先同步到服务器
        await this.syncToServer(isUpdate)

        // 服务器响应成功后，保存到本地缓存
        this.saveToLocal()

        uni.hideLoading()
        uni.showToast({
          title: isUpdate ? '信息更新成功' : '信息保存成功',
          icon: 'success'
        })

        setTimeout(() => {
          this.redirectToSourcePage()
        }, 1500)

      } catch (error) {
        console.error('同步到服务器失败:', error)
        uni.hideLoading()

        // 详细错误分析
        let errorMessage = '保存失败，请重试'

        if (error && error.data) {
          // 检查业务状态码（处理HTTP 200但业务错误的情况）
          if (error.data.statusCode === 404) {
            errorMessage = 'API接口不存在，请联系技术支持'
            console.error('API 404错误:', error.data)
          } else if (error.data.statusCode === 500 || error.data.code === 500) {
            errorMessage = '服务器错误，请稍后重试'
            console.error('服务器返回500错误:', error.data.msg || error.data.errMsg || '修改失败')
          } else if (error.data.statusCode && error.data.statusCode !== 200) {
            errorMessage = `请求失败(${error.data.statusCode})，请重试`
            console.error('业务状态码错误:', error.data)
          } else if (error.data.msg) {
            errorMessage = `保存失败: ${error.data.msg}`
          } else if (error.data.errMsg && error.data.errMsg !== 'request:ok') {
            errorMessage = `保存失败: ${error.data.errMsg}`
          }
        }

        uni.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 3000
        })

        // 服务器失败时不跳转页面，让用户可以重新尝试
      }
    },

    // 保存到本地存储（仅在服务器成功响应后调用）
    saveToLocal() {
      uni.setStorageSync('userInfo', this.userInfo)
      uni.setStorageSync('User-Informations', this.userInfo)

      // 更新所有相关时间戳
      const currentTime = Date.now()
      uni.setStorageSync('lastUserInfoUpdateTime', currentTime)
      uni.setStorageSync('lastUserInfoSyncTime', currentTime)

      // 更新原始数据和状态
      this.originalUserInfo = JSON.parse(JSON.stringify(this.userInfo))
      this.hasExistingUserInfo = true

      // 触发按钮文本更新（因为本地缓存已更新）
      this.$forceUpdate()
    },

    // 同步到服务器
    async syncToServer(isUpdate = null) {
      const apiData = this.convertLocalToServer(this.userInfo)

      let response
      // 如果传入了isUpdate参数，使用该参数；否则使用原有逻辑
      const shouldUpdate = isUpdate !== null ? isUpdate : this.hasExistingUserInfo

      if (shouldUpdate) {
        response = await updateUser(apiData)
      } else {
        response = await addUser(apiData)
      }

      return response
    },





    // 设置网络状态监听
    setupNetworkListener() {
      // 保存回调函数引用，用于后续移除监听
      this.networkStatusCallback = () => {
        // 网络状态变化处理
      }

      uni.onNetworkStatusChange(this.networkStatusCallback)
    },

    // 根据来源页面进行重定向
    redirectToSourcePage() {

      switch (this.fromPage) {
        case 'constitution':
          // 来自体质检测页面，重定向到体质检测详情页面
          uni.redirectTo({
            url: '/pages/gather/healthy/detailed'
          })
          break
        case 'tongue':
          // 来自舌诊页面，重定向回舌诊拍摄页面
          uni.redirectTo({
            url: '/pages/gather/diagnosis/diagnosis'
          })
          break
        case 'psychology':
          // 来自心理评测页面，重定向回心理评测页面
          uni.redirectTo({
            url: '/pages/heart/heart/heart'
          })
          break
        default:
          // 默认跳转到首页
          uni.switchTab({
            url: '/pages/index'
          })
          break
      }
    },

    goBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style scoped>
.analyse-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 32rpx;
}

.form-title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  color: #1976d2;
  margin-bottom: 16rpx;
}

.form-desc {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 48rpx;
}

.form-content {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin: 32rpx 0 24rpx 0;
  border-left: 6rpx solid #1976d2;
  padding-left: 16rpx;
}

.section-title:first-child {
  margin-top: 0;
}

.form-item {
  margin-bottom: 24rpx;
}

/* 特别处理出生年月的表单项 */
.date-picker-form-item {
  margin-bottom: 2rpx !important;
}

.label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
}

.required {
  color: #ff4757;
  margin-left: 8rpx;
}

.input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  background: #fff;
}

.input:focus {
  border-color: #1976d2;
}

.textarea {
  width: 100%;
  min-height: 120rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 16rpx 24rpx;
  font-size: 28rpx;
  background: #fff;
}

.date-picker-container {
  position: relative;
  /* 确保容器没有额外的高度 */
  height: auto;
  overflow: hidden;
}

.picker {
  height: 80rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #333;
  cursor: pointer;
  box-sizing: border-box;
  margin: 0;
  line-height: 1;
}

.picker-arrow {
  color: #999;
  font-size: 24rpx;
  transform: rotate(90deg);
}

/* 日期选择器弹窗样式 */
.picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: flex-end;
}

.picker-popup {
  width: 100%;
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #eee;
}

.picker-cancel {
  color: #666;
  font-size: 32rpx;
}

.picker-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.picker-confirm {
  color: #3ec6c6;
  font-size: 32rpx;
  font-weight: bold;
}

.date-picker-view {
  height: 400rpx;
  padding: 20rpx 0;
}

.picker-item {
  height: 80rpx;
  display: flex;
  justify-content: center;
  font-size: 32rpx;
  color: #333;
  line-height: 1.2;
  padding: 0 20rpx;
  box-sizing: border-box;
  transition: all 0.2s;
  /* position: relative; */
  z-index: 10;
}

/* 自定义选中区域样式 */
.date-picker-view {
  position: relative;
}

/* 选中区域背景 */
.date-picker-view::before {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  top: 50%;
  height: 80rpx;
  transform: translateY(-50%);
  background-color: rgba(62, 198, 198, 0.1);
  border-top: 1px solid #3ec6c6;
  border-bottom: 1px solid #3ec6c6;
  z-index: 0;
  pointer-events: none;
}

.radio-group {
  display: flex;
  gap: 48rpx;
}

.radio-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  font-size: 28rpx;
  color: #333;
}

.button-group {
  display: flex;
  gap: 32rpx;
  padding: 0 32rpx;
}

.save-btn {
  flex: 1;
  height: 88rpx;
  background: linear-gradient(135deg, #1976d2, #42a5f5);
  color: #fff;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.cancel-btn {
  flex: 1;
  height: 88rpx;
  background: #f5f5f5;
  color: #666;
  border: 2rpx solid #e0e0e0;
  border-radius: 44rpx;
  font-size: 32rpx;
}
</style>