<template>
	<view class="diet-detail-container">
		<!-- 返回按钮 -->
		<view class="header" :style="{ paddingTop: statusBarHeight + 'px' }">
			<view class="header-content">
				<view class="back-btn" @click="goBack">
					<uni-icons type="left" size="20" color="#3ec6c6"></uni-icons>
					<text class="back-text">返回</text>
				</view>
				<view class="header-title">
					<text class="title-text">{{ currentDietData ? currentDietData.name : '药膳详情' }}</text>
				</view>
				<view class="header-placeholder"></view>
			</view>
		</view>

		<!-- 药膳详情内容 -->
		<scroll-view
			scroll-y
			class="detail-content"
			v-if="currentDietData"
			:style="{ paddingTop: contentPaddingTop }"
		>
			<!-- 食材配料 -->
			<view class="section">
				<view class="section-title">
					<view class="title-icon">🥘</view>
					<text class="title-text">食材配料</text>
				</view>
				<view class="section-content">
					<text class="content-text">{{ currentDietData.ingredients }}</text>
				</view>
			</view>

			<!-- 制作方法 -->
			<view class="section">
				<view class="section-title">
					<view class="title-icon">👨‍🍳</view>
					<text class="title-text">制作方法</text>
				</view>
				<view class="section-content">
					<text class="content-text">{{ currentDietData.method }}</text>
				</view>
			</view>

			<!-- 功效作用 -->
			<view class="section">
				<view class="section-title">
					<view class="title-icon">💊</view>
					<text class="title-text">功效作用</text>
				</view>
				<view class="section-content effect-content">
					<text class="content-text">{{ currentDietData.effect }}</text>
				</view>
			</view>

			<!-- 温馨提示 -->
			<view class="tips-section">
				<view class="tips-title">
					<uni-icons type="info" size="18" color="#ff9500"></uni-icons>
					<text class="tips-text">温馨提示</text>
				</view>
				<view class="tips-content">
					<text class="tips-item">请在专业中医师指导下使用</text>
					<text class="tips-item">孕妇、儿童及特殊体质者慎用</text>
					<text class="tips-item">如有不适请立即停用并咨询医师</text>
				</view>
			</view>
		</scroll-view>

		<!-- 加载状态 -->
		<view class="loading" v-else>
			<uni-icons type="spinner-cycle" size="32" color="#3ec6c6"></uni-icons>
			<text class="loading-text">加载中...</text>
		</view>
	</view>
</template>

<script>
	import { dietDetail } from '@/api/system/diet.js'

	export default {
		name: 'DietaComponent',
		props: {
			dietId: {
				type: [String, Number],
				required: true
			},
			dietData: {
				type: Object,
				default: null
			}
		},
		data() {
			return {
				internalDietData: null,
				statusBarHeight: 20 // 状态栏高度
			}
		},
		computed: {
			// 优先使用传入的dietData，否则使用内部加载的数据
			currentDietData() {
				return this.dietData || this.internalDietData;
			},

			// 计算内容区域的顶部间距
			contentPaddingTop() {
				return `${this.statusBarHeight + 50}px`; // 状态栏高度 + header高度 + 额外间距
			}
		},
		mounted() {
			// 获取状态栏高度
			const systemInfo = uni.getSystemInfoSync();
			this.statusBarHeight = systemInfo.statusBarHeight || 20;
			console.log('状态栏高度:', this.statusBarHeight);
		},
		watch: {
			dietId: {
				immediate: true,
				handler(newId) {
					console.log('dieta组件 - dietId变化:', newId);
					// 如果没有传入dietData，才调用API加载数据
					if (newId && !this.dietData) {
						console.log('未传入dietData，调用API加载数据');
						this.loadDietData(newId);
					} else if (this.dietData) {
						console.log('已传入dietData，跳过API调用:', this.dietData);
					}
				}
			},
			dietData: {
				immediate: true,
				handler(newData) {
					console.log('dieta组件 - dietData变化:', newData);
					if (newData) {
						console.log('使用传入的药膳数据:', newData.name);
					}
				}
			}
		},
		methods: {
			// 加载药膳数据
			async loadDietData(id) {
				console.log('加载药膳数据，ID:', id);
				try {
					uni.showLoading({ title: '加载中...' })

					const response = await dietDetail(id)
					console.log('药膳详情API响应:', response)

					if (response && response.data) {
						// 处理API返回的数据格式
						const item = response.data
						this.internalDietData = {
							name: item.name || item.recipeName || item.title || '未知药膳',
							ingredients: item.ingredients || item.materials || item.ingredient || '暂无配料信息',
							method: item.method || item.steps || item.preparation || item.process || '暂无制作方法',
							effect: item.effect || item.efficacy || item.function || item.benefits || '暂无功效说明'
						}
						console.log('药膳数据加载成功:', this.internalDietData.name)
					} else {
						throw new Error('获取药膳详情失败')
					}
				} catch (error) {
					console.error('加载药膳详情失败:', error)
					uni.showToast({
						title: '加载药膳信息失败',
						icon: 'none'
					})
				} finally {
					uni.hideLoading()
				}
			},

			// 返回上一页
			goBack() {
				uni.navigateBack();
			}
		}
	}
</script>

<style scoped>
.diet-detail-container {
  width: 100%;
  height: 100vh;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 头部样式 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  z-index: 100;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.header-content {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  padding: 0 20rpx;
}

.back-btn {
  display: flex;
  align-items: center;
  background: rgba(62, 198, 198, 0.1);
  padding: 6rpx 12rpx;
  border-radius: 20rpx;
}

.back-text {
  font-size: 28rpx;
  color: #3ec6c6;
  margin-left: 4rpx;
}

.header-title {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  max-width: 60%;
}

.header-title .title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #3ec6c6;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
}

.header-placeholder {
  width: 60rpx;
}

/* 内容区域 */
.detail-content {
  flex: 1;
  padding: 20rpx;
  /* padding-bottom: 40rpx;  */
  box-sizing: border-box;
  background-color: #f8f9fc;
  overflow-y: auto;
  min-height: 100vh;
}

/* 第一个section增加顶部间距 */
/* .detail-content .section:first-child {
  margin-top: 20rpx;
} */

.section {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid rgba(62, 198, 198, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid rgba(62, 198, 198, 0.1);
}

.title-icon {
  margin-right: 12rpx;
  font-size: 36rpx;
}

.section-title .title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #3ec6c6;
  letter-spacing: 1rpx;
}

.section-content {
  padding: 8rpx 0;
}

.content-text {
  font-size: 28rpx;
  color: #4a5568;
  line-height: 1.8;
  word-wrap: break-word;
  white-space: pre-wrap;
  letter-spacing: 0.5rpx;
}

/* 功效作用特殊样式 */
.effect-content {
  background: rgba(62, 198, 198, 0.05);
  border-radius: 16rpx;
  padding: 20rpx;
  margin-top: 8rpx;
  border: 1rpx dashed rgba(62, 198, 198, 0.3);
}

/* 提示区域 */
.tips-section {
  background: #fff8f0;
  border-radius: 20rpx;
  padding: 24rpx;
  margin-top: 32rpx;
  border: 1rpx solid rgba(255, 149, 0, 0.1);
}

.tips-title {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  padding-bottom: 12rpx;
  border-bottom: 1rpx dashed rgba(255, 149, 0, 0.2);
}

.tips-text {
  font-size: 28rpx;
  color: #ff9500;
  margin-left: 8rpx;
  font-weight: 600;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.tips-item {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  display: flex;
  align-items: flex-start;
}

.tips-item::before {
  content: "•";
  margin-right: 8rpx;
  color: #ff9500;
  font-weight: bold;
}

/* 加载状态 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx;
  gap: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #94a3b8;
}

/* 适配小屏幕 */
@media screen and (max-width: 375px) {
  .detail-content {
    padding: 98rpx 20rpx 20rpx;
  }
  
  .section {
    padding: 20rpx;
  }
  
  .content-text {
    font-size: 26rpx;
  }
  
  .tips-item {
    font-size: 24rpx;
  }
}
</style>
