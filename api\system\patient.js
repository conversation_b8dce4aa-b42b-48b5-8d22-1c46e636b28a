import upload from '@/utils/upload'
import request from '@/utils/request'

// 查询病患列表
export function listUpload(query) {
  return request({
    url: '/system/upload/list',
    method: 'get',
    params: query
  })
}

// 查询病患详细
export function getUpload(id) {
  return request({
    url: '/system/upload/' + id,
    method: 'get'
  })
}

// 新增病患
export function addUpload(data) {
  return request({
    url: '/system/upload',
    method: 'post',
    data: data
  })
}

// 修改病患
export function updateUpload(data) {
  return request({
    url: '/system/upload',
    method: 'put',
    data: data
  })
}

// 删除病患
export function delUpload(id) {
  return request({
    url: '/system/upload/' + id,
    method: 'delete'
  })
}
