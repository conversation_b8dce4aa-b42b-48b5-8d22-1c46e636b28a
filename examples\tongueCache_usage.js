/**
 * 舌象科普缓存使用示例
 * 展示如何在 tongue.vue, quality.vue, moss.vue 中使用缓存
 */

import { tongueList } from '@/api/system/tongue'
import TongueCache from '@/utils/tongueCache'

// ==================== tongue.vue 使用示例 ====================
export const tonguePageExample = {
  data() {
    return {
      loading: false,
      tongueData: {
        colors: [],
        shapes: [],
        spirits: [],
        states: []
      },
      mossData: {
        colors: [],
        qualities: []
      }
    }
  },
  
  mounted() {
    this.loadTongueData()
  },
  
  methods: {
    // 加载舌象数据（支持缓存）
    async loadTongueData(forceRefresh = false) {
      try {
        this.loading = true
        uni.showLoading({ title: '加载中...' })
        
        // 1. 优先从缓存加载（除非强制刷新）
        if (!forceRefresh) {
          const cacheResult = TongueCache.loadRawData()
          if (cacheResult.success) {
            const processedData = TongueCache.processTongueData(cacheResult.data, 'all')
            this.tongueData = processedData.tongueData
            this.mossData = processedData.mossData
            console.log('从缓存加载舌象数据成功')
            return
          }
        }
        
        // 2. 缓存无效，请求API
        console.log('缓存无效，请求API获取舌象数据')
        const params = { pageNum: 1, pageSize: 150 }
        const response = await tongueList(params)
        
        if (response && response.rows) {
          // 3. 保存原始数据到缓存
          TongueCache.saveRawData(response.rows)
          
          // 4. 处理数据并更新页面
          const processedData = TongueCache.processTongueData(response.rows, 'all')
          this.tongueData = processedData.tongueData
          this.mossData = processedData.mossData
          
          console.log('API请求成功，数据已缓存')
        }
        
      } catch (error) {
        console.error('加载舌象数据失败:', error)
        uni.showToast({ title: '加载失败', icon: 'none' })
      } finally {
        this.loading = false
        uni.hideLoading()
      }
    },
    
    // 下拉刷新
    async onPullDownRefresh() {
      await this.loadTongueData(true) // 强制刷新
      uni.stopPullDownRefresh()
    }
  }
}

// ==================== quality.vue 使用示例 ====================
export const qualityPageExample = {
  data() {
    return {
      loading: false,
      tongueSpirit: [],
      tongueColors: [],
      tongueShapes: [],
      tongueStates: []
    }
  },
  
  mounted() {
    this.loadTongueData()
  },
  
  methods: {
    // 加载舌质数据（支持缓存）
    async loadTongueData(forceRefresh = false) {
      try {
        this.loading = true
        uni.showLoading({ title: '加载中...' })
        
        // 1. 优先从缓存加载
        if (!forceRefresh) {
          const cacheResult = TongueCache.loadRawData()
          if (cacheResult.success) {
            const processedData = TongueCache.processTongueData(cacheResult.data, 'tongue')
            this.tongueSpirit = processedData.tongueData.spirits
            this.tongueColors = processedData.tongueData.colors
            this.tongueShapes = processedData.tongueData.shapes
            this.tongueStates = processedData.tongueData.states
            console.log('从缓存加载舌质数据成功')
            return
          }
        }
        
        // 2. 缓存无效，请求API
        console.log('缓存无效，请求API获取舌质数据')
        const params = { pageNum: 1, pageSize: 150 }
        const response = await tongueList(params)
        
        if (response && response.rows) {
          // 3. 保存原始数据到缓存
          TongueCache.saveRawData(response.rows)
          
          // 4. 处理舌质数据并更新页面
          const processedData = TongueCache.processTongueData(response.rows, 'tongue')
          this.tongueSpirit = processedData.tongueData.spirits
          this.tongueColors = processedData.tongueData.colors
          this.tongueShapes = processedData.tongueData.shapes
          this.tongueStates = processedData.tongueData.states
          
          console.log('API请求成功，舌质数据已缓存')
        }
        
      } catch (error) {
        console.error('加载舌质数据失败:', error)
        uni.showToast({ title: '加载失败', icon: 'none' })
      } finally {
        this.loading = false
        uni.hideLoading()
      }
    },
    
    // 下拉刷新
    async onPullDownRefresh() {
      await this.loadTongueData(true) // 强制刷新
      uni.stopPullDownRefresh()
    }
  }
}

// ==================== moss.vue 使用示例 ====================
export const mossPageExample = {
  data() {
    return {
      loading: false,
      mossColors: [],
      mossQualities: []
    }
  },
  
  mounted() {
    this.loadTongueData()
  },
  
  methods: {
    // 加载舌苔数据（支持缓存）
    async loadTongueData(forceRefresh = false) {
      try {
        this.loading = true
        uni.showLoading({ title: '加载中...' })
        
        // 1. 优先从缓存加载
        if (!forceRefresh) {
          const cacheResult = TongueCache.loadRawData()
          if (cacheResult.success) {
            const processedData = TongueCache.processTongueData(cacheResult.data, 'moss')
            this.mossColors = processedData.mossData.colors
            this.mossQualities = processedData.mossData.qualities
            console.log('从缓存加载舌苔数据成功')
            return
          }
        }
        
        // 2. 缓存无效，请求API
        console.log('缓存无效，请求API获取舌苔数据')
        const params = { pageNum: 1, pageSize: 150 }
        const response = await tongueList(params)
        
        if (response && response.rows) {
          // 3. 保存原始数据到缓存
          TongueCache.saveRawData(response.rows)
          
          // 4. 处理舌苔数据并更新页面
          const processedData = TongueCache.processTongueData(response.rows, 'moss')
          this.mossColors = processedData.mossData.colors
          this.mossQualities = processedData.mossData.qualities
          
          console.log('API请求成功，舌苔数据已缓存')
        }
        
      } catch (error) {
        console.error('加载舌苔数据失败:', error)
        uni.showToast({ title: '加载失败', icon: 'none' })
      } finally {
        this.loading = false
        uni.hideLoading()
      }
    },
    
    // 下拉刷新
    async onPullDownRefresh() {
      await this.loadTongueData(true) // 强制刷新
      uni.stopPullDownRefresh()
    }
  }
}

// ==================== 缓存管理示例 ====================
export const cacheManagementExample = {
  methods: {
    // 检查缓存状态
    checkCacheStatus() {
      const status = TongueCache.getCacheStatus()
      console.log('缓存状态:', status)
      /*
      输出示例:
      {
        exists: true,
        expired: false,
        dataCount: 150,
        cacheTime: "2024-01-15 10:30:25"
      }
      */
    },
    
    // 清除缓存
    clearCache() {
      TongueCache.clearCache()
      uni.showToast({ title: '缓存已清除', icon: 'success' })
    },
    
    // 强制刷新所有数据
    async forceRefreshAllData() {
      // 清除缓存
      TongueCache.clearCache()
      
      // 重新加载数据
      await this.loadTongueData(true)
      
      uni.showToast({ title: '数据已刷新', icon: 'success' })
    }
  }
}
