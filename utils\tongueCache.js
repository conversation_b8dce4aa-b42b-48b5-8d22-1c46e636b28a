/**
 * 舌象科普数据缓存管理工具
 * 统一管理 tongue.vue, quality.vue, moss.vue 的数据缓存
 */

const CACHE_KEY = 'tongue_science_data'
const CACHE_EXPIRE_TIME = 24 * 60 * 60 * 1000 //24小时过期

class TongueCacheManager {
  
  /**
   * 保存原始API数据到缓存
   * @param {Array} rawData - API返回的原始数据
   */
  saveRawData(rawData) {
    try {
      const cacheData = {
        data: rawData,
        timestamp: Date.now(),
        version: '1.0'
      }
      
      uni.setStorageSync(CACHE_KEY, JSON.stringify(cacheData))
      console.log('舌象科普数据已保存到缓存，数据量:', rawData.length)
      
      return true
    } catch (error) {
      console.error('保存舌象科普数据到缓存失败:', error)
      return false
    }
  }
  
  /**
   * 从缓存加载原始数据
   * @returns {Object|null} - 缓存结果 {success: boolean, data: Array, fromCache: boolean}
   */
  loadRawData() {
    try {
      const cacheStr = uni.getStorageSync(CACHE_KEY)
      if (!cacheStr) {
        console.log('舌象科普缓存为空')
        return { success: false, data: null, fromCache: false }
      }
      
      const cacheData = JSON.parse(cacheStr)
      
      // 检查缓存是否过期
      if (this.isCacheExpired(cacheData.timestamp)) {
        console.log('舌象科普缓存已过期')
        this.clearCache()
        return { success: false, data: null, fromCache: false }
      }
      
      console.log('从缓存加载舌象科普数据，数据量:', cacheData.data.length)
      return { 
        success: true, 
        data: cacheData.data, 
        fromCache: true 
      }
      
    } catch (error) {
      console.error('从缓存加载舌象科普数据失败:', error)
      this.clearCache()
      return { success: false, data: null, fromCache: false }
    }
  }
  
  /**
   * 检查缓存是否过期
   * @param {number} timestamp - 缓存时间戳
   * @returns {boolean}
   */
  isCacheExpired(timestamp) {
    return Date.now() - timestamp > CACHE_EXPIRE_TIME
  }
  
  /**
   * 清除缓存
   */
  clearCache() {
    try {
      uni.removeStorageSync(CACHE_KEY)
      console.log('舌象科普缓存已清除')
    } catch (error) {
      console.error('清除舌象科普缓存失败:', error)
    }
  }
  
  /**
   * 处理舌象数据 - 通用处理逻辑
   * @param {Array} rawData - 原始API数据
   * @param {string} filterType - 过滤类型: 'all', 'tongue', 'moss'
   * @returns {Object} - 处理后的数据结构
   */
  processTongueData(rawData, filterType = 'all') {
    const result = {
      // 舌质数据
      tongueData: {
        spirits: [],
        colors: [],
        shapes: [],
        states: []
      },
      // 舌苔数据
      mossData: {
        colors: [],
        qualities: []
      }
    }
    
    if (!Array.isArray(rawData)) {
      return result
    }
    
    rawData.forEach(item => {
      const category = item.category || ''
      const subCategory = item.subCategory || ''
      
      // 根据filterType过滤数据
      if (filterType === 'tongue' && category !== '舌质' && !category.includes('舌质')) {
        return
      }
      if (filterType === 'moss' && category !== '舌苔' && !category.includes('舌苔')) {
        return
      }
      
      // 统一的数据处理格式
      const processedItem = {
        id: item.id || Math.random().toString(36).substring(2, 11),
        name: item.name || '未知舌象',
        description: item.description || '暂无描述',
        characteristics: item.description || '暂无特征描述',
        clinicalSignificance: item.clinicalMeaning || '暂无临床意义',
        mechanism: item.pathogenesis || '暂无机理说明',
        mainDisease: item.mainDisease || '暂无主病信息',
        subCategory: subCategory,
        category: category,
        referencesinfo: item.referencesinfo || '',
        type: this.getTypeFromCategory(category, subCategory)
      }
      
      // 分类存储
      if (category === '舌质' || category.includes('舌质')) {
        switch (subCategory) {
          case '舌神':
            result.tongueData.spirits.push(processedItem)
            break
          case '舌色':
            result.tongueData.colors.push(processedItem)
            break
          case '舌形':
            result.tongueData.shapes.push(processedItem)
            break
          case '舌态':
            result.tongueData.states.push(processedItem)
            break
        }
      } else if (category === '舌苔' || category.includes('舌苔')) {
        switch (subCategory) {
          case '苔色':
            result.mossData.colors.push(processedItem)
            break
          case '苔质':
            result.mossData.qualities.push(processedItem)
            break
        }
      }
    })
    
    return result
  }
  
  /**
   * 根据category和subCategory获取type
   */
  getTypeFromCategory(category, subCategory) {
    if (category === '舌质' || category.includes('舌质')) {
      switch (subCategory) {
        case '舌神': return 'spirit'
        case '舌色': return 'color'
        case '舌形': return 'shape'
        case '舌态': return 'state'
        default: return 'tongue_unknown'
      }
    } else if (category === '舌苔' || category.includes('舌苔')) {
      switch (subCategory) {
        case '苔色': return 'moss_color'
        case '苔质': return 'moss_quality'
        default: return 'moss_unknown'
      }
    }
    return 'unknown'
  }
  
  /**
   * 获取缓存状态信息
   */
  getCacheStatus() {
    try {
      const cacheStr = uni.getStorageSync(CACHE_KEY)
      if (!cacheStr) {
        return { exists: false, expired: false, dataCount: 0 }
      }
      
      const cacheData = JSON.parse(cacheStr)
      const expired = this.isCacheExpired(cacheData.timestamp)
      
      return {
        exists: true,
        expired: expired,
        dataCount: cacheData.data ? cacheData.data.length : 0,
        cacheTime: new Date(cacheData.timestamp).toLocaleString()
      }
    } catch (error) {
      return { exists: false, expired: true, dataCount: 0, error: error.message }
    }
  }
}

// 导出单例实例
export default new TongueCacheManager()
