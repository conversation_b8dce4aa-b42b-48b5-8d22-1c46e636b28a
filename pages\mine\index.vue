<template>
	<view class="mine-container" :style="{height: `${windowHeight}px`}">
		<!--顶部个人信息栏-->
		<view class="header-section">
			<view class="user-panel">
				<view class="avatar-wrapper">
					<image v-if="avatar" @click="handleToAvatar" :src="avatar" class="cu-avatar xl round"
						mode="widthFix"></image>
					<view v-else class="cu-avatar xl round bg-white">
						<view class="iconfont icon-people text-gray icon"></view>
					</view>
				</view>

				<view class="info-wrapper">
					<view v-if="!name" @click="handleToLogin" class="login-tip">
						点击登录
					</view>
					<view v-else @click="handleToInfo" class="user-info">
						<text class="user-name">{{ name || '未设置昵称' }}</text>
						<text class="edit-profile">个人资料 ></text>
					</view>
				</view>

			</view>
		</view>

		<view class="content-section">
			<view class="mine-actions grid col-4 text-center">
				<view class="action-item" @click="handlejiankangjilu">
					<view class="iconfontA icon-jiankangjilu icon-lg"></view>
					<text class="text">健康记录</text>
				</view>
				<view class="action-item" @click="handleToHealthy">
					<view class="iconfontA icon-wodejiancha icon-lg"></view>
					<text class="text">信息管理</text>
				</view>
					<view class="action-item" @click="handleFeedback">
					<view class="iconfontA icon-yijianfankui icon-lg"></view>
					<text class="text">意见反馈</text>
				</view>
				<view class="action-item" @click="handleCustomerService">
					<view class="iconfontA icon-zaixiankefu icon-lg"></view>
					<text class="text">服务热线</text>
				</view>
			
				
			</view>

			<!-- 商城服务标题 -->
			<!-- <view class="section-title">
				<text class="title-text">商城服务</text>
			</view> -->

			<!-- 购物相关功能模块 -->
			<!-- <view class="shop-actions grid col-4 text-center">
				<view class="action-item" @click="handleToCart">
					<view class="iconfontA icon-gouwuche icon-lg"></view>
					<text class="text">购物车</text>
				</view>
				<view class="action-item" @click="handleToFavorite">
					<view class="iconfontA icon-shoucang icon-lg"></view>
					<text class="text">我的收藏</text>
				</view>
				<view class="action-item" @click="handleToOrders">
					<view class="iconfontA icon-daifahuo icon-lg"></view>
					<text class="text">我的订单</text>
				</view>
				<view class="action-item" @click="handleToAddress">
					<view class="iconfontA icon-shouhuodizhi icon-lg"></view>
					<text class="text">收货地址</text>
				</view>
			</view> -->

			<view class="menu-list">
				<view class="list-cell list-cell-arrow" @click="handleToEditInfo">
					<view class="menu-item-box">
						<view class="iconfont icon-user menu-icon"></view>
						<view>编辑资料</view>
					</view>
				</view>
				<view class="list-cell list-cell-arrow" @click="handleHelp">
					<view class="menu-item-box">
						<view class="iconfont icon-help menu-icon"></view>
						<view>常见问题</view>
					</view>
				</view>
				<view class="list-cell list-cell-arrow" @click="handleAbout">
					<view class="menu-item-box">
						<view class="iconfont icon-aixin menu-icon"></view>
						<view>关于我们</view>
					</view>
				</view>
				<view class="list-cell list-cell-arrow" @click="handleToSetting">
					<view class="menu-item-box">
						<view class="iconfont icon-setting menu-icon"></view>
						<view>应用设置</view>
					</view>
				</view>
			</view>

		</view>

		<!-- 客服弹窗 -->
		<uni-popup ref="servicePopup" type="center">
			<view class="service-popup">
				<!-- 顶部背景装饰 -->
				<view class="popup-header">
					<text class="header-title">服务热线</text>
					<view class="close-btn" @click="closeServicePopup">
						<uni-icons type="closeempty" size="20" color="#fff"></uni-icons>
					</view>
				</view>

				<!-- 服务选项 -->
				<view class="service-options">
					<!-- #ifdef MP-WEIXIN -->
					<!-- <button class="service-btn" open-type="contact" plain="true">
						<view class="btn-icon">
							<uni-icons type="weixin" size="28" color="#3ec6c6"></uni-icons>
						</view>
						<text class="btn-title">在线客服</text>
						<uni-icons type="right" size="16" color="#999"></uni-icons>
					</button> -->
					<!-- #endif -->
					<!-- #ifdef APP-PLUS -->
					<!-- <button class="service-btn" @click="handleWeixinCustomerService" plain="true">
						<view class="btn-icon">
							<uni-icons type="weixin" size="28" color="#3ec6c6"></uni-icons>
						</view>
						<text class="btn-title">在线客服</text>
						<uni-icons type="right" size="16" color="#999"></uni-icons>
					</button> -->
					<!-- #endif -->
					<button class="service-btn" @click="handleHotlineService" plain="true">
						<view class="btn-icon">
							<uni-icons type="phone" size="28" color="#3ec6c6"></uni-icons>
						</view>
						<text class="btn-title">服务热线 0546-8080588</text>
						<uni-icons type="right" size="16" color="#999"></uni-icons>
					</button>
				</view>

				<!-- 底部服务时间 -->
				<view class="service-footer">
					<uni-icons type="calendar" size="14" color="#999"></uni-icons>
					<text class="footer-text">服务时间：周一至周五 10:00-20:00</text>
				</view>
			</view>
		</uni-popup>

		<!-- 分享弹框 -->
		<share-popup ref="sharePopup"></share-popup>

	</view>
</template>

<script>
	import uniPopup from '@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue' // 引入 uni-popup
	import uniIcons from '@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue' // 引入 uni-icons
	import SharePopup from '@/components/pop/share-popup.vue'

	export default {
		components: { // 注册组件
			uniPopup,
			uniIcons,
			SharePopup
		},
		data() {
			return {
				version: getApp().globalData.config.appInfo.version
			}
		},

		computed: {
			name() {
				return this.$store.state.user.name
			},
			avatar() {
				return this.$store.state.user.avatar
			},
			windowHeight() {
				// 调整 windowHeight 计算以适应底部按钮
				return uni.getSystemInfoSync().windowHeight;
			}
		},
		onShow() {
			console.log('Mine page onShow:');
			console.log('Store user name:', this.$store.state.user.name);
			console.log('Store user avatar:', this.$store.state.user.avatar);

			// 如果用户已登录，刷新用户信息以确保显示最新的昵称
			if (this.name) {
				this.$store.dispatch('GetInfo').then(() => {
					console.log('用户信息已刷新');
				}).catch(error => {
					console.warn('刷新用户信息失败:', error);
				});
			}
		},
		methods: {
			handleToInfo() {
				this.$tab.navigateTo('/pages/my/info/index')
			},
			handleToEditInfo() {
				this.$tab.navigateTo('/pages/my/info/edit')
			},
			handleToSetting() {
				this.$tab.navigateTo('/pages/my/setting/index')
			},
			handleToLogin() {
				// 这个方法现在可以移除或调整，因为onShow会处理未登录情况
				this.$tab.reLaunch('/pages/login')
			},
			handleToAvatar() {
				// this.$tab.navigateTo('/pages/my/avatar/index')
				
			},
			handleLogout() {
				this.$modal.confirm('确定注销并退出系统吗？').then(() => {
					this.$store.dispatch('LogOut').then(() => {
						// 清理更新相关缓存
						try {
							const { clearUpdateCache } = require('@/utils/upversion');
							clearUpdateCache();
						} catch(e) {
							console.log('清理更新缓存失败:', e);
						}
						// 移除重定向到首页的代码，留在当前页面
					})
				})
			},
			handleHelp() {
				this.$tab.navigateTo('/pages/my/help/index')
			},
			handleAbout() {
				this.$tab.navigateTo('/pages/my/about/index')
			},
			// 新增方法来处理登录拦截
			checkAndNavigateToLogin() {
				const {
					checkLogin
				} = require('@/plugins/auth');
				if (!checkLogin()) {
					uni.showModal({
						title: '提示',
						content: '您还未登录，请先登录。',
						confirmText: '去登录',
						cancelText: '取消',
						success: (res) => {
							if (res.confirm) {
								uni.redirectTo({
									url: '/pages/login'
								});
							}
						}
					});
					return true; // 表示已拦截并跳转
				}
				return false; // 表示未拦截，可以继续执行后续逻辑
			},
			handlejiankangjilu() {
				if (this.checkAndNavigateToLogin()) return;
				this.$tab.navigateTo('/pages/my/health/health')

			},
			handleCustomerService() {
				if (this.checkAndNavigateToLogin()) return;
				this.$refs.servicePopup.open();
			},
			
			// 处理服务热线按钮点击
			handleHotlineService() {
				if (this.checkAndNavigateToLogin()) return;
				uni.makePhoneCall({
					phoneNumber: '0546-8080588', // 客服电话
					success: function() {
						console.log('拨打电话成功！');
					},
					fail: function() {
						console.log('拨打电话失败！');
						uni.showToast({
							title: '拨打电话失败，请检查权限设置',
							icon: 'none'
						});
					}
				});
				this.closeServicePopup();
			},
			// 关闭客服弹窗
			closeServicePopup() {
				this.$refs.servicePopup.close();
			},
			// 处理微信客服
			handleWeixinCustomerService() {
				if (this.checkAndNavigateToLogin()) return;
				// #ifdef MP-WEIXIN
				// 微信小程序使用原有的 open-type="contact" 方式
				// 这部分在模板中已经处理
				// #endif

				// #ifdef APP-PLUS
				// 方案1：尝试打开企业微信客服
				this.openEnterpriseWeixinService();
				// #endif

				this.closeServicePopup();
			},

			// 打开企业微信客服
			openEnterpriseWeixinService() {
				let sweixin = null;
				plus.share.getServices(res => {
					sweixin = res.find(i => i.id === 'weixin');
					if (sweixin) {
						// 检查微信是否已安装
						sweixin.authorize(authRes => {
							console.log("微信授权成功", authRes);
							// 尝试打开客服聊天
							this.tryOpenCustomerService(sweixin);
						}, authErr => {
							console.log("微信授权失败", authErr);
							this.fallbackToAlternativeService();
						});
					} else {
						console.log('微信服务不可用');
						this.fallbackToAlternativeService();
					}
				}, err => {
					console.log("获取分享服务失败", err);
					this.fallbackToAlternativeService();
				});
			},

			// 尝试打开客服服务
			tryOpenCustomerService(sweixin) {
				// 使用更标准的参数格式
				const customerServiceConfig = {
					corpid: 'ww4d46b76e0b192654',
					url: 'https://work.weixin.qq.com/kfid/kfc4f8ec160075e8a86'
				};

				console.log("尝试打开客服，配置：", customerServiceConfig);

				sweixin.openCustomerServiceChat(
					customerServiceConfig,
					suc => {
						console.log("客服打开成功", suc);
					},
					err => {
						console.log("客服打开失败", err);
						// 如果企业微信客服失败，尝试其他方式
						this.fallbackToAlternativeService();
					}
				);
			},

			// 备用客服方案
			fallbackToAlternativeService() {
				uni.showModal({
					title: '客服联系方式',
					content: '微信客服暂时无法使用\n\n请选择其他联系方式：\n• 客服热线：0546-8080588\n• 或直接拨打电话联系',
					confirmText: '拨打电话',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							this.handleHotlineService();
						}
					}
				});
			},
			handleFeedback() {
				if (this.checkAndNavigateToLogin()) return;
				this.$tab.navigateTo('/pages/my/feedback/index');
			},
			handleShare() {
				if (this.checkAndNavigateToLogin && this.checkAndNavigateToLogin()) return;
				this.$refs.sharePopup.open();
			},
			handleToFavorite() {
				if (this.checkAndNavigateToLogin()) return;
				this.$tab.navigateTo('/pages/shopping/favorite/favorite')
			},
			handleToOrders() {
				if (this.checkAndNavigateToLogin()) return;
				this.$tab.navigateTo('/pages/shopping/order/order')
			},
			handleToAddress() {
				if (this.checkAndNavigateToLogin()) return;
				this.$tab.navigateTo('/pages/shopping/address/address')
			},
			handleToCart() {
				if (this.checkAndNavigateToLogin()) return;
				this.$tab.navigateTo('/pages/shopping/cart/cart')
			},
			// 跳转到用户信息录入页面
			handleToHealthy() {
				if (this.checkAndNavigateToLogin()) return;
				this.$tab.navigateTo('/pages/my/healthInput/analyse')
			}

		}
	}
</script>

<style lang="scss">
	page {
		background-color: #ffffff;
		/* Lighter background color */
	}

	.mine-container {
		width: 100%;
		min-height: 100vh;
		display: flex;
		flex-direction: column;
		background-color: #f8f8f8;

		.header-section {
			position: relative;
			padding: 40rpx 30rpx;
			background: linear-gradient(180deg, #3ec6c6 0%, #3ec6c6 60%, #f8f8f8 100%);
			border-radius: 0 0 30rpx 30rpx;
			color: white;
			overflow: hidden;

			.user-panel {
				display: flex;
				align-items: center;
				justify-content: flex-start;
				width: 100%;
			}

			.avatar-wrapper {
				position: relative;
				margin-right: 30rpx;

				.cu-avatar {
					width: 120rpx;
					height: 120rpx;
					border: 4rpx solid rgba(255, 255, 255, 0.8);
					box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
				}

				.icon {
					font-size: 60rpx;
				}
			}

			.info-wrapper {
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: center;

				.login-tip {
					color: #ffffff;
					font-size: 32rpx;
					font-weight: 500;
				}

				.user-info {
					display: flex;
					align-items: center;
					justify-content: space-between;
					cursor: pointer;
					width: 100%;

					.user-name {
						display: block;
						color: #ffffff;
						font-size: 36rpx;
						font-weight: bold;
						margin-bottom: 10rpx;
					}

					.edit-profile {
						color: rgba(255, 255, 255, 0.9);
						font-size: 24rpx;
					}
				}
			}
		}

		.content-section {
			position: relative;
			top: -25rpx;
			margin: 0 20rpx;
			flex: 1;

			.mine-actions {
				background: #ffffff;
				border-radius: 16rpx;
				padding: 18rpx 18rpx;
				margin-bottom: 20rpx;
				box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);

				.action-item {
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					padding: 20rpx 0;
					transition: transform 0.2s ease-in-out;

					&:active {
						transform: translateY(2px);
						/* 点击时下沉 */
					}

					.icon-lg {
						font-size: 56rpx;
						color: #3ec6c6;
						margin-bottom: 16rpx;
					}

					.text {
						font-size: 24rpx;
						color: #333333;
					}
				}
			}

			.section-title {
				padding: 30rpx 10rpx 20rpx;

				.title-text {
					font-size: 32rpx;
					color: #333;
					font-weight: 600;
					position: relative;
					padding-left: 24rpx;

					&::before {
						content: '';
						position: absolute;
						left: 0;
						top: 50%;
						transform: translateY(-50%);
						width: 8rpx;
						height: 32rpx;
						background: #3ec6c6;
						border-radius: 4rpx;
					}
				}
			}

			.shop-actions {
				background: #ffffff;
				border-radius: 16rpx;
				padding: 18rpx 18rpx;
				margin-bottom: 20rpx;
				box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);

				.action-item {
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					padding: 20rpx 0;
					transition: transform 0.2s ease-in-out;

					&:active {
						transform: translateY(2px);
						/* 点击时下沉 */
					}

					.icon-lg {
						font-size: 56rpx;
						color: #3ec6c6;
						margin-bottom: 16rpx;
					}

					.text {
						font-size: 24rpx;
						color: #333333;
					}
				}
			}

			.menu-list {
				background: #ffffff;
				border-radius: 16rpx;
				overflow: hidden;
				box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
				margin: 0 20rpx;

				.list-cell {
					position: relative;
					padding: 28rpx 40rpx;

					&:not(:last-child)::after {
						content: '';
						position: absolute;
						left: 40rpx;
						right: 40rpx;
						bottom: 0;
						height: 1rpx;
						background-color: #f5f5f5;
					}

					.menu-item-box {
						display: flex;
						align-items: center;
						flex: 1;
						font-size: 28rpx;
						color: #333333;
					}

					.menu-icon {
						font-size: 40rpx;
						color: #3ec6c6;
						margin-right: 20rpx;
					}
				}

				&.list-cell-arrow::after {
					content: '';
					position: absolute;
					right: 40rpx;
					top: 50%;
					width: 12rpx;
					height: 12rpx;
					border-top: 2rpx solid #999;
					border-right: 2rpx solid #999;
					transform: translateY(-50%) rotate(45deg);
				}
			}
		}

		.fixed-bottom {
			padding: 10rpx;
			padding-bottom: constant(safe-area-inset-bottom);
			padding-bottom: env(safe-area-inset-bottom);
			background-color: transparent;
		}
	}

	/* 客服弹窗样式 */
	.service-popup {
		width: 600rpx;
		background: #fff;
		border-radius: 24rpx;
		overflow: hidden;
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);

		.popup-header {
			position: relative;
			height: 100rpx;
			background: linear-gradient(135deg, #3ec6c6 0%, #2bb3b3 100%);
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: center;
			padding: 20rpx;

			.header-icon {
				width: 48rpx;
				height: 48rpx;
				margin-right: 12rpx;
				margin-bottom: 0;
			}

			.header-title {
				font-size: 32rpx;
				color: #fff;
				font-weight: 500;
			}

			.close-btn {
				position: absolute;
				top: 50%;
				transform: translateY(-50%);
				right: 20rpx;
				padding: 10rpx;
			}
		}

		.service-options {
			padding: 30rpx;

			.service-btn {
				width: 100%;
				display: flex;
				align-items: center;
				background: #fff;
				border-radius: 18rpx;
				box-shadow: 0 2px 8px rgba(62,198,198,0.06);
				margin-bottom: 24rpx;
				padding: 18rpx 20rpx;
				border: none;
				outline: none;
				transition: box-shadow 0.2s, background 0.2s;
			}

			.service-btn:active {
				background: #e6f7fa;
			}

			.btn-icon {
				width: 64rpx;
				height: 64rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 50%;
				background: linear-gradient(135deg, #e6f7fa 60%, #c6f0f7 100%);
				box-shadow: 0 2px 8px rgba(62,198,198,0.10);
				margin-right: 12rpx;
			}

			.btn-title {
				flex: 1;
				font-size: 28rpx;
				font-weight: 700;
				color: #1a3c4e;
				text-align: left;
			}
		}

		.service-footer {
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 30rpx;
			border-top: 2rpx solid #f5f5f5;

			.footer-text {
				font-size: 24rpx;
				color: #999;
				margin-left: 8rpx;
			}
		}
	}
</style>