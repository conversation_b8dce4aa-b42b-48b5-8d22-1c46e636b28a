import request from '@/utils/request'
import config from '@/config'
import { createSSEConnection } from '@/utils/sse-helper';
import { getToken } from '@/utils/auth'

// 查询参数列表
export function sendMsgToAi(query) {
  return request({
    url: '/Models/aitext',
    method: 'get',
    params: query
  })
}
const baseUrl = config.baseUrl;
// 流式
// api/system/ai.js
export function sendMsgToAiWS(query, callback) {

		console.log(baseUrl.replace("http","wss"));
		// const wsUrl = `${baseUrl.replace("http","wss")}/ws/realtime-ai?text=${encodeURIComponent(query.text)}&model=${encodeURIComponent('qwen:1.8b')}`;
		const wsUrl = `${baseUrl.replace("http","ws")}/ws/realtime-ai`;
		console.log(wsUrl,{
			// 'Accept': 'text/event-stream',
			// 'content-type': 'application/json',
			'Authorization': 'Bearer ' + getToken()
		  });
		// let websocket = new WebSocket(wsUrl);
		//创建实例
		const socketTask = uni.connectSocket({
		  url: wsUrl,
		  // method: 'GET',
		  header: {
			// 'Accept': 'text/event-stream',
			// 'content-type': 'application/json',
			'Authorization': 'Bearer ' + getToken()
		  },
		  success: res => {
			console.log("实例创建成功",res);
		  },
		  fail: err => {
			  console.log("实例创建失败",err);
		  }
		});
		console.log(socketTask);
		
		// WebSocket 连接建立时触发
		socketTask.onOpen(res => {
			socketTask.send({
				data: JSON.stringify({
					text: query.text, 
					model: 'qwen:1.8b'
				}),
				success: () => console.log('消息发送成功')
			});
			console.log('连接建立成功',res);
		})

		// WebSocket 接收到消息时触发
		socketTask.onMessage(event => {
			console.log('收到消息',event);
			try {
				const chunk = event.data;
				if(chunk === '[DONE]'){
					socketTask.close();
				}else{
					callback(chunk, null);
				}
			} catch (err) {
				callback(null, { error: err.message });
			}
		})

		// WebSocket 发生错误时触发
		socketTask.onError(err => {
			console.log("连接出错",err);
			callback(null, { error: err.message });
			socketTask.close();
		})

		// WebSocket 关闭时触发
		socketTask.onClose( () => {
			console.log('连接关闭');
			callback(null, { done: true });
		})

	
	return socketTask; // 返回 WebSocket 实例
}
