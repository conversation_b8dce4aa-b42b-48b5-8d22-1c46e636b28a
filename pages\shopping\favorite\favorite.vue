<template>
  <view class="favorite-container">
    <!-- 头部导航 -->
    <view class="header" v-if="favoriteList.length > 0">
      <view class="header-content">
        <view class="title-section">
          <text class="subtitle">共{{ favoriteList.length }}件商品</text>
        </view>
        <view class="edit-btn" @click="toggleEditMode">
          <text>{{ isEditMode ? '完成' : '编辑' }}</text>
        </view>
      </view>
    </view>

    <!-- 收藏列表 -->
    <view class="content">
      <view v-if="favoriteList.length === 0" class="empty-state">
        <view class="empty-illustration">
          <view class="empty-icon">
            <uni-icons type="star" size="80" color="#e0e0e0"></uni-icons>
          </view>
          <view class="empty-bg-circle"></view>
          <view class="empty-bg-circle-2"></view>
        </view>
        <text class="empty-title">还没有收藏商品</text>
        <text class="empty-desc">收藏喜欢的商品，随时查看</text>
        <view class="go-shopping-btn" @click="goShopping">
          <uni-icons type="shop" size="16" color="#fff" style="margin-right: 8rpx;"></uni-icons>
          <text>去逛逛</text>
        </view>
      </view>

      <view v-else class="favorite-list">
        <view
          class="favorite-item"
          v-for="item in favoriteList"
          :key="item.id"
          @click="!isEditMode && goToDetail(item.id)"
        >
          <!-- 选择框 -->
          <view class="checkbox" v-if="isEditMode" @click.stop="toggleSelect(item.id)">
            <view class="checkbox-inner" :class="{ checked: selectedItems.includes(item.id) }">
              <text class="checkmark" v-if="selectedItems.includes(item.id)">✓</text>
            </view>
          </view>

          <!-- 商品图片 -->
          <image :src="item.image" mode="aspectFill" class="product-image" />

          <!-- 商品信息 -->
          <view class="product-info">
            <view class="product-name">{{ item.name }}</view>
            <view class="product-tags">
              <text class="tag" v-if="item.isPromotion">限时优惠</text>
              <text class="tag" v-if="item.freeShipping">包邮</text>
            </view>
            <view class="price-section">
              <view class="current-price">¥{{ item.price }}</view>
              <view class="original-price" v-if="item.originalPrice">¥{{ item.originalPrice }}</view>
            </view>
            <view class="sales-info">
              <text>已售{{ item.sales }}件</text>
              <text>库存{{ item.stock }}件</text>
            </view>
          </view>

          <!-- 操作按钮 -->
          <view class="action-btns" v-if="!isEditMode">
            <view class="action-btn remove-btn" @click.stop="removeFavorite(item.id)">
              <uni-icons type="star-filled" size="20" color="#ff5555"></uni-icons>
            </view>
            <view class="action-btn cart-btn" @click.stop="addToCart(item)">
              <uni-icons type="cart" size="20" color="#fff"></uni-icons>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-bar" v-if="isEditMode && favoriteList.length > 0">
      <view class="select-all" @click="toggleSelectAll">
        <view class="checkbox-inner" :class="{ checked: isAllSelected }">
          <text class="checkmark" v-if="isAllSelected">✓</text>
        </view>
        <text>全选</text>
      </view>
      <view class="selected-count">
        <text>已选择{{ selectedItems.length }}件</text>
      </view>
      <view class="action-buttons">
        <view class="action-btn delete-btn" @click="batchDelete" :class="{ disabled: selectedItems.length === 0 }">
          删除
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      favoriteList: [],
      isEditMode: false,
      selectedItems: []
    }
  },
  computed: {
    isAllSelected() {
      return this.favoriteList.length > 0 && this.selectedItems.length === this.favoriteList.length;
    }
  },
  onShow() {
    this.loadFavoriteList();
  },
  methods: {
    // 加载收藏列表
    loadFavoriteList() {
      const favoriteItems = uni.getStorageSync('favoriteItems') || [];
      // 按添加时间倒序排列
      this.favoriteList = favoriteItems.sort((a, b) => b.addTime - a.addTime);
    },

    // 切换编辑模式
    toggleEditMode() {
      this.isEditMode = !this.isEditMode;
      if (!this.isEditMode) {
        this.selectedItems = [];
      }
    },

    // 切换选择状态
    toggleSelect(id) {
      const index = this.selectedItems.indexOf(id);
      if (index > -1) {
        this.selectedItems.splice(index, 1);
      } else {
        this.selectedItems.push(id);
      }
    },

    // 全选/取消全选
    toggleSelectAll() {
      if (this.isAllSelected) {
        this.selectedItems = [];
      } else {
        this.selectedItems = this.favoriteList.map(item => item.id);
      }
    },

    // 移除单个收藏
    removeFavorite(id) {
      uni.showModal({
        title: '提示',
        content: '确定要取消收藏这个商品吗？',
        success: (res) => {
          if (res.confirm) {
            const favoriteItems = uni.getStorageSync('favoriteItems') || [];
            const newFavoriteItems = favoriteItems.filter(item => item.id !== id);
            uni.setStorageSync('favoriteItems', newFavoriteItems);
            this.loadFavoriteList();
            uni.showToast({
              title: '已取消收藏',
              icon: 'success'
            });
          }
        }
      });
    },

    // 批量删除
    batchDelete() {
      if (this.selectedItems.length === 0) {
        return;
      }

      uni.showModal({
        title: '提示',
        content: `确定要删除选中的${this.selectedItems.length}个商品吗？`,
        success: (res) => {
          if (res.confirm) {
            const favoriteItems = uni.getStorageSync('favoriteItems') || [];
            const newFavoriteItems = favoriteItems.filter(item => !this.selectedItems.includes(item.id));
            uni.setStorageSync('favoriteItems', newFavoriteItems);
            this.loadFavoriteList();
            this.selectedItems = [];
            this.isEditMode = false;
            uni.showToast({
              title: '删除成功',
              icon: 'success'
            });
          }
        }
      });
    },

    // 添加到购物车
    addToCart(product) {
      const cartItems = uni.getStorageSync('cartItems') || [];

      // 查找是否已存在该商品
      const existItemIndex = cartItems.findIndex(item => item.id === product.id);

      if (existItemIndex > -1) {
        // 如果商品已存在，检查库存是否足够
        const currentQuantity = cartItems[existItemIndex].quantity;
        if (currentQuantity >= product.stock) {
          uni.showToast({
            title: '已达到商品库存上限',
            icon: 'none'
          });
          return;
        }
        // 增加数量
        cartItems[existItemIndex].quantity += 1;
        uni.showToast({
          title: '已更新购物车数量',
          icon: 'success'
        });
      } else {
        // 如果商品不存在，添加新商品
        cartItems.push({
          ...product,
          quantity: 1,
          selected: true,
          specs: {} // 默认规格
        });
        uni.showToast({
          title: '已添加到购物车',
          icon: 'success'
        });
      }

      // 更新购物车状态
      uni.setStorageSync('cartItems', cartItems);
    },

    // 跳转到商品详情
    goToDetail(id) {
      console.log('跳转到商品详情，商品ID:', id);
      uni.navigateTo({
        url: `/pages/shopping/detail/detail?id=${id}`
      });
    },

    // 去购物
    goShopping() {
      uni.switchTab({
        url: '/pages/shop/index'
      });
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/static/fontA/iconfont.css';

.favorite-container {
  min-height: 100vh;
  padding-bottom: 120rpx;

  /* 有收藏商品时的背景 */
  &.has-favorites {
    background: linear-gradient(135deg, #f8f9fa 0%, #f1f3f4 100%);
  }

  /* 空状态时的背景 */
  &.empty-favorites {
    background: #f5f5f5;
  }
}

// 头部
.header {
  background: linear-gradient(135deg, #fff 0%, #fafbfc 100%);
  padding: 30rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  min-height: 80rpx; /* 确保头部有最小高度 */

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 20rpx;
    min-height: 40rpx; /* 确保内容区域有最小高度 */

    .title-section {
      display: flex;
      flex-direction: column;

      .subtitle {
        font-size: 26rpx;
        color: #8a8a8a;
        white-space: nowrap;
        margin-top:10rpx
      }
    }

    .edit-btn {
      padding: 10rpx 30rpx;
      background: linear-gradient(135deg, #3ec6c6 0%, #36b3b3 100%);
      border-radius: 24rpx;
      box-shadow: 0 4rpx 12rpx rgba(62, 198, 198, 0.3);
      flex-shrink: 0;
      height: fit-content;
      // margin-top: 4rpx;

      text {
        font-size: 26rpx;
        color: #fff;
        font-weight: 500;
        white-space: nowrap;
      }
    }
  }
}

// 内容区域
.content {
  flex: 1;
  padding: 20rpx;
}

// 空状态
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 180rpx 0 40rpx 40rpx;
  text-align: center;
  // min-height: 80vh; /* 占据整个屏幕高度 */

  .empty-illustration {
    position: relative;
    margin-bottom: 60rpx;

    .empty-icon {
      width: 160rpx;
      height: 160rpx;
      background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
      position: relative;
      z-index: 2;
    }

    .empty-bg-circle {
      position: absolute;
      top: -20rpx;
      left: -20rpx;
      width: 200rpx;
      height: 200rpx;
      background: linear-gradient(135deg, rgba(62, 198, 198, 0.1) 0%, rgba(62, 198, 198, 0.05) 100%);
      border-radius: 50%;
      z-index: 1;
    }

    .empty-bg-circle-2 {
      position: absolute;
      top: -40rpx;
      left: -40rpx;
      width: 240rpx;
      height: 240rpx;
      background: linear-gradient(135deg, rgba(62, 198, 198, 0.05) 0%, rgba(62, 198, 198, 0.02) 100%);
      border-radius: 50%;
      z-index: 0;
    }
  }

  .empty-title {
    font-size: 32rpx;
    color: #333;
    font-weight: 600;
    margin-bottom: 16rpx;
  }

  .empty-desc {
    font-size: 26rpx;
    color: #8a8a8a;
    margin-bottom: 70rpx;
    line-height: 1.5;
  }

  .go-shopping-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 24rpx 48rpx;
    background: linear-gradient(135deg, #3ec6c6 0%, #36b3b3 100%);
    color: #fff;
    border-radius: 48rpx;
    font-size: 28rpx;
    font-weight: 500;
    box-shadow: 0 8rpx 24rpx rgba(62, 198, 198, 0.3);
    transition: all 0.3s ease;

    &:active {
      transform: translateY(2rpx);
      box-shadow: 0 4rpx 12rpx rgba(62, 198, 198, 0.3);
    }
  }
}

// 收藏列表
.favorite-list {
  .favorite-item {
    background: linear-gradient(135deg, #fff 0%, #fafbfc 100%);
    border-radius: 20rpx;
    margin-bottom: 24rpx;
    padding: 24rpx;
    display: flex;
    align-items: center;
    position: relative;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
    border: 1rpx solid rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;

    &:active {
      transform: translateY(2rpx);
      box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
    }



    .checkbox {
      margin-right: 20rpx;

      .checkbox-inner {
        width: 40rpx;
        height: 40rpx;
        border: 2rpx solid #ddd;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        &.checked {
          background: #3ec6c6;
          border-color: #3ec6c6;
        }

        .checkmark {
          color: #fff;
          font-size: 24rpx;
          font-weight: bold;
        }
      }
    }

    .product-image {
      width: 200rpx;
      height: 200rpx;
      border-radius: 12rpx;
      margin-right: 20rpx;
    }

    .product-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 200rpx;

      .product-name {
        font-size: 28rpx;
        color: #333;
        line-height: 1.4;
        margin-bottom: 10rpx;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        overflow: hidden;
      }

      .product-tags {
        margin-bottom: 10rpx;

        .tag {
          display: inline-block;
          padding: 4rpx 12rpx;
          background: rgba(62, 198, 198, 0.1);
          color: #3ec6c6;
          font-size: 22rpx;
          border-radius: 8rpx;
          margin-right: 12rpx;
        }
      }

      .price-section {
        display: flex;
        align-items: baseline;
        margin-bottom: 10rpx;

        .current-price {
          font-size: 32rpx;
          color: #ff5555;
          font-weight: bold;
          margin-right: 12rpx;
        }

        .original-price {
          font-size: 24rpx;
          color: #999;
          text-decoration: line-through;
        }
      }

      .sales-info {
        font-size: 24rpx;
        color: #999;

        text:first-child {
          margin-right: 20rpx;
        }
      }
    }

    .action-btns {
      display: flex;
      flex-direction: column;
      gap: 20rpx;
      position: relative;
      z-index: 2;

      .action-btn {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        &.remove-btn {
          background: #f5f5f5;

          .iconfontA {
            font-size: 24rpx;
            color: #ff5555;
          }
        }

        &.cart-btn {
          background: #3ec6c6;
        }
      }
    }
  }
}

// 底部操作栏
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background: #fff;
  border-top: 1rpx solid #eee;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  z-index: 999;

  .select-all {
    display: flex;
    align-items: center;
    margin-right: 40rpx;

    .checkbox-inner {
      width: 40rpx;
      height: 40rpx;
      border: 2rpx solid #ddd;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16rpx;

      &.checked {
        background: #3ec6c6;
        border-color: #3ec6c6;
      }

      .checkmark {
        color: #fff;
        font-size: 24rpx;
        font-weight: bold;
      }
    }

    text {
      font-size: 28rpx;
      color: #333;
    }
  }

  .selected-count {
    flex: 1;

    text {
      font-size: 26rpx;
      color: #666;
    }
  }

  .action-buttons {
    .action-btn {
      padding: 20rpx 40rpx;
      border-radius: 40rpx;
      font-size: 28rpx;

      &.delete-btn {
        background: #ff5555;
        color: #fff;

        &.disabled {
          background: #ccc;
          color: #999;
        }
      }
    }
  }
}
</style>
