const TokenKey = 'App-Token'

export function getToken() {
  try {
    const token = uni.getStorageSync(TokenKey)
    console.log('getToken - Retrieved token:', token ? 'Token exists' : 'No token found')
    return token
  } catch (error) {
    console.error('getToken - Error retrieving token:', error)
    return null
  }
}

export function setToken(token) {
  try {
    console.log('setToken - Setting token:', token ? 'Token provided' : 'No token provided')
    return uni.setStorageSync(TokenKey, token)
  } catch (error) {
    console.error('setToken - Error setting token:', error)
    return false
  }
}

export function removeToken() {
  try {
    console.log('removeToken - Removing token')
    return uni.removeStorageSync(TokenKey)
  } catch (error) {
    console.error('removeToken - Error removing token:', error)
    return false
  }
}

/**
 * 关于username缓存数据的获取、设置、删除的方法
 */
const UserName = 'Kat-Gather-UserName'
export function getUserName() {
  return uni.getStorageSync(UserName)
}

export function setUserName(username) {
  return uni.setStorageSync(UserName, username)
}

export function removeUserName() {
  return uni.removeStorageSync(UserName)
} 

/**
 * 关于password缓存数据的获取、设置、删除的方法
 */
const PassWord = 'Kat-Gather-PassWord'
export function getPassWord() {
  return uni.getStorageSync(PassWord)
}

export function setPassWord(password) {
  return uni.setStorageSync(PassWord, password)
}


export function removePassWord() {
  return uni.removeStorageSync(PassWord)
}

// 全局token过期处理锁，防止重复弹窗
let isTokenExpiredHandling = false

/**
 * 处理token过期的通用方法
 * 显示确认弹窗，用户点击确认后跳转到登录页面
 * 使用全局锁机制防止重复弹窗
 * 清除缓存但保留用户名和密码
 */
export function handleTokenExpired() {
  // 如果已经在处理token过期，直接返回，避免重复弹窗
  if (isTokenExpiredHandling) {
    console.log('Token过期处理已在进行中，跳过重复处理')
    return
  }

  // 设置处理标志
  isTokenExpiredHandling = true
  console.log('开始处理Token过期，设置全局锁')

  uni.showModal({
    title: '登录状态已过期',
    content: '当前登录状态已过期，请重新登录',
    confirmText: '确认',
    showCancel: false, // 不显示取消按钮，只有确认选项
    success: (res) => {
      if (res.confirm) {
        // 用户点击确认后，清除缓存但保留用户名和密码
        clearCacheKeepCredentials()

        // 跳转到登录页面后重置处理标志
        uni.reLaunch({
          url: '/pages/login',
          success: () => {
            console.log('已跳转到登录页面，重置Token过期处理锁')
            isTokenExpiredHandling = false
          },
          fail: () => {
            console.log('跳转登录页面失败，重置Token过期处理锁')
            isTokenExpiredHandling = false
          }
        })
      }
    },
    fail: () => {
      // 弹窗显示失败时也要重置标志
      console.log('Token过期弹窗显示失败，重置处理锁')
      isTokenExpiredHandling = false
    }
  })
}

/**
 * 清除缓存但保留用户名和密码
 * 在token过期时调用，清除用户相关数据但保留登录凭据
 */
export function clearCacheKeepCredentials() {
  try {
    console.log('开始清除缓存但保留用户名和密码')
    let cleanedCount = 0

    // 获取所有存储键
    const allKeys = uni.getStorageInfoSync().keys || []

    // 需要保留的关键数据（用户名和密码）
    const keysToKeep = [
      'Kat-Gather-UserName',  // 用户名
      'Kat-Gather-PassWord',  // 密码
      'Kat-Gather-Rule' ,  // 用户协议状态
      'storage_data'       // Vuex store数据

    ]

    // 需要明确清理的用户相关数据
    const userDataKeysToClean = [
      'App-Token',            // token
      'userInfo',             // 用户基本信息
      'User-Informations',    // 用户健康信息
      'lastLoginTime',        // 最后登录时间
      'lastUserInfoSyncTime', // 用户信息同步时间
      'lastUserInfoUpdateTime' // 用户信息更新时间
    ]

    // 检测相关数据键（以detection_开头的键）
    const detectionDataKeysToClean = [
      'detection_user_info',
      'detection_tongue_data',
      'detection_constitution_data',
      'detection_state',
      'capturedImage',
      'tongueAnalysisResult',
      'tongueDetectionResult'
    ]

    // 舌象科普缓存数据
    const tongueDataKeysToClean = [
      'tongue_science_data'
    ]

    // 其他业务数据
    const businessDataKeysToClean = [
      'cartItems',        // 购物车
      'orders',           // 订单
      'favoriteItems',    // 收藏
      'searchHistory',    // 搜索历史
      'addresses',        // 收货地址
      'update_pending',   // 更新状态
      'version_check_cache' // 版本检查缓存
    ]

    // 合并所有需要清理的数据键
    const allKeysToClean = [
      ...userDataKeysToClean,
      ...detectionDataKeysToClean,
      ...tongueDataKeysToClean,
      ...businessDataKeysToClean
    ]

    // 清理指定的数据键
    allKeysToClean.forEach(key => {
      if (uni.getStorageSync(key)) {
        uni.removeStorageSync(key)
        cleanedCount++
        console.log(`已清理: ${key}`)
      }
    })

    // 清理其他未明确指定但不在保留列表中的数据
    allKeys.forEach(key => {
      if (!keysToKeep.includes(key) && !allKeysToClean.includes(key)) {
        try {
          uni.removeStorageSync(key)
          cleanedCount++
          console.log(`已清理其他数据: ${key}`)
        } catch (error) {
          console.warn(`清理 ${key} 失败:`, error)
        }
      }
    })

    console.log(`Token过期缓存清理完成，用户名和密码已保留，共清理 ${cleanedCount} 项数据`)
    return cleanedCount
  } catch (error) {
    console.error('清除缓存时发生错误:', error)
    return 0
  }
}

/**
 * 重置token过期处理状态（用于调试或特殊情况）
 */
export function resetTokenExpiredHandling() {
  console.log('手动重置Token过期处理锁')
  isTokenExpiredHandling = false
}

/**
 * 获取当前token过期处理状态（用于调试）
 */
export function getTokenExpiredHandlingStatus() {
  return isTokenExpiredHandling
}

/**
 * 验证store和本地存储中的token是否同步
 * @returns {Object} 同步状态信息
 */
export function validateTokenSync() {
  try {
    const localToken = getToken()
    let storeToken = null

    // 安全地获取store中的token
    try {
      const store = require('@/store').default
      storeToken = store.getters && store.getters.token
    } catch (error) {
      console.warn('validateTokenSync: 无法获取store中的token', error)
    }

    const isSync = localToken === storeToken
    const result = {
      isSync,
      localToken: localToken ? 'exists' : 'empty',
      storeToken: storeToken ? 'exists' : 'empty',
      localTokenLength: localToken ? localToken.length : 0,
      storeTokenLength: storeToken ? storeToken.length : 0
    }

    console.log('Token同步状态检查:', result)
    return result
  } catch (error) {
    console.error('validateTokenSync: 检查token同步状态失败', error)
    return {
      isSync: false,
      error: error.message
    }
  }
}

/**
 * 修复token同步问题
 * 以本地存储的token为准，同步到store
 */
export function fixTokenSync() {
  try {
    const localToken = getToken()
    console.log('fixTokenSync: 开始修复token同步，本地token:', localToken ? 'exists' : 'empty')

    // 获取store实例
    const store = require('@/store').default
    if (!store) {
      console.error('fixTokenSync: 无法获取store实例')
      return false
    }

    // 以本地存储为准，更新store
    if (localToken && localToken.trim() !== '') {
      store.commit('user/SET_TOKEN', localToken)
      console.log('fixTokenSync: 已将本地token同步到store')
    } else {
      store.commit('user/SET_TOKEN', '')
      console.log('fixTokenSync: 已清空store中的token')
    }

    // 验证修复结果
    const validation = validateTokenSync()
    console.log('fixTokenSync: 修复后的同步状态:', validation)

    return validation.isSync
  } catch (error) {
    console.error('fixTokenSync: 修复token同步失败', error)
    return false
  }
}

/**
 * 关于rule同意用户协议的获取、设置、删除的方法
 */
const Rule = 'Kat-Gather-Rule'
export function getRule() {
  return uni.getStorageSync(Rule)
}

export function setRule(rule) {
  return uni.setStorageSync(Rule, rule)
}

export function removeRule() {
  return uni.removeStorageSync(Rule)
} 
