export function sendMsgToAiWebSocket(query, callback) {
  const params = new URLSearchParams({
    text: query.text,
    model: 'qwen:1.8b' // 参数直接拼接到URL
  });

  const wsUrl = `${baseUrl}/Models/streamV2?${params.toString()}`;
  const socket = new WebSocket(wsUrl);

  // WebSocket 连接建立时触发
  socket.onopen = () => {
    console.log('WebSocket连接已打开');
  };

  // WebSocket 接收到消息时触发
  socket.onmessage = (event) => {
    try {
      const chunk = event.data;
      callback(chunk, null);
    } catch (err) {
      callback(null, { error: err.message });
    }
  };

  // WebSocket 发生错误时触发
  socket.onerror = (err) => {
    callback(null, { error: err.message });
    socket.close();
  };

  // WebSocket 关闭时触发
  socket.onclose = () => {
    callback(null, { done: true });
    console.log('WebSocket连接已关闭');
  };

  return socket; // 返回 WebSocket 实例
}
