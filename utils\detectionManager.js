/**
 * 综合检测数据管理工具
 * 用于管理舌诊和体质检测的数据流转
 */

class DetectionManager {
  constructor() {
    this.STORAGE_KEYS = {
      USER_INFO: 'detection_user_info',
      TONGUE_DATA: 'detection_tongue_data', 
      CONSTITUTION_DATA: 'detection_constitution_data',
      DETECTION_STATE: 'detection_state'
    }
  }

  /**
   * 保存用户信息
   * @param {Object} userInfo 用户信息
   */
  saveUserInfo(userInfo) {
    uni.setStorageSync(this.STORAGE_KEYS.USER_INFO, userInfo)
  }

  /**
   * 获取用户信息
   * @returns {Object|null} 用户信息
   */
  getUserInfo() {
    return uni.getStorageSync(this.STORAGE_KEYS.USER_INFO) || null
  }

  /**
   * 保存舌诊数据
   * @param {Object} tongueData 舌诊数据
   */
  saveTongueData(tongueData) {
    uni.setStorageSync(this.STORAGE_KEYS.TONGUE_DATA, {
      ...tongueData,
      timestamp: Date.now()
    })
  }

  /**
   * 获取舌诊数据
   * @returns {Object|null} 舌诊数据
   */
  getTongueData() {
    return uni.getStorageSync(this.STORAGE_KEYS.TONGUE_DATA) || null
  }

  /**
   * 保存体质检测数据
   * @param {Object} constitutionData 体质检测数据
   */
  saveConstitutionData(constitutionData) {
    uni.setStorageSync(this.STORAGE_KEYS.CONSTITUTION_DATA, {
      ...constitutionData,
      timestamp: Date.now()
    })
  }

  /**
   * 获取体质检测数据
   * @returns {Object|null} 体质检测数据
   */
  getConstitutionData() {
    return uni.getStorageSync(this.STORAGE_KEYS.CONSTITUTION_DATA) || null
  }

  /**
   * 保存检测状态
   * @param {Object} state 检测状态
   */
  saveDetectionState(state) {
    uni.setStorageSync(this.STORAGE_KEYS.DETECTION_STATE, state)
  }

  /**
   * 获取检测状态
   * @returns {Object|null} 检测状态
   */
  getDetectionState() {
    return uni.getStorageSync(this.STORAGE_KEYS.DETECTION_STATE) || null
  }

  /**
   * 检查是否有完整的综合检测数据
   * @returns {Boolean} 是否有完整数据
   */
  hasCompleteData() {
    const userInfo = this.getUserInfo()
    const tongueData = this.getTongueData()
    const constitutionData = this.getConstitutionData()
    
    return !!(userInfo && tongueData && constitutionData)
  }

  /**
   * 检查是否有舌诊数据
   * @returns {Boolean} 是否有舌诊数据
   */
  hasTongueData() {
    const tongueData = this.getTongueData()
    return !!tongueData
  }

  /**
   * 检查是否有体质检测数据
   * @returns {Boolean} 是否有体质检测数据
   */
  hasConstitutionData() {
    const constitutionData = this.getConstitutionData()
    return !!constitutionData
  }

  /**
   * 获取综合检测数据
   * @returns {Object} 综合检测数据
   */
  getComprehensiveData() {
    return {
      userInfo: this.getUserInfo(),
      tongueData: this.getTongueData(),
      constitutionData: this.getConstitutionData()
    }
  }

  /**
   * 清除指定类型的数据
   * @param {String} type 数据类型: 'user', 'tongue', 'constitution', 'state', 'all'
   */
  clearData(type = 'all') {
    switch (type) {
      case 'user':
        uni.removeStorageSync(this.STORAGE_KEYS.USER_INFO)
        break
      case 'tongue':
        uni.removeStorageSync(this.STORAGE_KEYS.TONGUE_DATA)
        break
      case 'constitution':
        uni.removeStorageSync(this.STORAGE_KEYS.CONSTITUTION_DATA)
        break
      case 'state':
        uni.removeStorageSync(this.STORAGE_KEYS.DETECTION_STATE)
        break
      case 'all':
        Object.values(this.STORAGE_KEYS).forEach(key => {
          uni.removeStorageSync(key)
        })
        break
    }
  }

  /**
   * 显示综合检测询问弹窗
   * @param {String} targetType 目标检测类型
   * @returns {Promise} 用户选择结果
   */
  showComprehensiveDialog(targetType) {
    const typeText = targetType === 'tongue' ? '舌诊检测' : '体质检测'

    return new Promise((resolve) => {
      uni.showModal({
        title: '综合检测',
        content: `检测到您已完成部分检测，是否继续进行${typeText}以获得更全面的健康分析报告？`,
        confirmText: '继续检测',
        cancelText: '暂不需要',
        success: (res) => {
          resolve(res)
        },
        fail: () => {
          resolve({ confirm: false, cancel: true })
        }
      })
    })
  }

  /**
   * 生成跳转URL
   * @param {String} targetPage 目标页面
   * @param {String} fromPage 来源页面
   * @param {Object} extraParams 额外参数
   * @returns {String} 跳转URL
   */
  generateNavigateUrl(targetPage, fromPage, extraParams = {}) {
    const baseUrls = {
      'tongue': '/pages/gather/diagnosis/diagnosis',
      'constitution': '/pages/gather/healthy/detailed'
    }
    
    const baseUrl = baseUrls[targetPage]
    if (!baseUrl) {
      throw new Error(`未知的目标页面: ${targetPage}`)
    }
    
    const params = {
      from: fromPage,
      comprehensive: 'true',
      ...extraParams
    }
    
    const queryString = Object.keys(params)
      .map(key => `${key}=${encodeURIComponent(params[key])}`)
      .join('&')
    
    return `${baseUrl}?${queryString}`
  }

  /**
   * 检查数据是否过期（24小时）
   * @param {Object} data 包含timestamp的数据
   * @returns {Boolean} 是否过期
   */
  isDataExpired(data) {
    if (!data || !data.timestamp) return true
    
    const now = Date.now()
    const expireTime = 24 * 60 * 60 * 1000 // 24小时
    
    return (now - data.timestamp) > expireTime
  }

  /**
   * 清理过期数据
   */
  cleanExpiredData() {
    const tongueData = this.getTongueData()
    const constitutionData = this.getConstitutionData()
    
    if (tongueData && this.isDataExpired(tongueData)) {
      this.clearData('tongue')
    }
    
    if (constitutionData && this.isDataExpired(constitutionData)) {
      this.clearData('constitution')
    }
  }

  /**
   * 获取检测进度信息
   * @returns {Object} 检测进度
   */
  getDetectionProgress() {
    return {
      hasTongue: this.hasTongueData(),
      hasConstitution: this.hasConstitutionData(),
      hasUser: !!this.getUserInfo(),
      isComplete: this.hasCompleteData()
    }
  }
}

// 创建单例实例
const detectionManager = new DetectionManager()

export default detectionManager
