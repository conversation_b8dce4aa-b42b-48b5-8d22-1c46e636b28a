<template>
  <view class="comprehensive-result">
    <!-- 头部区域 -->
    <view class="header-section">
      <view class="header-content">
        <view class="header-left">
          <view class="report-title">AI舌象体质报告</view>
          <view class="report-time" v-if="reportTime">检测时间：{{ reportTime }}</view>
          <view class="report-subtitle">
            <view class="subtitle-text">AI辅导专业仅供参考，针对异常情况建议咨询专业医生进一步诊断及治疗。</view>
          </view>
        </view>
        <view class="header-right" v-if="tongueImageUrl">
          <view class="tongue-image-container" @click="previewTongueImage">
            <image
              class="tongue-image"
              :src="tongueImageUrl"
              mode="aspectFill"
              @error="onImageError"
              @load="onImageLoad"
            />
          </view>
        </view>
      </view>
    </view>

    <!-- 体质显示区域 -->
    <view class="constitution-circle-section" v-if="constitutionTypeName">
      <view class="circle-container">
        <!-- a01.png作为背景图片 -->
        <view class="background-image" :style="{backgroundImage: 'url('+imageURL+')'}">
          <!-- 动态中心圆，显示体质信息 -->
          <view class="dynamic-center-circle">
            <view class="center-text">{{ constitutionTypeName }}</view>
            <view class="center-subtitle">体质倾向</view>
          </view>
        </view>
      </view>
    </view>

    <view class="analysis-section">
      <!-- 舌象特征分析 -->
      <view class="result-card" v-if="filteredFeatureList.length">
        <view class="section-title">
          <view class="section-bar"></view>
          <view class="section-text">舌象特征分析</view>
        </view>
        <view class="feature-table">
          <view class="feature-row feature-row-desc" v-for="item in filteredFeatureList" :key="item.label">
            <text class="feature-label">{{ item.label }}</text>
            <text class="feature-value">{{ item.value }}</text>
            <text class="feature-desc" v-if="getFeatureDesc(item)">{{ getFeatureDesc(item) }}</text>
          </view>
        </view>
      </view>

      <!-- 体质与证候辨识 -->
      <view class="result-card" v-if="hasConstitutionData || hasSyndromeData">
        <view class="section-title">
          <view class="section-bar"></view>
          <view class="section-text">体质与证候辨识</view>
        </view>
        <view class="constitution-content">
          <!-- 体质辨识部分 -->
          <view v-if="hasConstitutionData" class="constitution-item">
            <view class="constitution-item-title-row">
              <view class="constitution-item-bar"></view>
              <view class="constitution-title">体质辨识</view>
            </view>
            <view class="constitution-details">
              <!-- 体质类型 -->
              <view class="constitution-detail-item" v-if="constitutionTypeName">
                <text class="constitution-label">体质：</text>
                <text class="constitution-value primary">{{ constitutionTypeName }}</text>
              </view>
              <!-- 体质表现 -->
              <view class="constitution-detail-item" v-if="constitutionPerformance">
                <text class="constitution-label">体质表现：</text>
                <text class="constitution-value">{{ constitutionPerformance }}</text>
              </view>
            </view>
          </view>

          <!-- 证候辨识部分 -->
          <view v-if="hasSyndromeData" class="constitution-item">
            <view class="constitution-item-title-row">
              <view class="constitution-item-bar"></view>
              <view class="constitution-title">证候辨识</view>
            </view>
            <view class="constitution-details">
              <!-- 证候描述 -->
              <view class="constitution-detail-item" v-if="syndromeSecondary">
                <text class="constitution-label">证候：</text>
                <text class="constitution-value syndrome-highlight">{{ syndromeSecondary }}</text>
              </view>
              <!-- 体质描述 -->
              <view class="constitution-detail-item" v-if="syndromePerformance">
                <text class="constitution-label">证候表现：</text>
                <text class="constitution-value">{{ syndromePerformance }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 养生方案 -->
      <view class="result-card" v-if="hasWellnessAdvice">
        <view class="section-title">
          <view class="section-bar"></view>
          <view class="section-text">养生方案</view>
        </view>
        <view class="wellness-content">
          <!-- 综合养生建议 -->
          <view v-if="advice" class="wellness-item">
            <view class="wellness-item-title-row">
              <view class="wellness-item-bar"></view>
              <view class="wellness-title">综合建议</view>
            </view>
            <view class="wellness-desc">{{ advice }}</view>
          </view>

          <!-- 按摩调理 -->
          <view v-if="getWellnessInfo('Massage_Moxibustion')" class="wellness-item">
            <view class="wellness-item-title-row">
              <view class="wellness-item-bar"></view>
              <view class="wellness-title">按摩调理</view>
            </view>
            <view class="wellness-desc">{{ getWellnessInfo('Massage_Moxibustion') }}</view>
          </view>

          <!-- 运动养生 -->
          <view v-if="getWellnessInfo('Exercise')" class="wellness-item">
            <view class="wellness-item-title-row">
              <view class="wellness-item-bar"></view>
              <view class="wellness-title">运动养生</view>
            </view>
            <view class="wellness-desc">{{ getWellnessInfo('Exercise') }}</view>
          </view>

          <!-- 饮食调养 -->
          <view v-if="getWellnessInfo('Diet')" class="wellness-item">
            <view class="wellness-item-title-row">
              <view class="wellness-item-bar"></view>
              <view class="wellness-title">饮食调养</view>
            </view>
            <view class="wellness-desc">{{ getWellnessInfo('Diet') }}</view>
          </view>

          <!-- 生活起居 -->
          <view v-if="getWellnessInfo('Life')" class="wellness-item">
            <view class="wellness-item-title-row">
              <view class="wellness-item-bar"></view>
              <view class="wellness-title">生活起居</view>
            </view>
            <view class="wellness-desc">{{ getWellnessInfo('Life') }}</view>
          </view>

          <!-- 拔罐刮痧 -->
          <view v-if="getWellnessInfo('Cupping_Scraping')" class="wellness-item">
            <view class="wellness-item-title-row">
              <view class="wellness-item-bar"></view>
              <view class="wellness-title">拔罐刮痧</view>
            </view>
            <view class="wellness-desc">{{ getWellnessInfo('Cupping_Scraping') }}</view>
          </view>

          <!-- 五韵调元 -->
          <view v-if="getWellnessInfo('rhymes')" class="wellness-item">
            <view class="wellness-item-title-row">
              <view class="wellness-item-bar"></view>
              <view class="wellness-title">五韵调元</view>
            </view>
            <view class="wellness-desc">{{ getWellnessInfo('rhymes') }}</view>
          </view>
        </view>
      </view>

      <!-- 节气养生 -->
      <view class="result-card" v-if="solarTerms">
        <view class="section-title">
          <view class="section-bar"></view>
          <view class="section-text">节气养生</view>
        </view>
        <view class="solar-content">
          <!-- 节气信息 -->
          <view class="solar-info" v-if="solarTermsInfo">
            <text class="solar-term">{{ solarTermsInfo }}</text>
          </view>
          <!-- 节气养生建议 -->
          <view class="solar-advice">
            <text class="advice-text">{{ solarTerms }}</text>
          </view>
        </view>
      </view>


    </view>


  </view>
</template>

<script>
export default {
  data() {
    return {
      resultData: {},
      tongueResult: {},
      constitutionResult: {},
      syndromeResult: {},
      advice: '',
      solarTerms: '',
      scores: {},
      reportTime: '',
      tags: [],
      healthScore: 0,
      tongueImageUrl: '', // 当前使用的舌诊图片URL（仅使用服务器分析图片）
      featureMap: {
        tongue_color: { label: '舌色' },
        tongue_shape: { label: '舌形' },
        fur_color: { label: '苔色' },
        fur_shape: { label: '苔质' },
        greasy_fur: { label: '腻苔' },
        crack: { label: '裂纹' },
        indentation: { label: '齿痕' },
        peel_fur: { label: '剥苔' }
      },
      // 舌诊特征值到描述key的映射
      fieldMapping: {
        // 舌色映射
        '淡红': '淡红舌', '淡白': '淡白舌', '红': '红舌', '绛': '绛舌', '紫': '紫舌', '青': '青舌',
        '淡红舌': '淡红舌', '淡白舌': '淡白舌', '红舌': '红舌', '绛舌': '绛舌', '紫舌': '紫舌', '青舌': '青舌',
        // 舌形映射
        '苍老': '苍老舌', '娇嫩': '娇嫩舌', '胖大': '胖大舌', '瘦薄': '瘦薄舌', '点刺': '点刺舌', '裂纹': '裂纹舌', '齿痕': '齿痕舌',
        '苍老舌': '苍老舌', '娇嫩舌': '娇嫩舌', '胖大舌': '胖大舌', '瘦薄舌': '瘦薄舌', '点刺舌': '点刺舌', '裂纹舌': '裂纹舌', '齿痕舌': '齿痕舌',
        // 苔色映射
        '白苔': '白苔', '黄苔': '黄苔', '灰苔': '灰黑苔', '黑苔': '灰黑苔', '灰黑苔': '灰黑苔', '白': '白苔', '黄': '黄苔', '灰': '灰黑苔', '黑': '灰黑苔', '灰黑': '灰黑苔',
        // 苔质映射
        '薄': '薄苔', '厚': '厚苔', '润': '润苔', '滑': '滑苔', '燥': '燥苔', '糙': '糙苔', '腻': '腻苔', '腐': '腐苔', '剥': '剥苔',
        '薄苔': '薄苔', '厚苔': '厚苔', '润苔': '润苔', '滑苔': '滑苔', '燥苔': '燥苔', '糙苔': '糙苔', '腻苔': '腻苔', '腐苔': '腐苔', '剥苔': '剥苔',
        // 其他特征映射
        '有': '有', '无': '无'
      },
      imageURL: '/static/images/icons/a01.png'
    }
  },
  computed: {
    getanalysis() {
      try {
        if (!this.resultData || typeof this.resultData !== 'object') {
          return {};
        }
        const analysis = this.resultData.getanalysis ||
                        (this.resultData.data && this.resultData.data.getanalysis) || {};
        return analysis && typeof analysis === 'object' ? analysis : {};
      } catch (error) {
        console.error('getanalysis 计算属性错误:', error);
        return {};
      }
    },

    // 舌象特征列表 - 根据API返回的数据结构映射
    featureList() {
      try {
        // 从API数据中提取舌象特征
        const apiData = this.resultData.data || this.resultData.apiResponse || this.resultData;

        return [
          { label: '舌色', value: this.tongueResult.fur_color || apiData.fur_color || '' },
          { label: '舌形', value: this.tongueResult.fur_shape || apiData.fur_shape || '' },
          { label: '裂纹', value: this.tongueResult.crack || apiData.crack || '' },
          { label: '齿痕', value: this.tongueResult.indentation || apiData.indentation || '' },
          { label: '苔色', value: this.tongueResult.fur_color || apiData.fur_color || '' },
          { label: '苔质', value: this.tongueResult.fur_shape || apiData.fur_shape || '' },
          { label: '腻苔', value: this.tongueResult.greasy_fur || apiData.greasy_fur || '' },
          { label: '剥苔', value: this.tongueResult.peel_fur || apiData.peel_fur || '' },
        ];
      } catch (error) {
        console.error('featureList 计算属性错误:', error);
        return [];
      }
    },

    // 过滤后的特征列表（只显示有值且不为"无"的特征）
    filteredFeatureList() {
      try {
        if (!this.featureList || !Array.isArray(this.featureList)) {
          return [];
        }
        return this.featureList.filter(item =>
          item &&
          item.value &&
          typeof item.value === 'string' &&
          item.value !== '无' &&
          item.value.trim() !== ''
        );
      } catch (error) {
        console.error('filteredFeatureList 计算属性错误:', error);
        return [];
      }
    },

    // 是否有体质数据
    hasConstitutionData() {
      return this.constitutionTypeName || this.primaryConstitution || this.secondaryConstitution || this.constitutionPerformance;
    },

    // 是否有证候数据
    hasSyndromeData() {
      return this.syndromeSecondary || this.syndromePerformance || Object.keys(this.otherSyndromeData).length > 0;
    },

    // 体质类型名称 - 根据API返回的数据结构获取
    constitutionTypeName() {
      try {
        const apiData = this.resultData.data || this.resultData.apiResponse || this.resultData;

        // 优先从体质数据中获取
        if (this.constitutionResult && this.constitutionResult.primary) {
          return this.constitutionResult.primary;
        }

        // 从API根级别获取
        if (apiData && apiData.primary) {
          return apiData.primary;
        }

        // 从舌诊数据中获取
        if (this.tongueResult && this.tongueResult.type_name) {
          return this.tongueResult.type_name;
        }

        return '';
      } catch (error) {
        console.error('constitutionTypeName 计算属性错误:', error);
        return '';
      }
    },

    // 主要体质 - 根据API返回的数据结构获取
    primaryConstitution() {
      try {
        const apiData = this.resultData.data || this.resultData.apiResponse || this.resultData;

        // 从API根级别获取
        if (apiData && apiData.primary) {
          return apiData.primary;
        }


        return '';
      } catch (error) {
        console.error('primaryConstitution 计算属性错误:', error);
        return '';
      }
    },

    // 次要体质 - 根据API返回的数据结构获取
    secondaryConstitution() {
      try {
        const apiData = this.resultData.data || this.resultData.apiResponse || this.resultData;
        // 从API根级别获取
        if (apiData && apiData.secondary) {
          return apiData.secondary;
        }
    
        return '';
      } catch (error) {
        console.error('secondaryConstitution 计算属性错误:', error);
        return '';
      }
    },

    // 体质表现 - 根据API返回的数据结构获取
    constitutionPerformance() {
      try {
        const apiData = this.resultData.data || this.resultData.apiResponse || this.resultData;

        // 从constitution_tizhi字段获取
        if (apiData && apiData.constitution_tizhi && apiData.constitution_tizhi.Performance) {
          return apiData.constitution_tizhi.Performance;
        }

        // 从体质数据中获取
        if (this.constitutionResult && this.constitutionResult.Performance) {
          return this.constitutionResult.Performance;
        }

        // 从嵌套的constitution字段获取
        if (apiData && apiData.constitution && apiData.constitution.Performance) {
          return apiData.constitution.Performance;
        }

        return '';
      } catch (error) {
        console.error('constitutionPerformance 计算属性错误:', error);
        return '';
      }
    },

    // 证候描述 - 根据API返回的数据结构获取
    syndromeSecondary() {
      try {
        const apiData = this.resultData.data || this.resultData.apiResponse || this.resultData;

        // 从API根级别获取
        if (apiData && apiData.externalResponse && apiData.externalResponse.secondary) {
          return apiData.externalResponse.secondary;
        }

        if (apiData && apiData.secondary) {
          return apiData.secondary;
        }

        return '';
      } catch (error) {
        console.error('syndromeSecondary 计算属性错误:', error);
        return '';
      }
    },

    // 证候体质描述 - 根据API返回的数据结构获取
    syndromePerformance() {
      try {
        const apiData = this.resultData.data || this.resultData.apiResponse || this.resultData;

        // 从证候数据中获取
        if (this.syndromeResult && this.syndromeResult.Performance) {
          return this.syndromeResult.Performance;
        }

        // 从constitution_zhenghou字段获取
        if (apiData && apiData.constitution_zhenghou && apiData.constitution_zhenghou.Performance) {
          return apiData.constitution_zhenghou.Performance;
        }

        // 从嵌套的constitution字段获取
        if (apiData && apiData.constitution && apiData.constitution.Performance) {
          return apiData.constitution.Performance;
        }

        return '';
      } catch (error) {
        console.error('syndromePerformance 计算属性错误:', error);
        return '';
      }
    },

    // 其他证候数据 - 根据API返回的数据结构获取
    otherSyndromeData() {
      try {
        const excludeKeys = ['secondary', 'Performance', 'primary', 'Wellness_advice'];
        const result = {};

        // 安全检查：确保 resultData 存在
        if (!this.resultData || typeof this.resultData !== 'object') {
          return result;
        }

        const apiData = this.resultData.data || this.resultData.apiResponse || this.resultData;

        // 从constitution_zhenghou对象中获取证候数据
        const syndromeData = (apiData && apiData.constitution_zhenghou) || {};
        if (syndromeData && typeof syndromeData === 'object') {
          Object.keys(syndromeData).forEach(key => {
            if (key && excludeKeys && Array.isArray(excludeKeys) && !excludeKeys.includes(key) &&
                syndromeData[key] && syndromeData[key] !== '无' && syndromeData[key] !== '') {
              // 转换字段名为中文
              const chineseKey = this.getChineseFieldName(key);
              if (chineseKey) {
                result[chineseKey] = syndromeData[key];
              }
            }
          });
        }

        // 从constitution对象中获取其他数据（作为备用）
        const constitutionData = (apiData && apiData.constitution) || {};
        if (constitutionData && typeof constitutionData === 'object') {
          Object.keys(constitutionData).forEach(key => {
            if (key && excludeKeys && Array.isArray(excludeKeys) && !excludeKeys.includes(key) &&
                constitutionData[key] && constitutionData[key] !== '无' && constitutionData[key] !== '' &&
                !result[this.getChineseFieldName(key)]) { // 避免重复
              // 转换字段名为中文
              const chineseKey = this.getChineseFieldName(key);
              if (chineseKey) {
                result[chineseKey] = constitutionData[key];
              }
            }
          });
        }

        return result;
      } catch (error) {
        console.error('otherSyndromeData 计算错误:', error);
        return {};
      }
    },

    // 节气信息（从solarterms字段获取）
    solarTermsInfo() {
      const apiData = this.resultData.data || this.resultData.apiResponse || this.resultData;
      return (apiData && apiData.solarterms) || '';
    },

    // 是否有养生建议数据
    hasWellnessAdvice() {
      return this.advice ||
             this.getWellnessInfo('rhymes') ||
             this.getWellnessInfo('Massage_Moxibustion') ||
             this.getWellnessInfo('Exercise') ||
             this.getWellnessInfo('Diet') ||
             this.getWellnessInfo('Life') ||
             this.getWellnessInfo('Cupping_Scraping');
    },


  },
  onLoad(options) {
    try {
      if (options.result) {
        try {
          const raw = JSON.parse(decodeURIComponent(options.result))
          this.resultData = raw
          console.log('综合检测结果数据:', raw)

        // 获取API数据，支持多种数据结构
        const apiData = raw.data || raw.apiResponse || raw || {}
        console.log('API数据:', apiData)

        // 解析舌诊数据 - 根据API返回的数据结构
        this.tongueResult = apiData.tongueResult ||
                           apiData.tongue_result ||
                           apiData.externalResponse ||
                           apiData.tongueData ||
                           {}
        console.log('舌诊数据:', this.tongueResult)

        // 解析体质检测数据 - 根据API返回的数据结构
        this.constitutionResult = apiData.constitutionResult ||
                                 apiData.constitution_result ||
                                 apiData.constitution ||
                                 apiData.constitution_tizhi ||
                                 apiData.constitutionData ||
                                 {}
        console.log('体质数据:', this.constitutionResult)

        // 解析证候数据 - 从API返回的数据中提取
        this.syndromeResult = apiData.constitution_zhenghou ||
                             apiData.syndrome ||
                             apiData.zhenghou ||
                             {}
        console.log('证候数据:', this.syndromeResult)

        // 解析养生建议 - 支持多种字段名
        this.advice = apiData.advice ||
                     apiData.comprehensiveAdvice ||
                     apiData.Wellness_advice ||
                     (apiData.constitution && apiData.constitution.Wellness_advice) ||
                     ''
        console.log('养生建议:', this.advice)

        // 解析节气养生 - 支持多种字段名
        this.solarTerms = apiData.solarTerms ||
                         apiData.solar_advice ||
                         apiData.getsolar ||
                         apiData.solar ||
                         ''
        console.log('节气养生:', this.solarTerms)

        // 解析舌诊图片URL - 只使用服务器分析图片（image_A）
        this.tongueImageUrl = apiData.image_A ||  // 优先使用后端返回的分析图片
                             (this.tongueResult && this.tongueResult.image_A) ||
                             ''

        console.log('使用服务器分析图片URL:', this.tongueImageUrl)

        // 解析其他数据
        this.scores = apiData.scores || apiData.constitutionScores || apiData.scoresData || {}

        // 提取分析时间 - 支持多种时间字段
        let timeValue = raw.timestamp || raw.analysis_time || raw.request_time || raw.creatime || raw.createTime
        if (!timeValue && apiData) {
          timeValue = apiData.analysis_time || apiData.request_time || apiData.creatime || apiData.timestamp
        }
        this.reportTime = timeValue ? this.formatTime(timeValue) : this.getCurrentTime()
        console.log('设置报告时间:', this.reportTime, '原始时间值:', timeValue)

        this.tags = apiData.tags || []

        console.log('解析完成')

      } catch (e) {
        console.error('数据解析失败:', e)
        uni.showToast({ title: '数据解析失败', icon: 'none' })
        }
      } else {
        console.warn('未接收到结果数据')
      }
    } catch (globalError) {
      console.error('onLoad 全局错误:', globalError)
      uni.showToast({ title: '页面初始化失败', icon: 'none' })
    }
  },

  onShow() {
    // 页面显示时的处理
    console.log('综合检测报告页面显示');
  },

  methods: {
    formatTime(ts) {
      if (!ts) return ''
      const date = new Date(ts)
      const y = date.getFullYear()
      const m = String(date.getMonth() + 1).padStart(2, '0')
      const d = String(date.getDate()).padStart(2, '0')
      const h = String(date.getHours()).padStart(2, '0')
      const min = String(date.getMinutes()).padStart(2, '0')
      return `${y}-${m}-${d} ${h}:${min}`
    },

    // 获取当前时间
    getCurrentTime() {
      const now = new Date()
      const y = now.getFullYear()
      const m = String(now.getMonth() + 1).padStart(2, '0')
      const d = String(now.getDate()).padStart(2, '0')
      const h = String(now.getHours()).padStart(2, '0')
      const min = String(now.getMinutes()).padStart(2, '0')
      return `${y}-${m}-${d} ${h}:${min}`
    },

    getDesc(value) {
      const key = this.fieldMapping[value] || value
      return this.getanalysis[key] || ''
    },

    // 获取养生信息
    getWellnessInfo(key) {
      // 尝试从多个可能的数据源获取养生信息
      const apiData = this.resultData.data || this.resultData.apiResponse || this.resultData;

      // 从constitution对象中获取
      if (apiData && apiData.constitution && apiData.constitution[key]) {
        return apiData.constitution[key];
      }
      return '';
    },

    // 将英文字段名转换为中文
    getChineseFieldName(key) {
      const fieldNameMap = {
        'Cupping_Scraping': '拔罐刮痧',
        'Diet': '饮食调养',
        'Exercise': '运动养生',
        'Life': '生活起居',
        'Massage_Moxibustion': '按摩调理',
        'rhymes': '五韵调元',
        'Performance': '体质表现',
        'primary': '主要体质',
        'secondary': '次要体质',
        'Wellness_advice': '养生建议'
      };

      return fieldNameMap[key] || key;
    },







    // 获取特征详细描述
    getFeatureDesc(item) {
      try {
        // 安全检查：确保 item 存在且有必要的属性
        if (!item || (!item.value && !item.label)) {
          return '';
        }

        const descMap = this.getanalysis;
        if (!descMap || typeof descMap !== 'object' || Object.keys(descMap).length === 0) {
          return '';
        }

      // 尝试多种方式查找描述
      let description = '';

      // 1. 直接用值查找
      if (item.value) {
        description = descMap[item.value];
        if (description) {
          return description;
        }
      }

      // 2. 用映射后的值查找
      if (item.value && this.fieldMapping) {
        const mappedValue = this.fieldMapping[item.value];
        if (mappedValue) {
          description = descMap[mappedValue];
          if (description) {
            return description;
          }
        }
      }

      // 3. 用label查找
      if (item.label) {
        description = descMap[item.label];
        if (description) {
          return description;
        }
      }

      // 4. 用映射后的label查找
      if (item.label && this.fieldMapping) {
        const mappedLabel = this.fieldMapping[item.label];
        if (mappedLabel) {
          description = descMap[mappedLabel];
          if (description) {
            return description;
          }
        }
      }

      // 5. 模糊匹配：在描述字段中查找包含当前值的项
      if (descMap && typeof descMap === 'object') {
        const entries = Object.entries(descMap);
        for (let i = 0; i < entries.length; i++) {
          const key = entries[i][0];
          const desc = entries[i][1];
          if (key && item.value && typeof key === 'string' && typeof item.value === 'string' &&
              (key.includes(item.value) || item.value.includes(key))) {
            return desc;
          }
        }
      }

      return '';
      } catch (error) {
        console.error('getFeatureDesc 方法错误:', error);
        return '';
      }
    },

    // 计算体质文字在环形上的位置（用于模板绑定）
    getTextPositionStyleByIndex(index) {
      const total = this.constitutionTypes.length;
      const angle = (360 / total) * index - 90; // -90度让第一个文字在顶部
      const radius = 180; // 增大半径
      const radian = (angle * Math.PI) / 180;
      const x = Math.cos(radian) * radius;
      const y = Math.sin(radian) * radius;

      return {
        position: 'absolute',
        left: `${50 + (x / 7.5)}%`,
        top: `${50 + (y / 7.5)}%`,
        transform: 'translate(-50%, -50%)'
      };
    },

    // 计算体质文字在环形上的位置（用于计算属性）
    getTextPositionStyle(index) {
      return this.getTextPositionStyleByIndex(index);
    },

    // 计算体质文字在环形上的位置（保留原方法以防其他地方使用）
    getTextPosition(index) {
      return this.getTextPositionStyle(index);
    },



    // 预览舌诊分析图片
    previewTongueImage() {
      if (this.tongueImageUrl) {
        console.log('预览分析图片URL:', this.tongueImageUrl);
        uni.previewImage({
          urls: [this.tongueImageUrl],
          current: this.tongueImageUrl
        });
      } else {
        uni.showToast({
          title: '暂无分析图片可预览',
          icon: 'none'
        });
      }
    },

    // 图片加载成功
    onImageLoad() {
      console.log('分析图片加载成功:', this.tongueImageUrl);
    },

    // 图片加载失败
    onImageError() {
      console.log('分析图片加载失败:', this.tongueImageUrl);
      uni.showToast({
        title: '图片加载失败',
        icon: 'none'
      });
    }
  }
}
</script>

<style scoped>
.comprehensive-result {
  min-height: 100%;
  background: #f8fffe;
  padding-bottom: 40rpx; /* 页面底部留白 */
}

/* 体质环形动画样式 */
.constitution-circle-section {
  /* padding: 10rpx 32rpx; */
  background: #f8fffe;
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: -40rpx;
}

.circle-container {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 650rpx;
}

/* a01.png背景图片容器 */
.background-image {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 100%; /* 创建正方形容器 */
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  max-width: 550rpx;
  /* margin: 0 auto; */
}

/* 动态中心圆，显示体质信息 */
.dynamic-center-circle {
  position: absolute;
  top: 48%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 180rpx;
  height: 180rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 255, 254, 0.9) 100%);
  box-shadow: 0 4rpx 20rpx rgba(62, 198, 198, 0.25);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 2rpx solid rgba(62, 198, 198, 0.3);
  backdrop-filter: blur(10rpx);
  z-index: 10;
}



.center-text {
  font-size: 32rpx;
  font-weight: 700;
  color: #3ec6c6;
  margin-bottom: 4rpx;
  text-shadow: 0 1rpx 4rpx rgba(62, 198, 198, 0.2);
  letter-spacing: 1rpx;
  text-align: center;
}

.center-subtitle {
  font-size: 20rpx;
  color: #666;
  opacity: 0.8;
  font-weight: 500;
  text-align: center;
}



/* 头部区域 */
.header-section {
  background: #f8fffe;
  padding: 32rpx;
  border-radius: 0 0 24rpx 24rpx;
  margin-bottom: -40rpx;
}

.header-content {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}

.header-right {
  flex-shrink: 0;
  margin-left: 20rpx;
}

.tongue-image-container {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid #ffffff;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.2s ease;
}

/* 小程序不支持 :active 伪类，移除此样式 */

.tongue-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.header-left {
  flex: 1;
}

.report-title {
  font-size: 36rpx;
  font-weight: 700;
  color: rgb(73, 73, 73);
  margin-bottom: 8rpx;
  letter-spacing: 1rpx;
}

.report-time {
  font-size: 24rpx;
  color: rgb(73, 73, 73);
  opacity: 0.8;
  margin-bottom: 12rpx;
}

.report-subtitle {
  margin-top: 8rpx;
}

.subtitle-text {
  font-size: 24rpx;
  color: rgb(73, 73, 73);
  opacity: 0.8;
  line-height: 1.6;
  text-align: left;
  background: #ffffff;
  padding: 16rpx 20rpx;
  border-radius: 12rpx;
  border: 1rpx solid rgba(62, 198, 198, 0.1);
  box-shadow: 0 2rpx 8rpx rgba(62, 198, 198, 0.05);
}

.analysis-section {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
  padding: 15rpx 32rpx 32rpx 32rpx;
}

.result-card {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  margin-bottom: 24rpx;
}

/* 统一的标题样式 */
.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.section-bar {
  width: 8rpx;
  height: 32rpx;
  background: linear-gradient(135deg, #3ec6c6 0%, #2ba8a8 100%);
  border-radius: 4rpx;
  margin-right: 16rpx;
}

.section-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  letter-spacing: 1rpx;
}

/* 舌象特征分析样式 */
.feature-table {
  margin-bottom: 32rpx;
}

.feature-row-desc {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f2f5;
}

.feature-row-desc:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.feature-label {
  width: 80rpx;
  color: #444c56;
  font-size: 28rpx;
  font-weight: 500;
  flex-shrink: 0;
  line-height: 44rpx;
  padding-top: 8rpx;
  padding-bottom: 8rpx;
  display: flex;
  align-items: center;
}

.feature-value {
  min-width: 100rpx;
  max-width: 120rpx;
  color: #3ec6c6;
  font-size: 28rpx;
  font-weight: bold;
  background: #f4f8fb;
  border-radius: 12rpx;
  padding: 8rpx 24rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
  align-self: flex-start;
  line-height: 44rpx;
  display: flex;
  align-items: center;
  text-align: center;
}

.feature-desc {
  flex: 1;
  color: #666;
  font-size: 26rpx;
  line-height: 1.6;
  padding-top: 8rpx;
  text-align: justify;
}

/* 体质与证候辨识样式 */
.constitution-content {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.constitution-item {
  background: #f8fffe;
  border-radius: 16rpx;
  padding: 24rpx;
}

.constitution-item-title-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.constitution-item-bar {
  width: 6rpx;
  height: 24rpx;
  background: #3ec6c6;
  border-radius: 3rpx;
  margin-right: 12rpx;
}

.constitution-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #2c3e50;
}

.constitution-details {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.constitution-detail-item {
  display: flex;
  align-items: flex-start;
  padding: 12rpx 16rpx;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 12rpx;
  margin-bottom: 8rpx;
  border: 1rpx solid rgba(62, 198, 198, 0.1);
}

.constitution-detail-item:last-child {
  margin-bottom: 0;
}

.constitution-label {
  width: 140rpx;
  color: #2c5555;
  font-size: 28rpx;
  font-weight: 600;
  flex-shrink: 0;
  line-height: 1.5;
  padding-right: 8rpx;
}

.constitution-value {
  flex: 1;
  color: #2c3e50;
  font-size: 28rpx;
  line-height: 1.6;
  text-align: justify;
  letter-spacing: 0.5rpx;
  word-break: break-all;
  white-space: normal;
}

.constitution-value.primary {
  color: #3ec6c6;
  font-weight: 700;
  font-size: 30rpx;
}

/* 节气养生样式 */
.solar-content {
  background: #f8fffe;
  border-radius: 16rpx;
  padding: 24rpx;
  /* border-left: 6rpx solid #3ec6c6; */
}

.solar-info {
  margin-bottom: 20rpx;
  text-align: center;
}

.solar-term {
  font-size: 32rpx;
  font-weight: 600;
  color: #3ec6c6;
  background: rgba(62, 198, 198, 0.1);
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  display: inline-block;
}

.solar-advice {
  margin-top: 16rpx;
}



/* 养生方案样式 */
.wellness-content {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.wellness-item {
  background: #f8fffe;
  border-radius: 16rpx;
  padding: 24rpx;
  
}

.wellness-item-title-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.wellness-item-bar {
  width: 6rpx;
  height: 24rpx;
  background: #3ec6c6;
  border-radius: 3rpx;
  margin-right: 12rpx;
}

.wellness-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #2c3e50;
}

.wellness-desc {
  color: #2c3e50;
  font-size: 28rpx;
  line-height: 1.8;
  text-align: justify;
  letter-spacing: 0.5rpx;
}

/* 节气养生样式 */
.advice-content {
  background: #f8fffe;
  border-radius: 16rpx;
  padding: 24rpx;
  border-left: 6rpx solid #3ec6c6;
}

.advice-text {
  color: #2c3e50;
  font-size: 28rpx;
  line-height: 1.8;
  text-align: justify;
  letter-spacing: 0.5rpx;
}



/* 响应式设计 */
@media screen and (max-width: 750rpx) {
  .comprehensive-result {
    padding: 24rpx;
  }

  .result-card {
    padding: 24rpx;
  }

  .feature-label {
    width: 70rpx;
    font-size: 26rpx;
  }

  .feature-value {
    font-size: 26rpx;
    padding: 6rpx 20rpx;
  }

  .feature-desc {
    font-size: 24rpx;
  }

  .constitution-label {
    width: 100rpx;
    font-size: 26rpx;
  }

  .constitution-value {
    font-size: 26rpx;
  }

  .advice-text {
    font-size: 26rpx;
  }

  /* 体质显示区域移动端适配 */
  .constitution-circle-section {
    padding: 8rpx 20rpx;
  }

  .circle-container {
    max-width: 500rpx;
  }

  .background-image {
    max-width: 450rpx;
  }

  .dynamic-center-circle {
    width: 140rpx;
    height: 140rpx;
  }

  .center-text {
    font-size: 28rpx;
  }

  .center-subtitle {
    font-size: 18rpx;
  }

  /* 舌诊图片移动端适配 */
  .tongue-image-container {
    width: 100rpx;
    height: 100rpx;
    border: 3rpx solid #ffffff;
  }
}

/* 超小屏幕适配 */
@media screen and (max-width: 600rpx) {
  /* .constitution-circle-section {
    padding: 5rpx 15rpx;
  } */

  .circle-container {
    max-width: 400rpx;
  }

  .background-image {
    max-width: 380rpx;
  }

  .dynamic-center-circle {
    width: 120rpx;
    height: 120rpx;
  }

  .center-text {
    font-size: 24rpx;
  }

  .center-subtitle {
    font-size: 16rpx;
  }
}

.syndrome-highlight {
  color: #3ec6c6;
  font-weight: bold;
  font-size: 32rpx;
  letter-spacing: 1rpx;
}
</style>
