<template>
  <view class="help-container">
    <view v-for="(item, findex) in list" :key="findex" :title="item.title" class="list-title">
      <view class="text-title">
        <view :class="item.icon"></view>{{ item.title }}
      </view>
      <view class="childList">
        <view v-for="(child, zindex) in item.childList" :key="zindex" class="question" hover-class="hover"
          @click="handleText(child)">
          <view class="text-item">{{ child.title }}</view>
          <view class="line" v-if="zindex !== item.childList.length - 1"></view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        list: [{
            icon: 'iconfont icon-help',
            title: '数智舌诊问题',
            childList: [/* {
              title: '数智舌诊开源吗？',
              content: '开源'
            }, {
              title: '数智舌诊可以商用吗？',
              content: '可以'
            },*/ {
              title: '数智舌诊官网地址多少？',
              content: 'http://www.katlot.cn',
            }, {
              title: '数智舌诊文档地址多少？',
              content: 'http://www.katlot.cn',
            }]
          },
          {
            icon: 'iconfont icon-help',
            title: '其他问题',
            childList: [{
              title: '如何退出登录？',
              content: '请点击[我的] - [应用设置] - [退出登录]即可退出登录',
            }, {
              title: '在哪查看检测记录？',
              content: '请点击[我的] - [健康记录] 点击相应的模块，即可查看与之相关的检测记录',
            }, {
              title: '如何修改登录密码？',
              content: '请点击[我的] - [应用设置] - [修改密码]即可修改登录密码',
            }]
          }
        ]
      }
    },
    methods: {
      handleText(item) {
        this.$tab.navigateTo(`/pages/my/common/textview/index?title=${item.title}&content=${item.content}`)
      }
    }
  }
</script>

<style lang="scss" scoped>
  page {
    background-color: #f8f8f8;
  }

  .help-container {
    margin-bottom: 100rpx;
    padding: 40rpx;
  }

  .list-title {
    margin-bottom: 40rpx;
  }

  .childList {
    background: #ffffff;
    box-shadow: 0px 0px 15rpx rgba(193, 193, 193, 0.3);
    border-radius: 20rpx;
    margin-top: 15rpx;
  }

  .line {
    width: 100%;
    height: 1rpx;
    background-color: #F0F0F0;
  }

  .text-title {
    color: #303133;
    font-size: 36rpx;
    font-weight: bold;
    margin-left: 20rpx;

    .iconfont {
      font-size: 20px;
      margin-right: 15rpx;
    }
  }

  .text-item {
    font-size: 32rpx;
    padding: 30rpx;
  }

  .question {
    color: #606266;
    font-size: 30rpx;
  }
</style>
