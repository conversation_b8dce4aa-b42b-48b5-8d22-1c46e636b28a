// 应用全局配置
module.exports = {
  // baseUrl: 'https://vue.ruoyi.vip/prod-api',
  // baseUrl: 'http://***********:8089',
  // baseUrl: 'http://**************:8091',
  baseUrl: 'http://************:8091',
  // baseUrl: 'http://**************:8090',
  // baseUrl: 'http://************:8099',
  // baseUrl: 'http://www.aigather.katlot.cn',
  // baseUrl: 'http://************:8096',
  // baseUrl: 'http://***********:8086',
  // 应用信息
  appInfo: {
    // 应用名称
    name: "数智舌诊",
    // 应用版本
    version: "1.1.29",
    // 应用logo
    logo: "/static/logo.png",
    // 官方网站
    site_url: "http://www.katlot.cn",
    // 政策协议
    agreements: [{
        title: "隐私政策",
        url: "http://www.katlot.cn",
      },
      {
        title: "用户服务协议",
        url: "http://www.katlot.cn",
      }
    ]
  },
  transpileDependencies: [
      'abortcontroller-polyfill',
      '@microsoft/fetch-event-source'
    ]
}
