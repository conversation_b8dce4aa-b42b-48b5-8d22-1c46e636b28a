import request from '@/utils/request'

//历史记录api - 获取所有类型的历史记录
export function videoList(params = {}) {
    // 设置默认参数，确保获取所有数据
    const defaultParams = {
        pageNum: 1,
        pageSize: 100, // 设置较大的页面大小以获取所有数据
        ...params
    }

    return request({
        url: '/system/video/list',
        method: 'get',
        params: defaultParams
    })
}

export function videoViews(data) {
    return request({
        url: '/system/video',
        method: 'put',
        data: data
    })
}