<template>
  <view class="review-detail">
    <view class="review-container" v-if="reviewData">
      <!-- 商品信息 -->
      <view class="product-info">
        <image :src="reviewData.product.image" class="product-image" />
        <view class="product-details">
          <text class="product-name">{{ reviewData.product.name }}</text>
          <text class="product-spec" v-if="reviewData.product.spec">{{ reviewData.product.spec }}</text>
        </view>
      </view>

      <!-- 评价信息 -->
      <view class="review-info">
        <view class="review-header">
          <image :src="userInfo.avatar || '/static/images/profile.jpg'" class="user-avatar" />
          <view class="user-details">
            <text class="username">{{ reviewData.anonymous ? '匿名用户' : (userInfo.nickname || '用户') }}</text>
            <text class="review-time">{{ reviewData.createTime }}</text>
          </view>
          <view class="rating">
            <uni-icons 
              v-for="i in 5" 
              :key="i" 
              :type="reviewData.rating >= i ? 'star-filled' : 'star'" 
              size="16" 
              :color="reviewData.rating >= i ? '#FFD700' : '#ddd'" 
            />
          </view>
        </view>

        <!-- 评价内容 -->
        <view class="review-content">
          <text>{{ reviewData.content }}</text>
        </view>

        <!-- 评价图片 -->
        <view class="review-images" v-if="reviewData.images && reviewData.images.length > 0">
          <image 
            v-for="(image, index) in reviewData.images" 
            :key="index"
            :src="image" 
            class="review-image"
            @click="previewImage(index)"
          />
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <button class="action-btn edit-btn" @click="editReview">编辑评价</button>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" v-else>
      <text class="iconfontA icon-pingjia empty-icon"></text>
      <text class="empty-text">评价不存在</text>
      <button class="back-btn" @click="goBack">返回</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      reviewId: '',
      reviewData: null,
      userInfo: {}
    }
  },

  onLoad(options) {
    this.reviewId = options.reviewId || '';
    this.loadUserInfo();
    this.loadReviewData();
  },

  methods: {
    // 加载用户信息
    loadUserInfo() {
      this.userInfo = uni.getStorageSync('userInfo') || {};
    },

    // 加载评价数据
    loadReviewData() {
      if (!this.reviewId) return;

      const userReviews = uni.getStorageSync('userReviews') || [];
      this.reviewData = userReviews.find(review => review.id === this.reviewId);
    },

    // 预览图片
    previewImage(index) {
      uni.previewImage({
        urls: this.reviewData.images,
        current: index
      });
    },

    // 编辑评价
    editReview() {
      uni.navigateTo({
        url: `/pages/shopping/appraise/appraise?reviewId=${this.reviewId}&edit=true`
      });
    },

    // 返回
    goBack() {
      uni.navigateBack();
    }
  }
}
</script>

<style lang="scss" scoped>
.review-detail {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20rpx;
}

.review-container {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.product-info {
  display: flex;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;

  .product-image {
    width: 120rpx;
    height: 120rpx;
    border-radius: 12rpx;
    margin-right: 20rpx;
  }

  .product-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;

    .product-name {
      font-size: 32rpx;
      color: #333;
      font-weight: 500;
      margin-bottom: 10rpx;
    }

    .product-spec {
      font-size: 24rpx;
      color: #999;
    }
  }
}

.review-info {
  padding: 30rpx;

  .review-header {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;

    .user-avatar {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      margin-right: 20rpx;
    }

    .user-details {
      flex: 1;

      .username {
        display: block;
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
        margin-bottom: 8rpx;
      }

      .review-time {
        font-size: 24rpx;
        color: #999;
      }
    }

    .rating {
      display: flex;
      gap: 4rpx;
    }
  }

  .review-content {
    margin-bottom: 20rpx;

    text {
      font-size: 28rpx;
      color: #333;
      line-height: 1.6;
    }
  }

  .review-images {
    display: flex;
    flex-wrap: wrap;
    gap: 10rpx;

    .review-image {
      width: 160rpx;
      height: 160rpx;
      border-radius: 12rpx;
    }
  }
}

.action-buttons {
  display: flex;
  padding: 30rpx;
  gap: 20rpx;
  border-top: 1rpx solid #f0f0f0;

  .action-btn {
    flex: 1;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    border-radius: 40rpx;
    font-size: 28rpx;
    font-weight: 500;

    &.edit-btn {
      background: #3ec6c6;
      color: #fff;
      border: none;
    }

    &.delete-btn {
      background: #fff;
      color: #ff5555;
      border: 2rpx solid #ff5555;
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  background: #fff;
  border-radius: 16rpx;

  .empty-icon {
    font-size: 120rpx;
    color: #ddd;
    margin-bottom: 30rpx;
  }

  .empty-text {
    font-size: 28rpx;
    color: #999;
    margin-bottom: 40rpx;
  }

  .back-btn {
    width: 200rpx;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    background: #3ec6c6;
    color: #fff;
    border-radius: 40rpx;
    font-size: 28rpx;
    border: none;
  }
}
</style>
