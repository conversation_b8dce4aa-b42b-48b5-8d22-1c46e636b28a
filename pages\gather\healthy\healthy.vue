<template>
	<view class="container">
		<!-- 顶部渐变背景和标题 -->
		<view class="header-bg">
			<view class="header">
				<text class="title">AI体质检测</text>
				<text class="history" @click="goToHistory">历史报告</text>
			</view>
		</view>
	
		<!-- 介绍说明 -->
		<view class="desc-card">
			<text class="desc-title">AI体质检测是根据《中医体质分类与判定》的体质判定标准研发而成</text>
			<text class="desc-detail">该标准将体质分为平和质、气虚质、阳虚质、阴虚质、痰湿质、湿热质、血瘀质、气郁质、特禀质九个类型。</text>
		</view>
		<!-- 引导问题 -->
		<view class="questions-card">
			<view class="question"><text>• 想知道自己的<text class="highlight">中医体质</text>吗？</text></view>
			<view class="question"><text>• 想知道<text class="highlight">体质与疾病</text>的关系吗？</text></view>
			<view class="question"><text>• 想知道如何<text class="highlight">针对自己的中医体质进行中医养生</text>吗？</text></view>
		</view>
		<!-- 占位，推送底部按钮到底部 -->
		<view style="flex:1;width:100%"></view>
		<!-- 底部提示和按钮整体移动到页面底部 -->
		<view class="bottom-action">
		<view class="bottom-tip">↓ 点击下方按钮【开始检测】</view>
		<button class="start-btn" @click="startDetection">开始检测</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
			}
		},
		methods: {
			goToHistory() {
				console.log('历史报告 - 跳转到健康记录体质选项卡');
				uni.navigateTo({
					url: '/pages/my/health/health?tab=1'
				});
			},
			startDetection() {
				console.log('开始检测 - 检查用户信息');
				// 首先检查用户信息是否完整
				if (this.checkUserInfo()) {
					// 用户信息完整，直接跳转到体质检测详情页面
					uni.navigateTo({
						url: '/pages/gather/healthy/detailed'
					});
				}
			},

			// 检查用户信息是否完整
			checkUserInfo() {
				try {
					const userInfo = uni.getStorageSync('userInfo') || {}

					// 检查必填字段（职业和婚姻状况改为选填）
					const requiredFields = [
						{ key: 'name', label: '姓名' },
						{ key: 'sex', label: '性别' },
						{ key: 'dateBirth', label: '出生年月' },
						{ key: 'phonenumber', label: '手机号' },
						{ key: 'height', label: '身高' },
						{ key: 'weight', label: '体重' }
					]

					const missingFields = []
					requiredFields.forEach(field => {
						if (!userInfo[field.key] || userInfo[field.key].toString().trim() === '') {
							missingFields.push(field.label)
						}
					})

					if (missingFields.length > 0) {
						this.showUserInfoModal(missingFields)
						return false
					}

					return true
				} catch (error) {
					console.error('检查用户信息失败:', error)
					this.showUserInfoModal(['基本信息'])
					return false
				}
			},

			// 显示用户信息完善提示弹窗
			showUserInfoModal(missingFields) {
				const fieldText = missingFields.length > 3
					? '基本信息'
					: missingFields.join('、')

				uni.showModal({
					title: '信息不完整',
					content: `为了更好的体质检测，请先完善${fieldText}等信息`,
					confirmText: '去完善',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							// 跳转到用户信息管理页面
							uni.navigateTo({
								url: '/pages/my/healthInput/analyse?from=constitution'
							})
						}
					}
				})
			},

			// 计算年龄的辅助方法
			calculateAge(birthDate) {
				if (!birthDate) return null

				try {
					const birth = new Date(birthDate)
					const today = new Date()
					let age = today.getFullYear() - birth.getFullYear()
					const monthDiff = today.getMonth() - birth.getMonth()

					if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
						age--
					}

					return age
				} catch (error) {
					return null
				}
			}
		}
	}
</script>

<style scoped>
.container {
	min-height: 100vh;
  background: linear-gradient(180deg, #f6fcfd 60%, #eafcff 100%);
	display: flex;
	flex-direction: column;
	align-items: center;
	padding-bottom: 40rpx;
}

.header-bg {
	width: 100%;
	background: linear-gradient(135deg, #6fd6e8 0%, #3ec6c6 100%);
	border-bottom-left-radius: 40rpx;
	border-bottom-right-radius: 40rpx;
	box-shadow: 0 8rpx 24rpx 0 rgba(62,198,198,0.10);
	padding-bottom: 20rpx;
}

.header {
	width: 100%;
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	padding: 48rpx 36rpx 0 36rpx;
	box-sizing: border-box;
}

.title {
  font-size: 44rpx;
	color: #fff;
  font-weight: 900;
	letter-spacing: 2rpx;
  text-shadow: 0 2rpx 8rpx rgba(62,198,198,0.18);
}

.history {
	font-size: 28rpx;
  color: #3ec6c6;
  background: #fff;
  padding: 8rpx 22rpx;
  border-radius: 22rpx;
  border: 1rpx solid #b2f0f0;
  box-shadow: 0 2rpx 8rpx 0 rgba(62,198,198,0.08);
  font-weight: bold;
}

.desc-card, .questions-card {
  width: 92%;
	background: #fff;
  border-radius: 32rpx;
  margin-top: 36rpx;
  padding: 38rpx 32rpx 28rpx 32rpx;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
  box-shadow: 0 8rpx 32rpx 0 rgba(62,198,198,0.10);
}

.questions-card {
  margin-top: 28rpx;
  margin-bottom: 24rpx;
  padding: 28rpx 32rpx 18rpx 32rpx;
}

.desc-title {
  font-size: 32rpx;
	font-weight: bold;
	color: #222;
  margin-bottom: 12rpx;
}

.desc-detail {
	font-size: 26rpx;
	color: #666;
}

.question {
	font-size: 28rpx;
	color: #222;
  margin-bottom: 16rpx;
  line-height: 1.7;
}

.highlight {
	color: #3ec6c6;
	font-weight: bold;
}

.bottom-action {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 350rpx;
}

.bottom-tip {
	width: 100%;
	text-align: center;
	color: #aaa;
  font-size: 26rpx;
  margin-bottom: 18rpx;
  letter-spacing: 1rpx;
}

.start-btn {
	width: 90%;
  height: 96rpx;
	background: linear-gradient(90deg, #6fd6e8 0%, #3ec6c6 100%);
	color: #fff;
  font-size: 36rpx;
  border-radius: 48rpx;
	font-weight: bold;
	border: none;
  box-shadow: 0 12rpx 32rpx 0 rgba(64, 196, 255, 0.18);
	letter-spacing: 2rpx;
  transition: box-shadow 0.2s, transform 0.1s;
  display: flex;
  align-items: center;
  justify-content: center;
}
.start-btn:active {
  box-shadow: 0 4rpx 12rpx 0 rgba(64, 196, 255, 0.10);
  transform: scale(0.97);
}
</style>
