<template>
  <view class="about-container">
    <view class="header-section text-center">
      <image style="width: 150rpx;height: 150rpx;" src="/static/logo200.png" mode="widthFix">
      </image>
      <uni-title type="h2" title="数智舌诊移动端"></uni-title>
    </view>

    <view class="content-section">
      <view class="menu-list">
        <view class="list-cell list-cell-arrow" @click="handleCheckUpdate">
          <view class="menu-item-box">
            <view>版本信息</view>
            <view class="text-right">v{{version}}</view>
          </view>
        </view>
        
        <!-- <view class="list-cell list-cell-arrow">
          <view class="menu-item-box">
            <view>官方邮箱</view>
            <view class="text-right"><EMAIL></view>
          </view>
        </view> -->
        <view class="list-cell list-cell-arrow" @click="makePhoneCall">
          <view class="menu-item-box">
            <view>服务热线</view>
            <view class="text-right">0546-8080588</view>
          </view>
        </view>
        <view class="list-cell list-cell-arrow">
          <view class="menu-item-box">
            <view>公司介绍</view>
            <view class="text-right">
              <uni-link :href="siteUrl" :text="siteUrl" showUnderLine="false"></uni-link>
            </view>
          </view>
        </view>
      </view>
    </view>

    <view class="copyright">
      <view>Copyright &copy; 2025 katlot.cn All Rights Reserved.</view>
    </view>

    <view class="policy-links">
      <text class="policy-text" @click="navigateToProtocol">《用户协议》</text>
      <text class="policy-text" @click="navigateToPrivacy">《隐私政策》</text>
    </view>
  </view>
</template>

<script>
import { checkForUpdates } from '@/utils/upversion';

export default {
  data() {
    return {
      siteUrl: getApp().globalData.config.appInfo.site_url,
      version: getApp().globalData.config.appInfo.version,
      isChecking: false // 防止重复点击
    }
  },
  methods: {
    makePhoneCall() {
      uni.makePhoneCall({
        phoneNumber: '0546-8080588' // 电话号码
      });
    },
    navigateToProtocol() {
      this.$tab.navigateTo('/pages/my/common/protocol/index');
    },
    navigateToPrivacy() {
      this.$tab.navigateTo('/pages/my/common/privacy/index');
    },
    async handleCheckUpdate() {
      if (this.isChecking) {
        return; // 防止重复点击
      }

      this.isChecking = true;

      try {
        console.log('关于我们页面 - 手动检查版本更新（强制刷新缓存）');

        uni.showLoading({
          title: '检查更新中...',
          mask: true
        });

        // 调用统一的版本检查函数，强制刷新缓存，显示"已是最新版本"提示
        await checkForUpdates(true, true);

      } catch (error) {
        console.error('版本检查失败:', error);
        uni.showToast({
          title: '检查更新失败',
          icon: 'none'
        });
      } finally {
        uni.hideLoading();
        this.isChecking = false;
      }
    }
  }
}
</script>

<style lang="scss">
  page {
    background-color: #f8f8f8;
  }

  .about-container {
    min-height: 100vh; /* 确保容器至少占满整个视口高度 */
    display: flex;
    flex-direction: column;
    padding-top: 100rpx;
    padding-bottom: 100rpx; /* 增加底部内边距，将内容向上推 */
  }

  .copyright {
    margin-top: auto; /* 将其推到底部 */
    text-align: center;
    line-height: 60rpx;
    color: #999;
  }

  .header-section {
    display: flex;
    padding: 30rpx 0 0;
    flex-direction: column;
    align-items: center;
  }

  .policy-links {
    display: flex;
    justify-content: center;
    margin-top: 40rpx;
  }

  .policy-text {
    color: #3ec6c6; /* 链接颜色 */
    font-size: 28rpx;
    padding: 0 15rpx;
  }
</style>
