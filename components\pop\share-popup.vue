<template>
  <uni-popup ref="popup" type="bottom" @change="change">
    <view class="share-popup" :style="{ paddingBottom: (safeBottom ? safeBottom + 'px' : 'env(safe-area-inset-bottom)') }">
      <view class="share-title">分享APP至</view>
      <view class="share-content">
        <view class="share-item" @click="handleShare('wechat')">
          <view class="iconfontA icon-weixin share-icon"></view>
          <text class="share-text">微信好友</text>
        </view>
        <view class="share-item" @click="handleShare('moments')">
          <view class="iconfontA icon-iconfontzhizuobiaozhunbduan36 share-icon"></view>
          <text class="share-text">朋友圈</text>
        </view>
        <view class="share-item" @click="handleShare('favorite')">
          <view class="iconfontA icon-weixinshoucang share-icon"></view>
          <text class="share-text">微信收藏</text>
        </view>
        <view class="share-item" @click="handleShare('copy')">
          <view class="iconfontA icon-lianjie share-icon"></view>
          <text class="share-text">复制链接</text>
        </view>
      </view>
    </view>
  </uni-popup>
</template>

<script>
export default {
  name: 'SharePopup',
  data() {
    return {
      shareUrl: '', // 分享链接
      shareTitle: 'KatGather', // 分享标题
      shareContent: '您的智能健康管理专家', // 分享描述
      safeBottom: 0
    }
  },
  mounted() {
    this.calcSafeBottom()
  },
  methods: {
    calcSafeBottom() {
      // #ifdef MP-WEIXIN || APP-PLUS
      const sys = uni.getSystemInfoSync()
      if (sys.safeArea) {
        this.safeBottom = sys.screenHeight - sys.safeArea.bottom
      } else {
        this.safeBottom = 0
      }
      // #endif
      // #ifdef H5
      this.safeBottom = 0
      // #endif
    },
    open() {
      this.calcSafeBottom()
      // 获取当前页面路径作为分享链接
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]
      // 使用小程序页面路径
      this.shareUrl = '/' + currentPage.route
      this.$refs.popup.open()
    },
    close() {
      this.$refs.popup.close()
    },
    change(e) {
      console.log('popup change', e)
    },
    handleShare(type) {
      // 关闭弹窗
      this.close()
      // #ifdef MP-WEIXIN
      if (type === 'wechat' || type === 'moments') {
        uni.showToast({
          title: '请点击右上角分享',
          icon: 'none'
        })
        return
      }
      // #endif
      // #ifdef APP-PLUS
      switch(type) {
        case 'wechat':
          uni.share({
            provider: 'weixin',
            scene: 'WXSceneSession',
            type: 0,
            title: this.shareTitle,
            summary: this.shareContent,
            href: this.shareUrl,
            success: (res) => {
              console.log('分享成功', res)
            },
            fail: (err) => {
              console.log('分享失败', err)
              uni.showToast({
                title: '请安装微信客户端',
                icon: 'none'
              })
            }
          })
          break
        case 'moments':
          uni.share({
            provider: 'weixin',
            scene: 'WXSceneTimeline',
            type: 0,
            title: this.shareTitle,
            summary: this.shareContent,
            href: this.shareUrl,
            success: (res) => {
              console.log('分享成功', res)
            },
            fail: (err) => {
              console.log('分享失败', err)
              uni.showToast({
                title: '请安装微信客户端',
                icon: 'none'
              })
            }
          })
          break
        case 'favorite':
          uni.share({
            provider: 'weixin',
            scene: 'WXSceneFavorite',
            type: 0,
            title: this.shareTitle,
            summary: this.shareContent,
            href: this.shareUrl,
            success: (res) => {
              console.log('分享成功', res)
            },
            fail: (err) => {
              console.log('分享失败', err)
              uni.showToast({
                title: '请安装微信客户端',
                icon: 'none'
              })
            }
          })
          break
      }
      // #endif
      if (type === 'copy') {
        uni.setClipboardData({
          data: this.shareUrl,
          success: () => {
            uni.showToast({
              title: '链接已复制',
              icon: 'none'
            })
          }
        })
      }
    }
  }
}
</script>

<style lang="scss">
.share-popup {
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;
  overflow-y: auto;

  .share-title {
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    font-size: 28rpx;
    color: #999;
  }
  
  .share-content {
    display: flex;
    padding: 30rpx 50rpx;
    justify-content: space-between;
    flex-wrap: wrap;
    
    .share-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 30rpx;
      
      .share-icon {
        font-size: 80rpx;
        margin-bottom: 20rpx;
      }
      
      .share-text {
        font-size: 24rpx;
        color: #333;
      }
    }
  }
}
</style> 