<template>
	<view class="diagnosis-container">
	  <!-- 顶部提示 -->
	  <view class="top-tips">
		<view class="title">舌诊拍摄注意事项</view>
		<view class="desc">
		  确保舌头完整位于取景框内，并在拍摄时注意以下三点：
		</view>
		<view class="tips-list">
		  <view class="tip-item">
			<image src="@/static/images/icons/light.png" class="tip-icon light" />
			<view>光线清晰自然</view>
		  </view>
		  <view class="tip-item">
			<image src="@/static/images/icons/clean.png" class="tip-icon" />
			<view>舌体无遮挡无异物</view>
		  </view>
		  <view class="tip-item">
			<image src="@/static/images/icons/face.png" class="tip-icon face" />
			<view>舌面平展放松</view>
		  </view>
		</view>
	  </view>
  
	  <!-- 拍照取景框 -->
	  <view class="camera-area">
		<view class="camera-frame-inner">
		  <!-- #ifndef APP-PLUS -->
		  <camera
			v-if="!photo && !uploadMode"
			:device-position="devicePosition"
			flash="off"
			:style="{ width: '340rpx', height: '340rpx' }"
			:zoom="6"
			ref="camera"
		  />
		  <!-- #endif -->
		  <!-- #ifdef APP-PLUS -->
		  <view v-if="!photo && !uploadMode" class="camera-placeholder" @click="takePhotoWithSystem">
			<view class="camera-icon">📷</view>
			<text class="camera-text">点击调用系统相机</text>
		  </view>
		  <!-- #endif -->
		  <image v-else :src="photo" class="photo-preview" />
		</view>
		<view class="zoom-tip" v-if="!photo">
		  <!-- #ifndef APP-PLUS -->
		  可用双指缩放取景画面
		  <!-- #endif -->
		  <!-- #ifdef APP-PLUS -->
		  点击上方区域调用系统相机拍照
		  <!-- #endif -->
		</view>

		<!-- 用户信息显示 - 移到取景画面下方 -->
		<view class="user-info-display" :class="{'incomplete-mode': !isUserInfoComplete()}">
		  <view class="user-info-text" v-if="isUserInfoComplete()">
			{{ userInfo.name }}，{{ getAge() }}岁
		  </view>
		  <view class="user-info-text incomplete" v-else>
			{{ getUserInfoTip() }}
		  </view>
		  <view class="edit-info-btn" @click="editUserInfo">
			用户信息
		  </view>
		</view>
	  </view>

	  <!-- 底部操作区 -->
	  <view class="bottom-bar">
		<view class="bottom-btn" @click="uploadPhoto">
		  <image src="@/static/images/icons/upload.png" class="bottom-icon" />
		  <view>上传舌头照片</view>
		</view>
		<view class="camera-btn" @click="takePhoto"></view>
		<!-- #ifndef APP-PLUS -->
		<view class="bottom-btn" @click="switchCamera">
		  <image src="@/static/images/icons/switch.png" class="bottom-icon switch-icon" />
		  <view>翻转摄像头</view>
		</view>
		<!-- #endif -->
		<!-- #ifdef APP-PLUS -->
		<view class="bottom-btn" @click="takePhoto">
		  <image src="@/static/images/icons/switch.png" class="bottom-icon switch-icon" />
		  <view>系统相机</view>
		</view>
		<!-- #endif -->
	  </view>
	</view>
  </template>
  
  <script>
  import config from '@/config'
  import { getToken, handleTokenExpired, getTokenExpiredHandlingStatus } from '@/utils/auth'
  import { submitComprehensiveDetection } from '@/api/system/comprehensive'
  import detectionManager from '@/utils/detectionManager'
  export default {
	data() {
	  return {
		gender: 'female',
		age: 23,
		photo: '',
		devicePosition: 'front', // 'front' or 'back'
		uploadMode: false, // true时只显示上传功能
		loading: false, // 新增加载状态
		analyzing: false, // 分析状态
		userInfo: {}, // 用户信息
		// 综合检测相关状态
		fromConstitution: false, // 是否来自体质检测页面
		isComprehensive: false, // 是否为综合检测模式
		pendingPhotoPath: null // 待处理的照片路径
	  }
	},

	onLoad(options) {
	  // 检查是否来自体质检测页面
	  if (options.from === 'constitution' && options.comprehensive === 'true') {
		this.fromConstitution = true
		this.isComprehensive = true
		console.log('从体质检测页面跳转而来，开启综合检测模式')
	  }

	  // 加载用户信息
	  this.loadUserInfo()

	  // 如果是综合检测模式，检查是否有体质检测数据
	  if (this.isComprehensive) {
		this.checkConstitutionData()
	  }
	},

	onShow() {
	  // 页面显示时清除照片数据
	  this.photo = ''
	  this.uploadMode = false
	  // 重新加载用户信息（可能在其他页面更新了）
	  this.loadUserInfo()
	},

	methods: {
	  uploadPhoto() {
		// 验证用户信息是否完整
		if (!this.validateUserInfo()) {
		  return
		}
		this.uploadMode = true;

		// 直接使用 uni.chooseImage，不使用 Promise 包装
		uni.chooseImage({
		  count: 1,
		  sourceType: ['album'], // 只允许从相册选择，不允许拍摄
		  sizeType: ['original', 'compressed'], // 可以选择原图或压缩图
		  success: (res) => {
			console.log('✅ 图片选择成功:', res)

			if (res.tempFilePaths && res.tempFilePaths.length > 0) {
			  this.photo = res.tempFilePaths[0]

			  // 选择照片成功后，检查是否需要询问综合检测
			  this.handlePhotoReady(res.tempFilePaths[0])

			  uni.showToast({
				title: '图片选择成功',
				icon: 'success'
			  })
			} else {
			  console.error('❌ 图片路径为空')
			  uni.showToast({
				title: '图片选择失败',
				icon: 'none'
			  })
			}
		  },
		  fail: (err) => {
			console.error('❌ 选择照片失败:', err)

			// 根据错误类型给出不同提示
			let errorMsg = '选择照片失败'
			if (err && err.errMsg) {
			  if (err.errMsg.includes('cancel')) {
				errorMsg = '已取消选择图片'
				console.log(' 用户取消选择')
				return // 用户取消不显示错误提示
			  } else if (err.errMsg.includes('permission')) {
				errorMsg = '请在设置中开启相册访问权限'
			  } else if (err.errMsg.includes('fail')) {
				errorMsg = '无法访问相册，请检查权限设置'
			  }
			}

			uni.showToast({
			  title: errorMsg,
			  icon: 'none',
			  duration: 2000
			})
		  }
		})
	  },
	  async takePhoto() {
		// 验证用户信息是否完整
		if (!this.validateUserInfo()) {
		  return
		}

		// #ifndef APP-PLUS
		// 非App端使用camera组件拍照
		const cameraContext = uni.createCameraContext()
		cameraContext.takePhoto({
		  quality: 'high',
		  success: (res) => {
			  console.log(res)
			this.photo = res.tempImagePath
			// 拍照成功后，检查是否需要询问综合检测
			this.handlePhotoReady(res.tempImagePath)
		  },
		  fail: (err) => {
			console.error('拍照失败:', err)
			uni.showToast({ title: '拍照失败，请检查摄像头权限', icon: 'none' })
		  }
		})
		// #endif

		// #ifdef APP-PLUS
		// App端调用系统相机
		this.takePhotoWithSystem()
		// #endif
	  },

	  // App端使用系统相机拍照
	  async takePhotoWithSystem() {
		// 验证用户信息是否完整
		if (!this.validateUserInfo()) {
		  return
		}

		try {
		  await uni.chooseImage({
			count: 1,
			sourceType: ['camera'], // 只允许拍照，不允许从相册选择
			sizeType: ['original'], // 使用原图
			success: (res) => {
			  console.log('系统相机拍照成功:', res)
			  this.photo = res.tempFilePaths[0]
			  // 拍照成功后，检查是否需要询问综合检测
			  this.handlePhotoReady(res.tempFilePaths[0])
			},
			fail: (err) => {
			  console.error('系统相机拍照失败:', err)
			  uni.showToast({
				title: '拍照失败，请检查摄像头权限',
				icon: 'none'
			  })
			}
		  })
		} catch (error) {
		  console.error('调用系统相机失败:', error)
		  uni.showToast({
			title: '无法调用相机，请检查权限设置',
			icon: 'none'
		  })
		}
	  },
	  switchCamera() {
		this.devicePosition = this.devicePosition === 'front' ? 'back' : 'front'
	  },

	  loadUserInfo() {
		// 优先从统一的用户信息存储读取
		let userInfo = uni.getStorageSync('userInfo')

		// 如果没有，则从旧的存储位置读取
		if (!userInfo || Object.keys(userInfo).length === 0) {
		  userInfo = uni.getStorageSync('User-Informations')
		}

		if (userInfo) {
		  this.userInfo = userInfo
		}
	  },

	  // 验证用户信息是否完整
	  validateUserInfo() {
		const userInfo = uni.getStorageSync('User-Informations')

		// 检查必填字段（职业和婚姻状况改为选填）
		const requiredFields = [
		  { key: 'name', name: '姓名' },
		  { key: 'sex', name: '性别' },
		  { key: 'dateBirth', name: '出生年月' },
		  { key: 'phonenumber', name: '手机号' },
		  { key: 'height', name: '身高' },
		  { key: 'weight', name: '体重' }
		]

		if (!userInfo) {
		  this.showCompleteInfoModal()
		  return false
		}

		// 检查每个必填字段
		for (const field of requiredFields) {
		  if (!userInfo[field.key] || userInfo[field.key] === '') {
			this.showCompleteInfoModal()
			return false
		  }
		}

		return true
	  },

	  // 显示完善信息弹窗
	  showCompleteInfoModal() {
		// 提示用户完善信息
		uni.showModal({
		  title: '提示',
		  content: '为了更好的识别，请完善信息',
		  confirmText: '去完善',
		  cancelText: '取消',
		  success: (res) => {
			if (res.confirm) {
			  uni.navigateTo({
				url: '/pages/my/healthInput/analyse?from=tongue'
			  })
			}
		  }
		})
	  },

	  editUserInfo() {
		// 跳转到用户信息页面
		uni.navigateTo({
		  url: '/pages/my/healthInput/analyse?from=tongue'
		})
	  },



	  // 获取用户信息提示文本
	  getUserInfoTip() {
		// 信息不完整时的提示
		return '请在拍摄前完善个人信息'
	  },

	  // 计算年龄
	  getAge() {
		if (!this.userInfo.dateBirth) return 0
		const birth = new Date(this.userInfo.dateBirth)
		const now = new Date()
		let age = now.getFullYear() - birth.getFullYear()
		const monthDiff = now.getMonth() - birth.getMonth()
		if (monthDiff < 0 || (monthDiff === 0 && now.getDate() < birth.getDate())) {
		  age--
		}
		return age
	  },

	  // 检查用户信息是否完整
	  isUserInfoComplete() {
		if (!this.userInfo) return false

		// 必填字段（职业和婚姻状况改为选填）
		const requiredFields = ['name', 'sex', 'dateBirth', 'phonenumber', 'height', 'weight']

		for (const field of requiredFields) {
		  if (!this.userInfo[field] || this.userInfo[field] === '') {
			return false
		  }
		}

		return true
	  },

	  // 开始分析舌象
	  async startAnalyze(photoPath) {
		this.analyzing = true

		// 显示分析中的弹窗
		uni.showLoading({
		  title: '分析中...',
		  mask: true
		})

		try {
		  // 获取用户信息
		  const userInfo = uni.getStorageSync('User-Informations') || {}

		  // 构建formData
		  const formData = {
			// 使用用户填写的真实信息
			TimeStamp: userInfo.TimeStamp || new Date().getTime(),
			person_name: userInfo.name,
			sex: userInfo.sex,
			dateBirth: userInfo.dateBirth,
			phone: userInfo.phonenumber,
			occupation: userInfo.occupation || '',
			maritalStatus: userInfo.maritalStatus || '',
			height: userInfo.height,
			weight: userInfo.weight,
			age: this.getAge(), // 使用计算方法获取年龄
			diastolicPressure: userInfo.diastolicPressure || '',
			systolicPressure: userInfo.systolicPressure || '',
			allergy: userInfo.allergy || '',
			medicalHistory: userInfo.medicalHistory || '',
			UserseeionType:3
		  }

		  console.log('=== 舌诊上传API数据详情 ===')
		  console.log('API地址:', config.baseUrl + '/Moudles/uploadAPP')
		  console.log('照片路径:', photoPath)
		  console.log('用户信息原始数据:', userInfo)
		  console.log('发送的formData:', formData)
		  console.log('formData字段详情:')
		  Object.keys(formData).forEach(key => {
			console.log(`  ${key}: ${formData[key]} (${typeof formData[key]})`)
		  })

		  // 直接使用 uni.uploadFile 进行文件上传
		  const uploadResult = await new Promise((resolve, reject) => {
			uni.uploadFile({
			  timeout: 20000,
			  url: config.baseUrl + '/Moudles/uploadAPP',
			  filePath: photoPath,
			  name: 'file',
			  header: {
				'Authorization': 'Bearer ' + getToken()
			  },
			  formData: formData,
			  success: (response) => {
				console.log("上传后返回的结果：", response);
				try {
				  const res = JSON.parse(response.data)
				  console.log("解析后的结果：", res);
				  console.log("res.code的类型和值：", typeof res.code, res.code);

				  // 优先检查token过期情况 - 使用宽松比较
				  console.log("开始检查401状态码，res.code:", res.code, "类型:", typeof res.code)
				  console.log("401比较结果:", res.code == 401, res.code === 401)

				  if (res.code == 401 || res.code === 401) {
					console.log("✅ 进入401处理分支，token过期")
					uni.hideLoading()

					// 检查是否已在处理token过期，避免重复弹窗
					if (!getTokenExpiredHandlingStatus()) {
					  console.log("调用统一的token过期处理方法")
					  handleTokenExpired()
					} else {
					  console.log("Token过期处理已在进行中，跳过重复处理")
					}

					// 不要立即reject，等用户点击对话框后再处理
					return
				  }

				  console.log("❌ 未进入401处理分支，继续后续检查")

				  // 检查是否是舌体检测失败的特殊情况
				  if (res.msg && (res.msg.includes('未检测到舌体') || res.msg.includes('检测失败'))) {
					reject(new Error('TONGUE_NOT_DETECTED'))
					return
				  }

				  if (res.code === 500) {
					reject(new Error(res.msg || '服务器内部错误'))
					return
				  }

				  if (res.code !== 200) {
					reject(new Error(res.msg || '请求失败'))
					return
				  }

				  if (!res.externalResponse) {
					reject(new Error('TONGUE_NOT_DETECTED'))
					return
				  }

				  const externalResponse = res.externalResponse
				  const hasValidTongueData = (externalResponse.tongue_color && externalResponse.tongue_color.trim() !== '') ||
											 (externalResponse.tongue_shape && externalResponse.tongue_shape.trim() !== '') ||
											 (externalResponse.fur_color && externalResponse.fur_color.trim() !== '') ||
											 (externalResponse.fur_shape && externalResponse.fur_shape.trim() !== '') ||
											 (externalResponse.type_name && externalResponse.type_name.trim() !== '')

				  if (!hasValidTongueData) {
					reject(new Error('TONGUE_NOT_DETECTED'))
					return
				  }

				  resolve(res)
				} catch (e) {
				  reject(new Error('解析响应数据失败'))
				}
			  },
			  fail: (error) => {
				console.log("上传失败：", error);
				reject(error)
			  }
			})
		  })

		  // 隐藏加载弹窗
		  uni.hideLoading()

		  // 分析成功，跳转到报告页面
		  const resultData = uploadResult.data || uploadResult;
		  console.log('后端返回的完整数据:', resultData);

		  uni.redirectTo({
			url: '/pages/gather/diagnosis/report?result=' + encodeURIComponent(JSON.stringify(resultData)) + '&photo=' + encodeURIComponent(photoPath)
		  })

		} catch (e) {
		  console.error('分析失败:', e)

		  // 隐藏加载弹窗
		  uni.hideLoading()

		  // 检查是否是舌体检测失败
		  if (e.message === 'TONGUE_NOT_DETECTED') {
			uni.showModal({
			  title: '分析失败',
			  content: '未检测到舌体，请重新拍摄或上传图片',
			  showCancel: false,
			  confirmText: '重新拍摄',
			  success: () => {
				// 清除当前照片，重新开始
				this.photo = ''
				this.uploadMode = false
			  }
			})
		  } else {
			uni.showModal({
			  title: '分析失败',
			  content: '网络错误或服务异常，请重试',
			  showCancel: false,
			  confirmText: '重新拍摄',
			  success: () => {
				// 清除当前照片，重新开始
				this.photo = ''
				this.uploadMode = false
			  }
			})
		  }
		} finally {
		  this.analyzing = false
		}
	  },
	  // 工具方法：将图片路径转为 File/Blob 以便 formData 上传
	  async getFile(path) {
		// #ifdef H5
		return path
		// #endif
		// #ifndef H5
		return await new Promise((resolve, reject) => {
		  uni.getFileSystemManager().readFile({
			filePath: path,
			encoding: 'base64',
			success: res => {
			  const file = wx.base64ToArrayBuffer(res.data)
			  resolve(file)
			},
			fail: reject
		  })
		})
		// #endif
	  },

	  // 检查体质检测数据
	  checkConstitutionData() {
		const constitutionData = detectionManager.getConstitutionData()
		if (!constitutionData) {
		  console.warn('综合检测模式但未找到体质检测数据')
		  this.isComprehensive = false
		  this.fromConstitution = false
		}
	  },

	  // 处理照片准备就绪
	  async handlePhotoReady(photoPath) {
		// 如果是从体质检测页面来的，直接进行综合检测
		if (this.fromConstitution) {
		  this.startComprehensiveAnalyze(photoPath)
		  return
		}

		// 检查是否有体质检测数据，如果有则询问是否进行综合检测
		const hasConstitutionData = detectionManager.hasConstitutionData()

		if (hasConstitutionData) {
		  // 有体质检测数据，询问是否进行综合检测
		  const result = await detectionManager.showComprehensiveDialog('constitution')

		  if (result.confirm) {
			// 用户选择进行综合检测，保存舌诊数据并跳转
			this.saveCurrentTongueData(photoPath)
			this.navigateToConstitution()
		  } else {
			// 用户选择单独舌诊
			this.startAnalyze(photoPath)
		  }
		} else {
		  // 没有体质检测数据，询问是否要做体质检测
		  const result = await detectionManager.showComprehensiveDialog('constitution')

		  if (result.confirm) {
			// 保存舌诊数据并跳转到体质检测
			this.saveCurrentTongueData(photoPath)
			this.navigateToConstitution()
		  } else {
			// 继续单独舌诊
			this.startAnalyze(photoPath)
		  }
		}
	  },

	  // 保存当前舌诊数据
	  saveCurrentTongueData(photoPath) {
		const userInfo = uni.getStorageSync('User-Informations') || {}

		const tongueData = {
		  photoPath: photoPath,
		  userInfo: userInfo,
		  timestamp: Date.now()
		}

		// 保存用户信息和舌诊数据
		detectionManager.saveUserInfo(userInfo)
		detectionManager.saveTongueData(tongueData)

		console.log('已保存舌诊数据:', tongueData)
	  },

	  // 跳转到体质检测页面
	  navigateToConstitution() {
		const url = detectionManager.generateNavigateUrl('constitution', 'tongue')
		uni.redirectTo({ url })
	  },

	  // 开始综合分析（当从体质检测页面跳转来时）
	  async startComprehensiveAnalyze(photoPath) {
		console.log('开始综合分析')

		// 保存当前舌诊数据
		this.saveCurrentTongueData(photoPath)

		// 调用综合检测接口
		await this.submitComprehensiveDetection()
	  },

	  // 提交综合检测数据
	  async submitComprehensiveDetection() {
		try {
		  uni.showLoading({
			title: '正在进行综合分析...',
			mask: true
		  })

		  // 获取综合检测数据
		  const comprehensiveData = detectionManager.getComprehensiveData()

		  if (!detectionManager.hasCompleteData()) {
			throw new Error('综合检测数据不完整')
		  }

		  // 构建发送给后端的数据
		  const requestData = {
			userInfo: comprehensiveData.userInfo,
			tongueData: comprehensiveData.tongueData,
			constitutionData: comprehensiveData.constitutionData
		  }

		  console.log('发送综合检测数据:', requestData)

		  // 调用综合检测接口
		  const response = await submitComprehensiveDetection(requestData)

		  uni.hideLoading()

		  // 处理综合检测结果
		  this.handleComprehensiveResult(response)

		} catch (error) {
		  uni.hideLoading()
		  console.error('综合检测失败:', error)

		  uni.showModal({
			title: '综合检测失败',
			content: error.message || '未能检测到舌体',
			confirmText: '重新拍摄',
			cancelText: '返回',
			showCancel: true,
			success: (res) => {
			  if (res.confirm) {
				// 重新拍摄：清除图像数据，留在当前页面
				this.handleRetakePhoto()
			  } else {
				// 返回：清除数据并回到首页
				this.handleReturn()
			  }
			}
		  })
		}
	  },

	  // 处理综合检测结果
	  handleComprehensiveResult(response) {
		console.log('综合检测结果:', response)

		// 获取原始的综合检测数据
		const comprehensiveData = detectionManager.getComprehensiveData()

		// 构建结果数据 - 包含API响应和原始数据
		const resultData = {
		  type: 'comprehensive',
		  data: response, // API响应数据
		  userInfo: comprehensiveData.userInfo, // 用户信息
		  tongueData: comprehensiveData.tongueData, // 舌诊数据
		  constitutionData: comprehensiveData.constitutionData, // 体质检测数据
		  tongueImageUrl: comprehensiveData.tongueData?.photoPath || '', // 直接传递舌诊图片URL
		  timestamp: Date.now()
		}

		console.log('传递给结果页面的数据:', resultData)

		// 清除临时数据
		detectionManager.clearData('all')

		// 跳转到综合结果页面
		uni.redirectTo({
		  url: '/pages/gather/comprehensive/result?result=' + encodeURIComponent(JSON.stringify(resultData))
		})
	  },

	  // 检查摄像头权限
	  checkCameraPermission() {
		return new Promise((resolve) => {
		  // #ifdef APP-PLUS
		  const main = plus.android.runtimeMainActivity();
		  const pkName = main.getPackageName();
		  const PermissionChecker = plus.android.importClass("android.support.v4.content.PermissionChecker");
		  const permission = PermissionChecker.checkSelfPermission(main, "android.permission.CAMERA");

		  if (permission !== PermissionChecker.PERMISSION_GRANTED) {
			uni.showModal({
			  title: '摄像头权限申请',
			  content: '需要摄像头权限来进行舌诊拍照，请在设置中开启',
			  confirmText: '去设置',
			  cancelText: '取消',
			  success: (res) => {
				if (res.confirm) {
				  const Intent = plus.android.importClass("android.content.Intent");
				  const Settings = plus.android.importClass("android.provider.Settings");
				  const Uri = plus.android.importClass("android.net.Uri");
				  const intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
				  const uri = Uri.fromParts("package", pkName, null);
				  intent.setData(uri);
				  main.startActivity(intent);
				}
				resolve(false);
			  }
			});
		  } else {
			resolve(true);
		  }
		  // #endif

		  // #ifndef APP-PLUS
		  resolve(true);
		  // #endif
		});
	  },

	  // 处理重新拍摄操作
	  handleRetakePhoto() {
		console.log('用户选择重新拍摄，清除图像数据')

		// 清除图像数据
		this.clearImageData()

		// 重置拍摄状态
		this.resetCameraState()
	  },

	  // 处理返回操作
	  handleReturn() {
		console.log('用户选择返回，清除数据并回到首页')

		// 清除所有舌诊数据
		this.clearAllTongueData()

		// 返回首页
		uni.switchTab({
		  url: '/pages/index'
		})
	  },

	  // 清除图像数据
	  clearImageData() {
		// 清除页面中的图片数据
		this.photo = ''
		this.uploadMode = false
		this.pendingPhotoPath = null

		// 清除本地存储的图片
		uni.removeStorageSync('capturedImage')
		uni.removeStorageSync('tongueAnalysisResult')

		console.log('图像数据已清除')
	  },

	  // 重置拍摄状态
	  resetCameraState() {
		// 重置分析状态
		this.analyzing = false
		this.loading = false

		// 重置上传模式
		this.uploadMode = false

		console.log('拍摄状态已重置')
	  },

	  // 清除所有舌诊数据
	  clearAllTongueData() {
		// 清除图像数据
		this.clearImageData()

		// 清除检测管理器中的舌诊数据
		detectionManager.clearData('tongue')

		// 清除其他相关的本地存储数据
		uni.removeStorageSync('tongueDetectionResult')

		console.log('所有舌诊数据已清除')
	  }
	},
	watch: {
	  devicePosition() {
		this.$forceUpdate()
	  }
	}


  }
  </script>
  
  <style scoped>
  .diagnosis-container {
	min-height: 100vh;
	background: #fff;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding-bottom: 160rpx;
  }
  .top-tips {
	margin-top: 48rpx;
	text-align: center;
	width: 100%;
  }
  .title {
	font-weight: bold;
	font-size: 38rpx;
	margin-bottom: 16rpx;
	color: #222;
  }
  .desc {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 32rpx;
	padding: 0 40rpx;
  }
  .tips-list {
	display: flex;
	justify-content: center;
	gap: 48rpx;
	margin-bottom: 40rpx;
  }
  .tip-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	width: 180rpx;
  }
  .tip-icon {
	width: 56rpx;
	height: 56rpx;
	margin-bottom: 10rpx;
	object-fit: contain;
	display: block;
  }
  .tip-icon.light {
	transform: scale(1.18);
  }
  .tip-icon.face {
	transform: scale(0.88);
  }
  .tip-item view {
	font-size: 22rpx;
	color: #444;
	text-align: center;
  }
  .camera-area {
	margin: 40rpx 0 32rpx 0;
	display: flex;
	flex-direction: column;
	align-items: center;
  }
  .camera-frame {
  width: 340rpx;
  height: 340rpx;
  border-radius: 50%;
  border: 4rpx solid #1976d2; /* 外圈实线描边 */
  box-shadow: 0 8rpx 32rpx rgba(72, 129, 194, 0.08);
  background: #f7f8fa;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
}
.camera-frame-inner {
  width: 320rpx;
  height: 320rpx;
  border-radius: 50%;
  border: 2rpx dashed #4ca1ff; /* 内圈虚线 */
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: #f7f8fa;
}
.photo-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
  .camera-frame-bordered::after {
	content: '';
	position: absolute;
	left: -10rpx;
	top: -10rpx;
	width: 360rpx;
	height: 360rpx;
	border-radius: 50%;
	border: 4rpx solid #1976d2;
	pointer-events: none;
  }
  .camera-placeholder {
	width: 100%;
	height: 100%;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s ease;
	border-radius: 50%;
  }

  .camera-placeholder:active {
	background: linear-gradient(135deg, #e8ebf0 0%, #b8c5d1 100%);
	transform: scale(0.95);
  }

  .camera-icon {
	font-size: 80rpx;
	margin-bottom: 20rpx;
	opacity: 0.8;
  }

  .camera-text {
	font-size: 24rpx;
	color: #666;
	text-align: center;
	line-height: 1.4;
  }
  .photo-preview {
	width: 100%;
	height: 100%;
	object-fit: cover;
  }
  .user-info {
	margin-top: 20rpx;
	display: flex;
	justify-content: center;
  }
  .gender-age {
	background: #e6f0ff;
	color: #4ca1ff;
	border-radius: 32rpx;
	padding: 10rpx 32rpx;
	font-size: 26rpx;
	display: inline-block;
	font-weight: 500;
  }
  .bottom-bar {
	position: fixed;
	bottom: 48rpx;
	left: 0;
	width: 100%;
	display: flex;
	justify-content: space-around;
	align-items: center;
	z-index: 10;
  }
  .bottom-btn {
	display: flex;
	flex-direction: column;
	align-items: center;
	color: #888;
	font-size: 24rpx;
	gap: 6rpx;
  }
  .bottom-icon {
	width: 56rpx;
	height: 56rpx;
	margin-bottom: 2rpx;
  }
  .camera-btn {
	width: 110rpx;
	height: 110rpx;
	background: linear-gradient(135deg, #4ca1ff 60%, #70e1f5 100%);
	border-radius: 50%;
	border: 10rpx solid #e6f0ff;
	box-shadow: 0 6rpx 24rpx rgba(76,161,255,0.18);
  }
  .switch-icon {
	width: 62rpx;
	height: 62rpx;
	margin-bottom: 2rpx;

  }
  .zoom-tip {
	margin-top: 18rpx;
	color: #888;
	font-size: 32rpx;
	text-align: center;
	font-weight: 500;
  }

  .user-info-display {
	margin-top: 32rpx;
	background: rgba(255, 255, 255, 0.98);
	border-radius: 16rpx;
	padding: 18rpx 28rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
	width: 400rpx;
	margin-left: auto;
	margin-right: auto;
  }

  .user-info-text {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
	margin-right: 18rpx;
  }
  .user-info-text.incomplete {
	color: #ff6b6b;
	font-weight: 400;
	max-width: 320rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
  }
  .user-info-display.incomplete-mode {
	width: 520rpx;
  }

  .edit-info-btn {
	background: #1976d2;
	color: #fff;
	padding: 10rpx 28rpx;
	border-radius: 24rpx;
	font-size: 24rpx;
	font-weight: 700;
	box-shadow: 0 2rpx 8rpx rgba(76,161,255,0.10);
	transition: background 0.2s;
	display: flex;
	align-items: center;
	justify-content: center;
	min-width: 80rpx;
	text-align: center;
  }
  </style>