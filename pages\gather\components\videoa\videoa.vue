<template>
	<view class="video-detail-container">

		<!-- 视频播放区域 -->
		<view class="video-wrapper">
			<video
				:src="videoData.videoUrl"
				:image="videoData.image"
				class="video-player"
				controls
				:show-fullscreen-btn="true"
				:show-play-btn="true"
				:show-center-play-btn="true"
				object-fit="cover"
			></video>
		</view>

		<!-- 视频信息区域 -->
		<view class="video-info">
			<!-- 标题 -->
			<view class="title-section">
				<text class="video-title">{{ videoData.title || '视频标题' }}</text>
			</view>

		

			<!-- 视频统计信息 -->
			<view class="stats-section">
				<view class="stat-item">
					<uni-icons type="eye" size="16" color="#999"></uni-icons>
					<text class="stat-text">{{ formatViews(videoData.views) }}</text>
				</view>
				
				<view class="stat-item">
					<uni-icons type="calendar" size="16" color="#999"></uni-icons>
					<text class="stat-text">{{ formatDate(videoData.time) }}</text>
				</view>
			</view>

			<!-- 简介区域 -->
			<view class="intro-section">
				<view class="section-title">
					<text class="section-label">视频简介</text>
				</view>
				<view class="intro-content">
					<text class="intro-text" v-if="videoData.text">
						{{ videoData.text }}
					</text>
					<text class="intro-placeholder" v-else>
						暂无简介内容
					</text>
				</view>
			</view>


		</view>
	</view>
</template>

<script>
	export default {
		name: 'VideoaComponent',
		props: {
			// 视频ID，从父组件传入
			videoId: {
				type: [String, Number],
				required: true
			},
			// 完整的课程数据，从父组件传入
			courseData: {
				type: Object,
				default: null
			}
		},
		data() {
			return {
				videoData: {
					videoUrl: '',
					image: '',
					title: '',
					text: '',
					views: 0,
					time: '',
				}
			}
		},
		watch: {
			videoId: {
				immediate: true,
				handler(newId) {
					console.log('videoa组件 - videoId变化:', newId);
					if (newId) {
						this.loadVideoData(newId);
					}
				}
			},
			courseData: {
				immediate: true,
				handler(newData) {
					console.log('videoa组件 - courseData变化:', newData);
					if (newData) {
						this.loadVideoDataFromProps(newData);
					}
				}
			}
		},
		methods: {
			// 从传递的props加载视频数据
			loadVideoDataFromProps(courseData) {
				console.log('videoa组件 - 从props加载数据:', courseData);

				// 使用传递过来的真实数据，完全匹配后端字段
				this.videoData = {
					// 视频播放相关 - 优先使用video字段，支持vedio拼写错误的兼容
					videoUrl: courseData.video || courseData.vedio || courseData.originalData?.video || courseData.originalData?.vedio,
					image: courseData.image || '/static/images/default-poster.jpg',
					title: (courseData.title || '未知标题').trim(), // 去除前后空格
					text: courseData.text || courseData.subtitle || '暂无简介',
					author: courseData.author || '未知作者',

					// 统计信息
					views: this.parseViews(courseData.views) || 0,
					time: courseData.time || courseData.date || '未知时间',
					duration: courseData.duration || courseData.time || '未知时长',
					free: courseData.free,
					top: courseData.top,

					// 保存完整的原始数据
					originalData: courseData
				};

				
			},

			// 根据视频ID加载视频数据（备用方法）
			async loadVideoData(videoId) {
				console.log('videoa组件 - 开始加载视频数据，videoId:', videoId);

				// 如果已经有courseData，优先使用
				if (this.courseData) {
					this.loadVideoDataFromProps(this.courseData);
					return;
				}

				
			},

			// 解析观看次数字符串为数字
			parseViews(viewsStr) {
				if (typeof viewsStr === 'number') return viewsStr;
				if (typeof viewsStr !== 'string') return 0;

				if (viewsStr.includes('万')) {
					return parseFloat(viewsStr.replace('万', '')) * 10000;
				}
				return parseInt(viewsStr) || 0;
			},

			
			



			// 返回上一页
			goBack() {
				uni.navigateBack();
			},

			// 格式化观看次数
			formatViews(views) {
				if (!views || views === 0) return '0次观看'

				const num = typeof views === 'string' ? parseInt(views) || 0 : views

				if (num >= 100000000) {
					return (num / 100000000).toFixed(1) + '亿次观看'
				} else if (num >= 10000) {
					return (num / 10000).toFixed(1) + '万次观看'
				} else if (num >= 1000) {
					return (num / 1000).toFixed(1) + 'k次观看'
				} else {
					return num + '次观看'
				}
			},

			// 格式化日期
			formatDate(dateString) {
				if (!dateString) return '';
				const date = new Date(dateString);
				const now = new Date();
				const diff = now - date;
				const days = Math.floor(diff / (1000 * 60 * 60 * 24));

				if (days === 0) return '今天';
				if (days === 1) return '昨天';
				if (days < 7) return `${days}天前`;
				if (days < 30) return `${Math.floor(days / 7)}周前`;
				if (days < 365) return `${Math.floor(days / 30)}个月前`;
				return `${Math.floor(days / 365)}年前`;
			}
		}
	}
</script>

<style lang="less" scoped>
/* 全局样式重置 */
page {
	height: 100%;
	background: #f8f9fa;
}

/* 确保父容器也铺满 */
.uni-page-body {
	height: 100%;
}
.video-detail-container {
	min-height: 100vh;
	height: 100vh;
	background: #f8f9fa;
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
	background-attachment: fixed;
	display: flex;
	flex-direction: column;
	position: relative;
}

/* 头部区域 */
.header {
	position: sticky;
	top: 0;
	z-index: 100;
	background: #fff;
	padding: 20rpx 24rpx;
	border-bottom: 1rpx solid #eee;
}

.back-btn {
	display: flex;
	align-items: center;
	width: fit-content;
}

.back-text {
	margin-left: 8rpx;
	font-size: 28rpx;
	color: #333;
}

/* 视频播放区域 */
.video-wrapper {
	width: 100%;
	background: #000;
	position: relative;
	flex-shrink: 0;
	/* 使用16:9宽高比 */
	aspect-ratio: 16/9;
}

.video-player {
	width: 100%;
	height: 100%;
	display: block;
}

/* 视频信息区域 */
.video-info {
	background: #fff;
	padding: 32rpx 24rpx;
	flex: 1;
	overflow-y: auto;
}

/* 标题区域 */
.title-section {
	margin-bottom: 24rpx;
}

.video-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
	line-height: 1.4;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
}

/* 作者信息区域 */
.author-section {
	margin-bottom: 20rpx;
	padding-bottom: 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.author-info {
	display: flex;
	align-items: center;
	gap: 10rpx;
}

.author-text {
	font-size: 28rpx;
	color: #666;
	margin-left: 8rpx;
}

.free-tag {
	background-color: #4CAF50;
	color: white;
	font-size: 20rpx;
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
	margin-left: 10rpx;
}

.top-tag {
	background-color: #FF9800;
	color: white;
	font-size: 20rpx;
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
	margin-left: 10rpx;
}

/* 统计信息区域 */
.stats-section {
	display: flex;
	align-items: center;
	margin-bottom: 32rpx;
	padding-bottom: 24rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.stat-item {
	display: flex;
	align-items: center;
	margin-right: 32rpx;
	background: rgba(0, 0, 0, 0.05);
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
}

.stat-item:first-child {
	background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
}

.stat-item:first-child .stat-text {
	color: white;
	font-weight: 500;
}

.stat-item:first-child uni-icons {
	color: white !important;
}

.stat-text {
	margin-left: 8rpx;
	font-size: 24rpx;
	color: #666;
	font-weight: 500;
}

/* 简介区域 */
.intro-section {
	margin-bottom: 32rpx;
}

.section-title {
	margin-bottom: 16rpx;
}

.section-label {
	font-size: 30rpx;
	font-weight: 600;
	color: #333;
}

.intro-content {
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 24rpx;
	border-left: 4rpx solid #3ec6c6;
}

.intro-text {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
}

.intro-placeholder {
	font-size: 28rpx;
	color: #ccc;
	font-style: italic;
}


</style>
