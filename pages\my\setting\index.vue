<template>
  <view class="setting-container" :style="{height: `${windowHeight}px`}">
    <view class="menu-list">
      <view class="list-cell list-cell-arrow" @click="handleToPwd">
        <view class="menu-item-box">
          <view class="iconfont icon-password menu-icon"></view>
          <view>修改密码</view>
        </view>
      </view>
      <view class="list-cell list-cell-arrow" @click="handleToUpgrade">
        <view class="menu-item-box">
          <view class="iconfont icon-refresh menu-icon"></view>
          <view>检查更新</view>
        </view>
      </view>

      <view class="list-cell list-cell-arrow" @click="handleDeepClean">
        <view class="menu-item-box">
          <view class="iconfont icon-clean menu-icon"></view>
          <view>清理缓存</view>
        </view>
      </view>

    </view>
    <view class="logout-button-wrapper">
      <button class="logout-button" @click="handleLogout">
        退出登录
      </button>
    </view>
  </view>
</template>

<script>
import { checkForUpdates } from '@/utils/upversion';

export default {
  data() {
    return {
      windowHeight: uni.getSystemInfoSync().windowHeight,
      isChecking: false // 防止重复点击
    }
  },
  methods: {
    handleToPwd() {
      this.$tab.navigateTo('/pages/my/pwd/index')
    },
    async handleToUpgrade() {
      if (this.isChecking) {
        return; // 防止重复点击
      }

      this.isChecking = true;

      try {
        console.log('应用设置页面 - 手动检查版本更新（强制刷新缓存）');

        uni.showLoading({
          title: '检查更新中...',
          mask: true
        });

        // 调用统一的版本检查函数，强制刷新缓存，显示"已是最新版本"提示
        await checkForUpdates(true, true);

      } catch (error) {
        console.error('版本检查失败:', error);
        uni.showToast({
          title: '检查更新失败',
          icon: 'none'
        });
      } finally {
        uni.hideLoading();
        this.isChecking = false;
      }
    },






    // 深度清理
    handleDeepClean() {
      this.performDeepClean();
    },



    // 执行深度清理
    performDeepClean() {
      uni.showLoading({
        title: '深度清理中...',
        mask: true
      });

      setTimeout(() => {
        try {
          console.log('--- 开始深度清理 --- ');
          let cleanedCount = 0;

          // 获取所有存储键
          const allKeys = uni.getStorageInfoSync().keys || [];

          // 深度清理：保留登录信息和用户检测信息
          const coreKeysToKeep = [
            'App-Token',
            'Kat-Gather-UserName',
            'Kat-Gather-PassWord',
            'userInfo',              // 用户基本信息
            'User-Informations',     // 用户健康信息
            'storage_data'           // Vuex store数据（包含用户状态）
          ];

          // 清理所有非核心数据
          allKeys.forEach(key => {
            if (!coreKeysToKeep.includes(key)) {
              uni.removeStorageSync(key);
              cleanedCount++;
              console.log(`深度清理: ${key}`);
            }
          });

          // H5 特殊处理
          // #ifdef H5
          try {
            if (typeof localStorage !== 'undefined') {
              const localStorageKeys = Object.keys(localStorage);
              localStorageKeys.forEach(key => {
                // 检查是否为需要保留的核心数据
                const shouldKeep = coreKeysToKeep.some(coreKey => key.includes(coreKey));
                if (!shouldKeep) {
                  localStorage.removeItem(key);
                  cleanedCount++;
                  console.log(`H5深度清理localStorage: ${key}`);
                }
              });
            }

            if (typeof sessionStorage !== 'undefined') {
              sessionStorage.clear();
              console.log('H5深度清理sessionStorage完成');
            }
          } catch (h5Error) {
            console.warn('H5 深度清理部分失败:', h5Error);
          }
          // #endif

          uni.hideLoading();
          this.$modal.showToast(`深度清理完成，共清理 ${cleanedCount} 项数据`);
          console.log(`深度清理完成: 共清理 ${cleanedCount} 项数据，保留了登录信息和用户检测信息`);

        } catch (error) {
          console.error('深度清理失败:', error);
          uni.hideLoading();
          this.$modal.showToast('深度清理失败，请重试');
        }
      }, 1000);
    },


    handleLogout() {
      this.$modal.confirm('确定退出登录吗？退出时将清理缓存数据，但保留用户名和密码以便下次快速登录。').then(() => {
        // 执行退出登录，清理缓存但保留用户名和密码
        this.performLogoutKeepCredentials();
      })
    },

    // 执行退出登录，清理缓存但保留用户名和密码
    performLogoutKeepCredentials() {
      uni.showLoading({
        title: '正在退出登录并清理缓存...',
        mask: true
      });

      setTimeout(() => {
        try {
          console.log('--- 开始退出登录（清理缓存但保留用户名密码） ---');
          let cleanedCount = 0;

          // 获取所有存储键
          const allKeys = uni.getStorageInfoSync().keys || [];

          // 需要保留的关键数据
          const keysToKeep = [
            'Kat-Gather-UserName',  // 用户名
            'Kat-Gather-PassWord',  // 密码
            'storage_data'          // Vuex store数据（部分保留）
          ];

          // 需要明确清理的业务数据
          const businessKeysToClean = [
            'cartItems',        // 购物车
            'orders',           // 订单
            'favoriteItems',    // 收藏
            'searchHistory',    // 搜索历史
            'addresses',        // 收货地址
            'Kat-Gather-Rule',  // 用户协议状态
            'userInfo',         // 用户信息
            'User-Informations' // 用户健康信息
          ];

          // 强制清理业务数据
          businessKeysToClean.forEach(key => {
            if (uni.getStorageSync(key)) {
              uni.removeStorageSync(key);
              cleanedCount++;
              console.log(`已清理业务数据: ${key}`);
            }
          });

          // 清理其他非关键数据，但保留用户名和密码
          allKeys.forEach(key => {
            if (!keysToKeep.includes(key) && !businessKeysToClean.includes(key)) {
              try {
                uni.removeStorageSync(key);
                cleanedCount++;
                console.log(`已清理缓存数据: ${key}`);
              } catch (error) {
                console.warn(`清理 ${key} 失败:`, error);
              }
            }
          });

          // 使用store action清除token和用户状态
          this.$store.dispatch('LogOutKeepCredentials').then(() => {
            uni.hideLoading();
            this.$modal.showToast(`退出成功，已清理 ${cleanedCount} 项缓存数据`);
            console.log(`退出登录完成，用户名和密码已保留，共清理 ${cleanedCount} 项数据`);

            // 延迟跳转到登录页面
            setTimeout(() => {
              this.$tab.reLaunch('/pages/login');
            }, 2000);
          }).catch(error => {
            console.error('退出登录失败:', error);
            uni.hideLoading();
            this.$modal.showToast('退出失败，请重试');
          });

        } catch (error) {
          console.error('退出登录处理失败:', error);
          uni.hideLoading();
          this.$modal.showToast('退出失败，请重试');
        }
      }, 1000);
    },
    onClickLeft() {
      uni.navigateBack();
    }
  }
}
</script>

<style lang="scss" scoped>
  page {
    background-color: #f8f8f8;
  }

  .setting-container {
    min-height: 100vh;
    background-color: #f8f8f8;
    padding-top: 20rpx; /* 顶部留白 */
  }

  .menu-list {
    background-color: white;
    border-radius: 15px;
    box-shadow: 0 4px 10px rgba(0,0,0,0.05);
    overflow: hidden;
    margin: 30rpx;
    margin-bottom: 80rpx; /* 调整与退出登录按钮的间距 */
  }

  .list-cell {
    display: flex;
    align-items: center;
    padding: 25rpx 30rpx;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease-in-out;
    
    &:last-child {
      border-bottom: none;
    }

    &:active {
      background-color: #f8f8f8;
    }

    .menu-item-box {
      display: flex;
      align-items: center;
      flex: 1;
      font-size: 32rpx;
      color: #333;
    }

    .menu-icon {
      font-size: 40rpx; /* 增大图标 */
      margin-right: 20rpx;
      color: #3ec6c6; /* 使用主题色 */
    }
  }

  .logout-button-wrapper {
    margin: 50rpx 30rpx;
  }

  .logout-button {
    background-color: #f06a6a; /* 更醒目的红色 */
    color: #fff;
    border-radius: 15px;
    font-size: 30rpx;
    padding: 20rpx 0;
    box-shadow: 0 5px 15px rgba(240, 106, 106, 0.3);
    transition: all 0.3s ease;
  }

  .logout-button:active {
    transform: translateY(2px);
  }

  .text-right {
    color: #666;
    font-size: 28rpx;
  }
</style>

