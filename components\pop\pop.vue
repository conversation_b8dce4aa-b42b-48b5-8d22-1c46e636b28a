<template>
	<view>
		<u-popup :show="show" :round="25" mode="center" @close="close" @open="open" :customStyle="{overflow: 'hidden'}" closeable :closeOnClickOverlay="false">
            <view class="pop" >
                <view style="flex:1;width:100%;text-align: center;line-height: 60px;font-size: 20px;background: rgb(66, 185, 131);color:#fff">更新包下载中</view>
				<view style="flex:3;margin: 30px; margin-top: 30px;">
					<u-line-progress :percentage="progress" activeColor="#2979ff" height="25"></u-line-progress>
					<view class="">正在下载：{{ totalBytesWritten }}MB/{{ totalBytesExpectedToWrite }}MB</view>
				</view>
            </view>
		</u-popup>
		<!-- <u-button @click="checkvs()">打开</u-button> -->
	</view>
</template>
<script>
	import { checkForUpdates } from '@/utils/upversion.js'
	import { checkLogin } from '@/plugins/auth'
	export default {
      data() {
        return {
          show: false,
		  tempFilePath:'',
		  progress: 0,
		  totalBytesWritten:0,
		  totalBytesExpectedToWrite:0,
		  downloadTask: null, // 存储下载任务引用

        }
      },
	  mounted() {
		if (checkLogin()) {
			console.log('用户已登录，开始自动检查版本更新...');
			this.checkVersionUpdate();
		} else {
			console.log('用户未登录，跳过版本更新检查。');
		}

		// 监听全局下载事件
		uni.$on('startDownloadWithProgress', this.handleDownloadEvent);
	  },

	  beforeDestroy() {
		// 移除事件监听
		uni.$off('startDownloadWithProgress', this.handleDownloadEvent);

		// 如果有正在进行的下载任务，取消它
		if (this.downloadTask) {
			this.downloadTask.abort();
			this.downloadTask = null;
		}
	  },
      methods: {
		/**
		 * 检查版本更新
		 */
		async checkVersionUpdate() {
			try {
				console.log('Pop组件开始检查版本更新...');
				await checkForUpdates(false); // 不显示"已是最新版本"提示
			} catch (error) {
				console.error('Pop组件版本检查失败:', error);
			}
		},

        open() {
			this.show = true;
          console.log('下载进度弹窗打开');
        },
        close() {
		  // 只有用户主动点击关闭按钮时才询问是否确认取消
		  if (this.downloadTask) {
			  uni.showModal({
				  title: '确认取消',
				  content: '是否取消当前下载？',
				  cancelText: '取消',
				  confirmText: '确定',
				  success: (res) => {
					  if (res.confirm) {
						  // 用户点击"确定"：取消下载并关闭弹窗
						  console.log('用户确认取消下载');
						  this.downloadTask.abort();
						  this.downloadTask = null;
						  this.resetProgress();
						  this.show = false;

						  // 不显示额外的失败提示，用户已经知道取消了下载
					  } else {
						  // 用户点击"取消"：继续下载，保持弹窗显示
						  console.log('用户选择继续下载');
						  // 不做任何操作，保持下载继续进行
					  }
				  }
			  });
		  } else {
			  // 没有下载任务时直接关闭弹窗
			  this.show = false;
			  console.log('下载进度弹窗关闭');
		  }
        },

		/**
		 * 直接关闭弹窗，不询问用户确认
		 * 用于下载完成、下载失败等自动关闭的场景
		 */
		closeDirectly() {
			this.show = false;
			console.log('下载进度弹窗直接关闭');
		},

		/**
		 * 重置下载进度
		 */
		resetProgress() {
			this.progress = 0;
			this.totalBytesWritten = 0;
			this.totalBytesExpectedToWrite = 0;
		},

		/**
		 * 处理全局下载事件
		 */
		handleDownloadEvent(data) {
			console.log('Pop组件接收到下载事件:', data);

			// 设置标记表示已处理
			uni.setStorageSync('pop_download_handled', true);

			// 开始下载
			this.startDownloadWithProgress(data.newVersion, data.downloadUrl);
		},

		/**
		 * 启动带进度显示的下载
		 * 这个方法可以被外部调用来显示下载进度
		 */
		startDownloadWithProgress(newVersion, downloadUrl) {
			console.log('开始带进度的下载:', downloadUrl);

			this.resetProgress();
			this.open(); // 显示进度弹窗

			this.downloadTask = uni.downloadFile({
				url: downloadUrl,
				success: (res) => {
					if (res.statusCode === 200) {
						console.log("下载完成");
						this.closeDirectly(); // 下载成功时直接关闭，不询问用户
						this.showInstallDialog(newVersion, res.tempFilePath);
					} else {
						this.closeDirectly(); // 下载失败时直接关闭，不询问用户
						uni.showModal({
							title: '下载失败',
							content: `下载失败，状态码: ${res.statusCode}`,
							showCancel: false
						});
					}
					this.downloadTask = null;
				},
				fail: (err) => {
					console.error('下载失败:', err);
					this.closeDirectly(); // 下载出错时直接关闭，不询问用户
					this.downloadTask = null;

					// 分析错误类型并提供相应的解决方案
					let errorMessage = '更新包下载失败';
					let solutions = [];

					if (err.errMsg) {
						if (err.errMsg.includes('network')) {
							errorMessage = '网络连接异常';
							solutions = ['检查网络连接', '切换到WiFi网络', '稍后重试'];
						} else if (err.errMsg.includes('timeout')) {
							errorMessage = '下载超时';
							solutions = ['网络较慢，建议稍后重试', '切换到更稳定的网络'];
						} else if (err.errMsg.includes('space')) {
							errorMessage = '存储空间不足';
							solutions = ['清理手机存储空间', '删除不必要的文件'];
						}
					}

					const content = solutions.length > 0
						? `${errorMessage}\n\n建议：\n${solutions.join('\n')}`
						: `${errorMessage}\n\n请检查网络连接或稍后重试。`;

					uni.showModal({
						title: '下载失败',
						content: content,
						confirmText: '重试',
						cancelText: '取消',
						success: (res) => {
							if (res.confirm) {
								// 延迟重试，给用户时间处理问题
								setTimeout(() => {
									this.startDownloadWithProgress(newVersion, downloadUrl);
								}, 1000);
							}
						}
					});
				}
			});

			// 监听下载进度
			this.downloadTask.onProgressUpdate((res) => {
				this.progress = res.progress || 0;
				this.totalBytesWritten = (res.totalBytesWritten / 1024 / 1024).toFixed(2);
				this.totalBytesExpectedToWrite = (res.totalBytesExpectedToWrite / 1024 / 1024).toFixed(2);

				console.log(`下载进度: ${this.progress}%, ${this.totalBytesWritten}MB/${this.totalBytesExpectedToWrite}MB`);
			});
		},

		/**
		 * 显示安装确认对话框
		 */
		showInstallDialog(newVersion, tempFilePath) {
			// 记录安装状态
			uni.setStorageSync('update_pending', {
				version: newVersion,
				timestamp: Date.now(),
				status: 'ready_to_install'
			});

			uni.showModal({
				title: '安装提示',
				content: `数智舌诊 ${newVersion} 安装包下载完成！\n\n点击"立即安装"将跳转到系统安装界面\n\n安装完成后请手动重新打开应用`,
				cancelText: '稍后安装',
				confirmText: '立即安装',
				success: (res) => {
					if (res.confirm) {
						this.installUpdate(newVersion, tempFilePath);
					}
				}
			});
		},

		/**
		 * 安装更新包
		 */
		installUpdate(newVersion, tempFilePath) {
			console.log('开始安装更新包:', tempFilePath);

			// #ifdef APP-PLUS
			// 更新安装状态
			uni.setStorageSync('update_pending', {
				version: newVersion,
				timestamp: Date.now(),
				status: 'installing'
			});

			plus.runtime.install(tempFilePath, {
				force: false // 让用户确认安装
			}, () => {
				// 这个回调可能不会执行
				console.log('安装成功回调执行');
				try {
					uni.setStorageSync('update_pending', {
						version: newVersion,
						timestamp: Date.now(),
						status: 'installed'
					});

					// uni.showToast({
					// 	title: '安装完成',
					// 	icon: 'success'
					// });

					setTimeout(() => {
						plus.runtime.restart();
					}, 1000);
				} catch(e) {
					console.log('安装成功回调处理失败:', e);
				}
			}, (error) => {
				console.error('安装失败:', error);
				// 清除更新状态
				uni.removeStorageSync('update_pending');

				uni.showModal({
					title: '安装失败',
					content: error.message || '安装过程中发生错误，请重试。',
					showCancel: false
				});
			});
			// #endif

			// #ifndef APP-PLUS
			uni.showModal({
				title: '提示',
				content: '当前环境不支持自动安装，请手动安装下载的安装包。',
				showCancel: false
			});
			// #endif
		}
      }
	}
</script>
<style lang="scss" scoped>
	.pop{
		width: 300px;
		height:180px;
		display: flex;
		flex-direction: column;
		// border-radius: 25px;
		overflow: hidden;
	}
	
</style>