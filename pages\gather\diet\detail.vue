<template>
	<view class="diet-detail-page">
		<!-- 使用自定义药膳详情组件 -->
		<dieta :dietId="dietId" :dietData="dietData"></dieta>
	</view>
</template>

<script>
	import dieta from '../components/dieta/dieta.vue'
	
	export default {
		components: {
			dieta
		},
		data() {
			return {
				dietId: '',
				dietData: null
			}
		},
		onLoad(options) {
			console.log('药膳详情页面onLoad，接收到的参数:', options);
			// 从页面参数获取药膳ID
			if (options.id) {
				this.dietId = options.id;
				console.log('药膳详情页面加载，药膳ID:', this.dietId);

				// 尝试从本地存储获取药膳数据
				try {
					const storedData = uni.getStorageSync('currentDietData');
					if (storedData && storedData.id == options.id) {
						this.dietData = storedData;
						console.log('从本地存储获取到药膳数据:', this.dietData);
						// 清除存储的数据
						uni.removeStorageSync('currentDietData');
					}
				} catch (error) {
					console.error('获取本地存储数据失败:', error);
				}
			} else {
				console.error('未获取到药膳ID，options:', options);
				uni.showToast({
					title: '药膳ID缺失',
					icon: 'none'
				});
			}
		},
		onShow() {
			// 页面显示时的逻辑
			console.log('药膳详情页面显示');
		}
	}
</script>

<style lang="scss">
.diet-detail-page {
	width: 100%;
	min-height: 100vh;
	background: #f8f9fa;
}
</style>
