import upload from '@/utils/upload'
import request from '@/utils/request'

// 修改用户个人信息
export function updateUserProfile(data) {
  return request({
    url: '/system/user/profile',
    method: 'put',
    data: data
  })
}

// 用户头像上传
export function uploadAvatar(data) {
  return upload({
    url: '/system/user/profile/avatar',
    name: data.name,
    filePath: data.filePath
  })
}

// 用户头像上传
export function uploadImg(data) {
  return upload({
    url: '/common/upload',
	method: 'post',
    filePath: data
  })
}
