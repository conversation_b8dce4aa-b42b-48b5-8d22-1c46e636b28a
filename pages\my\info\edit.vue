<template>
  <view class="container">
    <view class="example">
      <uni-forms ref="form" :model="user" labelWidth="80px">
        <uni-forms-item label="用户昵称" name="nickName">
          <uni-easyinput v-model="user.nickName" @input="handleNickNameInput" @blur="validateNickName" placeholder="请输入昵称" />
        </uni-forms-item>
        <uni-forms-item label="手机号码" name="phonenumber">
          <uni-easyinput v-model="user.phonenumber" @input="handlePhoneInput" placeholder="请输入手机号码"  type="number"/>
        </uni-forms-item>
        <uni-forms-item label="邮箱" name="email">
          <uni-easyinput v-model="user.email" placeholder="请输入邮箱" />
        </uni-forms-item>
   
      </uni-forms>
      <button type="primary" @click="submit">提交</button>
    </view>
  </view>
</template>

<script>
  import { getUserProfile } from "@/api/system/user"
  import { updateUserProfile } from "@/api/system/user"

  export default {
    data() {
      return {
        user: {
          nickName: "",
          phonenumber: "",
          email: ""
        },
        originalUser: {}, // 保存原始用户数据，用于比较
       
        rules: {
          nickName: {
            rules: [{
              required: true,
              errorMessage: '用户昵称不能为空'
            }, {
              minLength: 2,
              errorMessage: '用户昵称不能少于2位字符'
            }]
          },
          phonenumber: {
            rules: [{
              required: true,
              errorMessage: '手机号码不能为空'
            }, {
              pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
              errorMessage: '请输入正确的手机号码'
            }]
          },
          email: {
            rules: [{
              required: false, // 邮箱改为非必填项
              format: 'email',
              errorMessage: '请输入正确的邮箱地址'
            }]
          }
        }
      }
    },
    onLoad() {
      this.getUser()
    },
    onReady() {
      this.$refs.form.setRules(this.rules)
    },
    methods: {
      // 处理昵称输入
      handleNickNameInput(value) {
        this.user.nickName = value;
      },

      // 验证昵称
      validateNickName() {
        const nickName = this.user.nickName;
        if (!nickName) {
          uni.showToast({
            title: '用户昵称不能为空',
            icon: 'none',
            duration: 2000
          });
          return false;
        } else if (nickName.length < 2) {
          uni.showToast({
            title: '用户昵称不能少于2位字符',
            icon: 'none',
            duration: 2000
          });
          return false;
        }
        return true;
      },

      // 处理手机号输入，自动去除空格
      handlePhoneInput(value) {
        // uni-easyinput 组件直接传递字符串值，不是事件对象
        const cleanValue = value.replace(/\s/g, '');
        this.user.phonenumber = cleanValue;
      },
      getUser() {
        getUserProfile().then(response => {
          this.user = response.data
          // 保存原始数据的深拷贝，用于比较
          this.originalUser = JSON.parse(JSON.stringify(response.data))
        })
      },
      // 检查数据是否有变化
      hasDataChanged() {
        const fieldsToCompare = ['nickName', 'phonenumber', 'email']

        for (const field of fieldsToCompare) {
          const currentValue = this.user[field] || ''
          const originalValue = this.originalUser[field] || ''
          if (currentValue !== originalValue) {
            return true
          }
        }
        return false
      },

      submit() {
        // 先检查数据是否有变化
        if (!this.hasDataChanged()) {
          uni.showToast({
            title: '数据未发生变更',
            icon: 'none',
            duration: 2000
          })
          return
        }

        // 先进行自定义验证
        if (!this.validateNickName()) {
          return
        }

        // 表单验证
        this.$refs.form.validate().then(() => {
          uni.showLoading({
            title: '保存中...'
          })

          updateUserProfile(this.user).then(() => {
            uni.hideLoading()
            uni.showToast({
              title: '修改成功',
              icon: 'success',
              duration: 1500
            })

            // 更新store中的用户信息，确保昵称等信息同步
            this.$store.dispatch('GetInfo').then(() => {
              console.log('用户信息已更新到store')
            }).catch(error => {
              console.warn('更新store用户信息失败:', error)
            })

            // 延迟跳转到我的页面
            setTimeout(() => {
              uni.switchTab({
                url: '/pages/mine/index'
              })
            }, 1500)
          }).catch(error => {
            uni.hideLoading()
            console.error('更新用户信息失败:', error)
            uni.showToast({
              title: '修改失败，请重试',
              icon: 'none',
              duration: 2000
            })
          })
        }).catch(error => {
          console.log('表单验证失败:', error)
        })
      }
    }
  }
</script>

<style lang="scss">
  page {
    background-color: #ffffff;
  }

  .example {
    padding: 15px;
    background-color: #fff;
  }

  .segmented-control {
    margin-bottom: 15px;
  }

  .button-group {
    margin-top: 15px;
    display: flex;
    justify-content: space-around;
  }

  .form-item {
    display: flex;
    align-items: center;
    flex: 1;
  }

  .button {
    display: flex;
    align-items: center;
    height: 35px;
    line-height: 35px;
    margin-left: 10px;
  }
</style>
