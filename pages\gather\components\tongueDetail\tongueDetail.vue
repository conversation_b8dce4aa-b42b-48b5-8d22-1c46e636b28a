<template>
	<view class="tongue-detail-container" :style="{ height: contentHeight + 'px' }">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		<!-- 头部栏 -->
		<view class="header" :style="{ top: statusBarHeight + 'px'}">
			<view class="header-content">
				<view class="back-btn" @click="closePopup">
					<uni-icons type="back" size="20" color="#3ec6c6"></uni-icons>
				</view>
				<view class="header-title">
					<text class="title-text">{{ tongueData.name || '舌象详情' }}</text>
				</view>
				<view class="placeholder-btn"></view>
			</view>
		</view>
		<!-- 内容区域 -->
		<scroll-view 
			scroll-y 
			class="detail-content" 
			:style="{
				top: (statusBarHeight + headerHeight) + 'px',
				height: 'calc(' + contentHeight + 'px - ' + (statusBarHeight + headerHeight) + 'px)'
			}"
		>
			<view v-if="tongueData.id" class="content-wrapper">
				<!-- 舌象特征 -->
				<view class="info-section">
					<view class="section-title">
						<uni-icons type="info" size="16" color="#3ec6c6"></uni-icons>
						<text>舌象特征</text>
					</view>
					<view class="section-content">
						<view class="content-text" v-html="formatTextWithReferences(tongueData.characteristics)"></view>
					</view>
				</view>
				<!-- 临床意义 -->
				<view class="info-section">
					<view class="section-title">
						<uni-icons type="heart" size="16" color="#3ec6c6"></uni-icons>
						<text>临床意义</text>
					</view>
					<view class="section-content">
						<view class="content-text" v-html="formatTextWithReferences(tongueData.clinicalSignificance)"></view>
					</view>
				</view>
				<!-- 机理分析 -->
				<view class="info-section" v-if="tongueData.mechanism">
					<view class="section-title">
						<uni-icons type="gear" size="16" color="#3ec6c6"></uni-icons>
						<text>机理分析</text>
					</view>
					<view class="section-content">
						<view class="content-text" v-html="formatTextWithReferences(tongueData.mechanism)"></view>
					</view>
				</view>
				<!-- 主病 -->
				<view class="info-section">
					<view class="section-title">
						<uni-icons type="flag" size="16" color="#3ec6c6"></uni-icons>
						<text>主病</text>
					</view>
					<view class="section-content">
						<view class="disease-text" v-html="formatTextWithReferences(tongueData.mainDisease)"></view>
					</view>
				</view>
				<!-- 参考书籍 -->
				<view class="info-section">
					<view class="section-title">
						<uni-icons type="book" size="16" color="#3ec6c6"></uni-icons>
						<text>参考书籍</text>
					</view>
					<view class="section-content">
						<view class="reference-list" v-if="referencesInfo && referencesInfo.length > 0">
							<view class="reference-item" v-for="(book, index) in referencesInfo" :key="index">
								<text class="reference-number">[{{index + 1}}]</text>
								<text class="reference-title">{{book}}</text>
							</view>
						</view>
						<view v-else class="no-references">
							<text class="no-references-text">暂无参考书籍信息</text>
						</view>
					</view>
				</view>
			</view>
			<!-- 无数据提示 -->
			<view v-else class="no-data">
				<text class="no-data-text">暂无详细信息</text>
			</view>
		</scroll-view>
	</view>
</template>

<script>
export default {
	name: 'TongueDetail',
	props: {
		visible: {
			type: Boolean,
			default: false
		},
		tongueData: {
			type: Object,
			default: () => ({})
		}
	},
	data() {
		return {
			statusBarHeight: 20, // px
			headerHeight: 44, // px - 头部高度
			contentHeight: 0 // px
		}
	},
	computed: {
		// 处理参考书籍信息
		referencesInfo() {
			const referencesinfo = this.tongueData.referencesinfo
			console.log('原始参考书籍数据:', referencesinfo, '类型:', typeof referencesinfo)

			// 如果referencesinfo是字符串，尝试解析
			if (typeof referencesinfo === 'string') {
				if (!referencesinfo.trim()) {
					return []
				}

				try {
					// 尝试JSON解析
					const parsed = JSON.parse(referencesinfo)
					console.log('JSON解析成功:', parsed)
					return Array.isArray(parsed) ? parsed.filter(item => item && item.trim()) : [parsed]
				} catch (e) {
					// 如果不是JSON，按分隔符分割
					const split = referencesinfo.split(/[,，;；\n]/).filter(item => item.trim())
					console.log('字符串分割结果:', split)
					return split
				}
			}

			// 如果是数组，直接返回
			if (Array.isArray(referencesinfo)) {
				const filtered = referencesinfo.filter(item => item && item.trim())
				console.log('数组过滤结果:', filtered)
				return filtered
			}

			// 其他情况返回空数组
			console.log('返回空数组')
			return []
		}
	},
	mounted() {
		// 获取状态栏高度和窗口高度
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight;
		this.contentHeight = systemInfo.windowHeight;
		
	},
	methods: {
		// 格式化文本，处理角标和换行
		formatTextWithReferences(text) {
			if (!text) return ''

			// 处理换行符
			let formattedText = text.replace(/\n/g, '<br>')

			// 处理角标 [1], [2], [3] 等
			formattedText = formattedText.replace(/\[(\d+)\]/g, '<sup class="reference">[$1]</sup>')

			return formattedText
		},

		// 关闭弹窗
		closePopup() {
			this.$emit('close')
		},

		// 点击遮罩关闭
		handleMaskClick() {
			this.closePopup()
		}
	}
}
</script>

<style lang="scss" scoped>
.tongue-detail-container {
	position: fixed;
	top: 0; left: 0; right: 0; bottom: 0;
	z-index: 9999;
	background: linear-gradient(180deg, #f8f9fc 60%, #e6f7fa 100%);
	padding-bottom: env(safe-area-inset-bottom);
}

.status-bar {
	width: 100%;
	background: #fff;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 100;
}

.header {
	position: fixed;
	left: 0;
	right: 0;
	height: 44px;
	background: #fff;
	z-index: 100;
	box-shadow: 0 2px 8px -2px rgba(0,0,0,0.08);
}

.header-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 44px;
	padding: 0 16px;
}

.header-title {
	font-size: 16px;
	font-weight: 700;
	color: #1a3c4e;
	flex: 1;
	text-align: center;
	letter-spacing: 1px;
}

.title-text {
	font-size: 16px;
	font-weight: 700;
	color: #1a3c4e;
}

.back-btn {
	width: 32px;
	height: 32px;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50%;
	background: #f0f6f8;
	transition: background 0.2s;
	&:active {
		background: #d0eaf2;
	}
}

.placeholder-btn {
	width: 32px;
	height: 32px;
}

.detail-content {
	position: absolute;
	left: 0;
	right: 0;
	padding: 40rpx 30rpx 30rpx 30rpx;
	box-sizing: border-box;
	background: transparent;
	overflow-y: auto;
}

.content-wrapper {
	padding-top: 20rpx;
}

.info-section {
	margin-bottom: 40rpx;
}

.section-title {
	display: flex;
	align-items: center;
	margin-bottom: 18rpx;
	gap: 12rpx;
	text {
		font-size: 30rpx;
		font-weight: 700;
		color: #1a3c4e;
		margin-left: 10rpx;
	}
	.uni-icons {
		background: #e6f7fa;
		border-radius: 50%;
		padding: 6rpx;
	}
}

.section-content {
	background: linear-gradient(135deg, #f8f9fa 80%, #e6f7fa 100%);
	border-radius: 18rpx;
	padding: 28rpx 24rpx;
	box-shadow: 0 2px 8px rgba(62,198,198,0.04);
}

.content-text {
	font-size: 27rpx;
	color: #444;
	line-height: 2;
	letter-spacing: 0.5px;
}

.disease-text {
	font-size: 30rpx;
	color: #e74c3c;
	font-weight: 700;
	line-height: 1.7;
	background: #fff0f0;
	border-radius: 8rpx;
	padding: 8rpx 16rpx;
	display: inline-block;
}

.reference-list {
	display: flex;
	flex-direction: column;
	gap: 12rpx;
}

.reference-item {
	display: flex;
	align-items: flex-start;
	gap: 8rpx;
	border-bottom: 1px dashed #e6f7fa;
	padding-bottom: 6rpx;
	&:last-child {
		border-bottom: none;
	}
}

.reference-number {
	font-size: 24rpx;
	color: #3ec6c6;
	font-weight: 700;
	min-width: 40rpx;
}

.reference-title {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
	flex: 1;
}

.no-references {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx 20rpx;
}

.no-references-text {
	font-size: 26rpx;
	color: #999;
	font-style: italic;
}

.no-data {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 220rpx;
	flex-direction: column;
}
.no-data-text {
	font-size: 28rpx;
	color: #bbb;
	margin-top: 12rpx;
}
.no-data::before {
	content: '';
	display: block;
	width: 80rpx;
	height: 80rpx;
	background: url('/static/images/icons/face.png') no-repeat center/contain;
	opacity: 0.3;
}

/* 角标样式 */
:deep(.reference) {
	font-size: 18rpx;
	color: #3ec6c6;
	font-weight: 700;
	vertical-align: super;
	line-height: 1;
	cursor: pointer;
	transition: color 0.2s;
	&:hover {
		color: #1abc9c;
		text-decoration: underline;
	}
}
</style>