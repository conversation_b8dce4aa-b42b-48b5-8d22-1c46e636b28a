<template>
	<view class="diet-page">
		<!-- 搜索栏 -->
		<view class="search-section">
			<view class="search-box">
				<uni-icons type="search" size="18" color="#999"></uni-icons>
				<input class="search-input" type="text" placeholder="请输入药膳名称" v-model="searchKeyword" @input="handleSearch" />
			</view>
		</view>

		<!-- 药膳列表 -->
		<scroll-view
			class="diet-list"
			scroll-y="true"
			@scrolltolower="loadMore"
			:style="{ height: scrollHeight + 'px' }"
			refresher-enabled
			:refresher-triggered="isRefreshing"
			@refresherrefresh="onRefresh"
		>
			<view
				class="diet-item"
				v-for="(item, index) in dietList"
				:key="item.id"
				@click="goToDetail(item)"
			>
				<view class="diet-content">
					<view class="diet-title" v-html="highlightText(item.name)"></view>
					<view class="diet-effect" v-html="highlightText(item.effect)"></view>
				</view>
				<view class="diet-arrow">
					<uni-icons type="right" size="16" color="#ccc"></uni-icons>
				</view>
			</view>

			<!-- 空状态 -->
			<view class="empty-state" v-if="!isLoading && dietList.length === 0">
				<uni-icons type="info" size="48" color="#ccc"></uni-icons>
				<text v-if="searchKeyword.trim()">未找到相关药膳</text>
				<text v-else>暂无药膳数据</text>
			</view>

			<!-- 搜索结果统计 -->
			<view class="search-result" v-if="searchKeyword.trim() && dietList.length > 0">
				<text>找到 {{ dietList.length }} 个相关药膳</text>
			</view>

			<!-- 没有更多药膳提示 -->
			<view class="no-more" v-if="!isLoading && dietList.length > 0 && !searchKeyword.trim()">
				<text>没有更多药膳了</text>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	import { getDietListWithPagination } from '@/api/system/diet.js'

	export default {
		data() {
			return {
				searchKeyword: '',
				loading: false,
				isLoading: false,
				isRefreshing: false,
				hasMore: true,
				page: 1,
				pageSize: 10,
				total: 0,
				scrollHeight: 0,
				dietList: [], // 当前显示的药膳列表
				allDietList: [], // 所有药膳数据（用于搜索）
				searchTimer: null, // 搜索防抖定时器
				isFromCache: false, // 标记数据是否来自缓存
				lastUpdateTime: null // 最后更新时间
			}
		},
		onLoad() {
			// 计算滚动区域高度
			const systemInfo = uni.getSystemInfoSync();
			// 减去搜索栏高度(约100rpx)
			this.scrollHeight = systemInfo.windowHeight - uni.upx2px(100);

			this.loadDietList()
		},
		methods: {
			// 加载药膳列表
			async loadDietList(isRefresh = false) {
				if (this.isLoading && !isRefresh) return;

				try {
					this.isLoading = true;

					// 1. 优先从缓存加载数据（立即显示）
					if (!isRefresh) {
						const cacheResult = this.loadFromCache();
						if (cacheResult && cacheResult.data && cacheResult.data.length > 0) {
							this.allDietList = cacheResult.data;
							this.isFromCache = true;
							this.lastUpdateTime = cacheResult.timestamp;
							this.performSearch();
							console.log('从缓存加载药膳数据:', cacheResult.data.length, '条');

							// 检查缓存是否需要更新
							if (!this.shouldUpdateCache()) {
								this.isLoading = false;
								return;
							}

							// 后台静默更新
							console.log('后台更新药膳数据...');
						}
					}

					// 显示加载提示（仅首次加载或强制刷新）
					if (!this.allDietList.length || isRefresh) {
						uni.showLoading({ title: '加载中...' });
					}

					// 如果已有全部数据且不是刷新，直接进行搜索过滤
					if (this.allDietList.length > 0 && !isRefresh && !this.shouldUpdateCache()) {
						this.performSearch();
						return;
					}

					// 2. 检查网络状态
					const networkType = await this.checkNetworkStatus();
					if (!networkType || networkType === 'none') {
						// 离线状态，只能使用缓存
						if (this.allDietList.length === 0) {
							uni.showToast({
								title: '网络不可用，请检查网络连接',
								icon: 'none',
								duration: 3000
							});
						}
						this.isLoading = false;
						return;
					}

					// 获取所有数据（使用较大的pageSize获取全部数据）
					const params = {
						pageNum: 1,
						pageSize: 100 // 获取大量数据
					};

					const response = await getDietListWithPagination(params);
					console.log('药膳API响应:', response);

					// 处理API返回的数据
					let dataList = [];

					if (response) {
						// 尝试不同的数据结构
						if (response.rows && Array.isArray(response.rows)) {
							dataList = response.rows;
						} else if (response.data && Array.isArray(response.data)) {
							dataList = response.data;
						} else if (Array.isArray(response)) {
							dataList = response;
						} else if (response.list && Array.isArray(response.list)) {
							dataList = response.list;
						}
					}

					console.log('提取的数据列表:', dataList);

					if (dataList && dataList.length > 0) {
						// 处理API返回的数据格式
						this.allDietList = dataList.map((item, index) => ({
							id: item.id || index + 1,
							name: item.name || '未知药膳',
							ingredients: item.ingredients || '暂无配料信息',
							method: item.method || '暂无制作方法',
							effect: item.effect || '暂无功效说明'
						}));

						console.log('处理后的全部药膳数据:', this.allDietList);

						// 保存到缓存
						this.saveToCache(this.allDietList);

						// 更新状态
						this.isFromCache = false;
						this.lastUpdateTime = Date.now();

						// 执行搜索过滤
						this.performSearch();
					} else {
						this.allDietList = [];
						this.dietList = [];
					}
				} catch (error) {
					console.error('加载药膳列表失败:', error);
					uni.showToast({
						title: '加载失败',
						icon: 'none'
					});

					this.allDietList = [];
					this.dietList = [];
				} finally {
					this.isLoading = false;
					uni.hideLoading();
				}
			},

			// 执行搜索过滤（只搜索药膳名称）
			performSearch() {
				const keyword = this.searchKeyword.trim();

				if (!keyword) {
					// 如果没有搜索关键词，显示所有数据
					this.dietList = [...this.allDietList];
				} else {
					// 只根据药膳名称进行搜索过滤
					const lowerKeyword = keyword.toLowerCase();
					this.dietList = this.allDietList.filter(item => {
						// 只搜索药膳名称
						return item.name && item.name.toLowerCase().includes(lowerKeyword);
					});
				}

				console.log('搜索关键词:', keyword);
				console.log('搜索结果:', this.dietList);
				console.log('搜索结果数量:', this.dietList.length);
			},

			// 搜索处理（带防抖）
			handleSearch() {
				// 清除之前的定时器
				if (this.searchTimer) {
					clearTimeout(this.searchTimer);
				}

				// 设置新的定时器，300ms后执行搜索
				this.searchTimer = setTimeout(() => {
					if (this.allDietList.length > 0) {
						// 如果已有数据，直接进行前端搜索
						this.performSearch();
					} else {
						// 如果没有数据，先加载数据
						this.loadDietList();
					}
				}, 300);
			},

			// 加载更多（前端搜索不需要分页，保留方法避免报错）
			async loadMore() {
				// 前端搜索模式下不需要加载更多
				return;
			},

			// 下拉刷新
			async onRefresh() {
				this.isRefreshing = true;
				await this.loadDietList(true);
				this.isRefreshing = false;
			},

			// 高亮搜索关键词
			highlightText(text) {
				if (!text) return '';

				const keyword = this.searchKeyword.trim();
				if (!keyword) return text;

				// 使用正则表达式进行不区分大小写的替换
				const regex = new RegExp(`(${keyword})`, 'gi');
				return text.replace(regex, '<span style="color: #3ec6c6; font-weight: bold; background: rgba(62, 198, 198, 0.1); padding: 2rpx 4rpx; border-radius: 4rpx;">$1</span>');
			},

			goToDetail(dietItem) {
				console.log('跳转到药膳详情，数据:', dietItem);
				// 将药膳数据存储到全局状态或本地存储
				uni.setStorageSync('currentDietData', dietItem);
				uni.navigateTo({
					url: `/pages/gather/diet/detail?id=${dietItem.id}`
				});
			},

			// 缓存相关方法
			loadFromCache() {
				try {
					const cached = uni.getStorageSync('dietListCache');
					if (cached && cached.data && Array.isArray(cached.data)) {
						console.log('从缓存加载药膳列表:', cached.data.length, '条');
						return {
							data: cached.data,
							timestamp: cached.timestamp,
							version: cached.version
						};
					}
				} catch (error) {
					console.warn('读取药膳缓存失败:', error);
				}
				return null;
			},

			saveToCache(data) {
				try {
					const cacheData = {
						data: data,
						timestamp: Date.now(),
						version: '1.0'
					};
					uni.setStorageSync('dietListCache', cacheData);
					console.log('药膳列表已保存到缓存:', data.length, '条');
				} catch (error) {
					console.warn('保存药膳缓存失败:', error);
				}
			},

			shouldUpdateCache() {
				try {
					const cached = uni.getStorageSync('dietListCache');
					if (!cached || !cached.timestamp) {
						return true; // 没有缓存，需要更新
					}

					// 缓存超过24小时需要更新
					const cacheAge = Date.now() - cached.timestamp;
					const maxAge = 24* 60 * 60 * 1000; // 24小时

					return cacheAge > maxAge;
				} catch (error) {
					console.warn('检查缓存时效失败:', error);
					return true;
				}
			},

			clearCache() {
				try {
					uni.removeStorageSync('dietListCache');
					console.log('药膳缓存已清理');
				} catch (error) {
					console.warn('清理药膳缓存失败:', error);
				}
			},

			// 检查网络状态
			async checkNetworkStatus() {
				return new Promise((resolve) => {
					uni.getNetworkType({
						success: (res) => {
							console.log('当前网络状态:', res.networkType);
							resolve(res.networkType);
						},
						fail: () => {
							console.warn('获取网络状态失败');
							resolve('none');
						}
					});
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	.diet-page {
		background-color: #f8f9fa;
		min-height: 100vh;
	}

	.search-section {
		background: #fff;
		padding: 20rpx 30rpx;
		// border-bottom: 1rpx solid #eee;
	}

	.search-box {
		display: flex;
		align-items: center;
		background: #f5f5f5;
		border-radius: 25rpx;
		padding: 15rpx 20rpx;
	}

	.search-input {
		flex: 1;
		margin-left: 15rpx;
		font-size: 28rpx;
		color: #333;
	}

	.diet-list {
		padding: 20rpx 0;
		flex: 1;
	}

	.diet-item {
		background: #fff;
		margin: 0 30rpx 20rpx;
		border-radius: 15rpx;
		padding: 30rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
		transition: all 0.3s ease;
		border-left: 6rpx solid #3ec6c6;
	}

	.diet-item:active {
		transform: scale(0.98);
		background: #f8f9fa;
	}

	.diet-content {
		flex: 1;
	}

	.diet-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 10rpx;
	}

	.diet-effect {
		font-size: 26rpx;
		color: #3ec6c6;
		line-height: 1.4;
		font-weight: 500;
	}

	.diet-arrow {
		margin-left: 20rpx;
	}

	/* 分页相关样式 */
	.loading-more {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 30rpx;
		color: #999;
		font-size: 26rpx;
	}

	.loading-more text {
		margin-left: 10rpx;
	}

	.no-more {
		text-align: center;
		padding: 30rpx;
		color: #999;
		font-size: 26rpx;
	}

	.empty-state {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 100rpx 30rpx;
		color: #999;
		font-size: 28rpx;
	}

	.empty-state text {
		margin-top: 20rpx;
	}

	.search-result {
		text-align: center;
		padding: 20rpx 30rpx;
		color: #666;
		font-size: 26rpx;
		background: rgba(62, 198, 198, 0.1);
		margin: 0 30rpx 20rpx;
		border-radius: 10rpx;
	}

	.no-more {
		text-align: center;
		padding: 30rpx;
		color: #999;
		font-size: 26rpx;
		background: #f8f9fa;
		margin: 20rpx 30rpx;
		border-radius: 10rpx;
	
	}
</style>
