import Vue from 'vue';
import App from './App';
import store from './store';
import plugins from './plugins';
import uView from 'uview-ui';
import 'whatwg-fetch';
import { checkLogin } from './plugins/auth';

console.log('main.js loaded');

Vue.use(plugins);
Vue.use(uView);


//全局的分享功能
import share from '@/utils/share.js'
Vue.mixin(share)


uni.$u.config.unit = 'rpx';
uni.$u.setConfig({
	config: {
		unit: 'rpx'
	},
	props: {
		radio: {
			size: 15
		}
	}
});

Vue.config.productionTip = false;
Vue.prototype.$store = store;

App.mpType = 'app';

uni.addInterceptor('navigateTo', {
	invoke(args) {
		const url = args.url;
		try {
			const isLoggedIn = checkLogin();
			console.log('Is logged in (checkLogin):', isLoggedIn);

			// Define routes that require authentication
			const protectedRoutes = [
				'/pages/my/info/edit',
				'/pages/my/setting/index',
				'/pages/gather/healthy/healthy',
				'/pages/gather/video/video',
				'/pages/gather/diagnosis/diagnosis',
				'/pages/heart/heart/heart',
				// 商城服务区域需要登录的功能
				'/pages/shopping/cart/cart',
				'/pages/shopping/order/order',
				'/pages/shopping/appraise/photo',
				'/pages/shopping/appraise/appraise',
				'/pages/shopping/review/center',
				'/pages/shopping/review/my-reviews',
				'/pages/shopping/address/address',
				'/pages/shopping/favorite/favorite',
				'/pages/shopping/logistics/logistics'
			];

			const requiresAuth = protectedRoutes.some(route => url.startsWith(route));

			if (requiresAuth && !isLoggedIn) {
				console.log('Protected route accessed without login. Showing login modal.');
				uni.showModal({
					title: '提示',
					content: '您还未登录，请先登录。',
					confirmText: '去登录',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							uni.redirectTo({
								url: '/pages/login'
							});
						} else if (res.cancel) {
							// 用户点击取消，可以不做任何操作或返回上一页
						}
					}
				});
				return false; // Prevent navigation
			}
		} catch (error) {
			console.error('Error in navigateTo interceptor:', error);
			// 如果检查登录状态出错，允许导航继续
			return true;
		}
	},
	success(args) {
		console.log('navigateTo success', args)
	},
	fail(err) {
		console.error('navigateTo failed:', err)
	}
});

uni.addInterceptor('redirectTo', {
	invoke(args) {
		const url = args.url;
		// console.log('Redirecting to:', url);
		console.log('Is logged in (checkLogin):', checkLogin());
		const protectedRoutes = [
			'/pages/my/info/edit',
			'/pages/my/setting/index',
			'/pages/gather/healthy/healthy',
			'/pages/gather/video/video',
			'/pages/gather/diagnosis/diagnosis',
			'/pages/heart/heart/heart',
			// 商城服务区域需要登录的功能
			'/pages/shopping/cart/cart',
			'/pages/shopping/order/order',
			'/pages/shopping/appraise/photo',
			'/pages/shopping/appraise/appraise',
			'/pages/shopping/review/center',
			'/pages/shopping/review/my-reviews',
			'/pages/shopping/address/address',
			'/pages/shopping/favorite/favorite',
			'/pages/shopping/logistics/logistics'
		];
		const requiresAuth = protectedRoutes.some(route => url.startsWith(route));

		if (requiresAuth && !checkLogin()) {
			// console.log('Protected route accessed without login. Redirecting to login.');
			// uni.showToast({
			//   title: '请先登录',
			//   icon: 'none',
			//   duration: 1500
			// });
			// uni.redirectTo({
			//   url: '/pages/login'
			// });
			uni.showModal({
				title: '提示',
				content: '您还未登录，请先登录。',
				confirmText: '去登录',
				cancelText: '取消',
				success: (res) => {
					if (res.confirm) {
						uni.redirectTo({
							url: '/pages/login'
						});
					} else if (res.cancel) {
						// 用户点击取消，可以不做任何操作
					}
				}
			});
			return false; // Prevent navigation
		}
	},
	success(args) {},
	fail(err) {}
});

uni.addInterceptor('switchTab', {
	invoke(args) {
		const url = args.url;
		console.log('Switching tab to:', url);
		console.log('Is logged in (checkLogin):', checkLogin());
		const protectedRoutes = []; 
		const requiresAuth = protectedRoutes.some(route => url.startsWith(route));

		if (requiresAuth && !checkLogin()) {
			console.log('Protected tab accessed without login. Redirecting to login.');
			uni.showToast({
			  title: '请先登录',
			  icon: 'none',
			  duration: 1500
			});
			uni.switchTab({
			  url: '/pages/index'
			});
			uni.redirectTo({
			  url: '/pages/login'
			});
			// uni.showModal({
			// 	title: '提示',
			// 	content: '您还未登录，请先登录。',
			// 	confirmText: '去登录',
			// 	cancelText: '取消',
			// 	success: (res) => {
			// 		if (res.confirm) {
			// 			uni.redirectTo({
			// 				url: '/pages/login'
			// 			});
			// 		} else if (res.cancel) {
			// 			// 用户点击取消，可以返回首页或不做操作
			// 			uni.switchTab({
			// 				url: '/pages/index'
			// 			});
			// 		}
			// 	}
			// });
			return false; // Prevent navigation
		}
	},
	success(args) {},
	fail(err) {}
});

const app = new Vue({
	...App
});

app.$mount();