<template>
	<view class="detail-page">
		<!-- 舌象详情组件 -->
		<tongue-detail
			:visible="true"
			:tongue-data="tongueData"
			@close="handleClose"
		></tongue-detail>
	</view>
</template>

<script>
import TongueDetail from '../components/tongueDetail/tongueDetail.vue'

export default {
	components: {
		TongueDetail
	},
	data() {
		return {
			tongueData: {}
		}
	},
	onLoad(options) {
		// 从路由参数获取数据
		if (options.data) {
			try {
				this.tongueData = JSON.parse(decodeURIComponent(options.data))
			} catch (e) {
				console.error('解析舌象数据失败:', e)
				this.tongueData = {
					id: options.id || '',
					name: '舌象详情',
					type: options.type || ''
				}
			}
		} else {
			// 如果没有传递数据，可以根据 id 和 type 从本地数据中查找
			this.loadTongueDataById(options.id, options.type)
		}
	},
	methods: {
		// 根据 id 和 type 加载舌象数据
		loadTongueDataById(id, type) {
			// 这里可以根据 id 和 type 从本地数据或接口获取详细数据
			// 暂时设置默认数据
			this.tongueData = {
				id: id || '',
				name: '舌象详情',
				type: type || '',
				characteristics: '暂无数据',
				clinicalSignificance: '暂无数据',
				mechanism: '暂无数据',
				mainDisease: '暂无数据'
			}
		},

		// 处理关闭事件
		handleClose() {
			// 返回上一页
			uni.navigateBack()
		}
	}
}
</script>

<style lang="scss" scoped>
.detail-page {
	width: 100%;
	height: 100vh;
}
</style>