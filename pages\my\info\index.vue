<template>
  <view class="container">
    <uni-list>
      <uni-list-item showExtraIcon="true" :extraIcon="{type: 'person-filled', color: '#3ec6c6'}" title="昵称" :rightText="user.nickName" />
      <uni-list-item showExtraIcon="true" :extraIcon="{type: 'auth-filled', color: '#3ec6c6'}" title="用户账号" :rightText="user.userName" />
      <uni-list-item showExtraIcon="true" :extraIcon="{type: 'phone-filled', color: '#3ec6c6'}" title="手机号码" :rightText="user.phonenumber" />
      <uni-list-item showExtraIcon="true" :extraIcon="{type: 'email-filled', color: '#3ec6c6'}" title="邮箱" :rightText="user.email" />
      <uni-list-item showExtraIcon="true" :extraIcon="{type: 'staff-filled', color: '#3ec6c6'}" title="角色" :rightText="roleGroup" />
      <uni-list-item showExtraIcon="true" :extraIcon="{type: 'calendar-filled', color: '#3ec6c6'}" title="创建日期" :rightText="user.createTime" />
    </uni-list>
  </view>
</template>

<script>
  import { getUserProfile } from "@/api/system/user"

  export default {
    data() {
      return {
        user: {},
        roleGroup: "",
      }
    },
    onLoad() {
      this.getUser()
    },
    methods: {
      getUser() {
        getUserProfile().then(response => {
          console.log(response)
          this.user = response.data
          this.roleGroup = response.roleGroup
        })
      }
    }
  }
</script>

<style lang="scss">
  page {
    background-color: #f8f8f8; /* 柔和的页面背景色 */
  }

  .container {
    padding: 30rpx; /* 整体内边距 */
  }

  .uni-list-item {
    height: auto; /* 根据内容自适应高度 */
    min-height: 120rpx; /* 最小高度 */
    font-size: 32rpx;
    background-color: #ffffff; /* 干净的白色背景 */
    margin-bottom: 20rpx; /* 列表项间距 */
    border-radius: 20rpx; /* 更大的圆角 */
    box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.08); /* 优雅的阴影效果 */
    padding: 20rpx 30rpx; /* 列表项内部内边距 */
    display: flex; /* 使用 flex 布局 */
    align-items: center; /* 垂直居中 */
  }

  .uni-list-item__content-title {
    font-size: 34rpx !important; /* 标题字体大小 */
    color: #333333; /* 深色文字 */
    font-weight: 500; /* 标题加粗 */
    flex: 1; /* 占据剩余空间 */
  }

  .uni-list-item__extra-text {
    font-size: 30rpx !important; /* 信息字体大小 */
    color: #444444; /* 稍浅的文字颜色 */
    line-height: inherit; /* 继承父元素行高 */
  }

  /* 确保图标颜色生效 */
  .uni-list-item__extra-icon {
    margin-right: 20rpx; /* 图标与文字间距 */
    display: flex;
    align-items: center;
  }
</style>
