<template>
	<view class="report-container">
		<!-- 头部区域 -->
		<view class="header-section">
			<view class="report-title">心理健康评测报告</view>
			<view class="test-date" v-if="testTime">测试时间：{{ formatDate(testTime) }}</view>

			<view class="report-subtitle">本报告基于抑郁自评量表(SDS)，仅供参考，如有疑虑请咨询专业医师。</view>
		</view>

		<!-- 加载状态 -->
		<view v-if="loading" class="loading">
			<view class="loading-icon"></view>
			<view class="loading-text">分析中...</view>
		</view>

		<!-- 报告内容 -->
		<view v-else class="content-section">
			<!-- 总分显示 -->
			<view class="score-section">
				<view class="section-title">
					<view class="section-bar"></view>
					<view class="section-text">评测结果</view>
				</view>
				<view class="score-card">
					<view class="score-display">
						<view class="score-number">{{ totalScore }}</view>
						<view class="score-label">总分</view>
					</view>
					<view class="score-info">
						<!-- <view class="severity-index">严重指数：{{ severityIndex.toFixed(2) }}</view> -->
						<view class="level-result" :class="levelClass">{{ level }}</view>
					</view>
				</view>
			</view>

			<!-- 结果解读 -->
			<view class="interpretation-section">
				<view class="section-title">
					<view class="section-bar"></view>
					<view class="section-text">结果解读</view>
				</view>
				<view class="interpretation-content">
					<view class="level-description">
						<text class="description-text">{{ getLevelDescription() }}</text>
					</view>
					<view class="score-range">
						<text class="range-text">评分范围说明：</text>
						<view class="range-list">
							<view class="range-item">
								<view class="range-score">20-39分</view>
								<view class="range-desc">无抑郁</view>
							</view>
							<view class="range-item">
								<view class="range-score">40-47分</view>
								<view class="range-desc">轻微至轻度抑郁</view>
							</view>
							<view class="range-item">
								<view class="range-score">48-55分</view>
								<view class="range-desc">中至重度抑郁</view>
							</view>
							<view class="range-item">
								<view class="range-score">56-80分</view>
								<view class="range-desc">重度抑郁</view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 免责声明 -->
			<view class="disclaimer-section">
				<view class="disclaimer-title">
					<view class="disclaimer-bar"></view>
					免责声明
				</view>
				<view class="disclaimer-content">
					<text class="disclaimer-text">
						本评测结果仅供参考，不能替代专业医师的诊断。如果您感到持续的情绪困扰，建议及时寻求专业心理健康服务。
					</text>
				</view>
			</view>
		</view>



	</view>
</template>

<script>
	export default {
		data() {
			return {
				loading: false,
				totalScore: 0,
				severityIndex: 0,
				level: '',
				testTime: ''
			}
		},

		computed: {
			// 获取等级样式类
			levelClass() {
				switch (this.level) {
					case '无抑郁': return 'level-normal'
					case '轻微至轻度抑郁': return 'level-mild'
					case '中至重度抑郁': return 'level-moderate'
					case '重度抑郁': return 'level-severe'
					default: return 'level-normal'
				}
			}
		},

		onLoad(options) {
			if (options.data) {
				try {
					const reportData = JSON.parse(decodeURIComponent(options.data))
					console.log('心理评测报告数据:', reportData)

					this.totalScore = reportData.totalScore || 0
					this.testTime = reportData.testTime || ''

					// 如果有预计算的结果就使用，否则重新计算
					if (reportData.level && reportData.severityIndex) {
						this.level = reportData.level
						this.severityIndex = reportData.severityIndex
					} else {
						this.calculateResult()
					}
				} catch (error) {
					console.error('解析心理评测数据失败:', error)
					uni.showToast({
						title: '数据格式错误',
						icon: 'none'
					})
				}
			}
		},

		methods: {
			// 根据总分计算结果
			calculateResult() {
				this.severityIndex = this.totalScore / 80

				if (this.severityIndex < 0.5) {
					this.level = '无抑郁'
				} else if (this.severityIndex >= 0.5 && this.severityIndex <= 0.59) {
					this.level = '轻微至轻度抑郁'
				} else if (this.severityIndex >= 0.6 && this.severityIndex <= 0.69) {
					this.level = '中至重度抑郁'
				} else {
					this.level = '重度抑郁'
				}
			},



			// 获取等级描述
			getLevelDescription() {
				switch (this.level) {
					case '无抑郁':
						return '您的心理状态良好，没有明显的抑郁症状。继续保持积极的生活态度和健康的生活方式。'
					case '轻微至轻度抑郁':
						return '您可能存在轻微的抑郁情绪，建议关注自己的心理健康，适当调节生活节奏，必要时寻求专业帮助。'
					case '中至重度抑郁':
						return '您的抑郁症状较为明显，建议及时寻求专业心理健康服务，通过专业指导来改善心理状态。'
					case '重度抑郁':
						return '您的抑郁症状比较严重，强烈建议立即寻求专业医师或心理健康专家的帮助，进行系统的治疗和干预。'
					default:
						return '评测结果异常，请重新进行评测或咨询专业人士。'
				}
			},

			// 格式化日期
			formatDate(dateStr) {
				if (!dateStr) return ''

				try {
					let date

					// 处理不同的时间格式
					if (typeof dateStr === 'string') {
						// 检查是否是ISO格式 (如: 2025-07-07T15:15:49.000+08:00)
						if (dateStr.includes('T') && (dateStr.includes('+') || dateStr.includes('Z'))) {
							date = new Date(dateStr)
						}
						// 检查是否已经是我们想要的格式 (如: 2025-07-07 15:15)
						else if (/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}$/.test(dateStr)) {
							return dateStr
						}
						// 其他字符串格式，尝试解析
						else {
							date = new Date(dateStr)
						}
					} else if (typeof dateStr === 'number') {
						// 时间戳
						date = new Date(dateStr)
					} else {
						// 其他类型，尝试转换
						date = new Date(dateStr)
					}

					// 检查日期是否有效
					if (isNaN(date.getTime())) {
						console.warn('无效的时间格式:', dateStr)
						return this.getCurrentTime()
					}

					// 手动格式化时间，避免兼容性问题
					const year = date.getFullYear()
					const month = String(date.getMonth() + 1).padStart(2, '0')
					const day = String(date.getDate()).padStart(2, '0')
					const hours = String(date.getHours()).padStart(2, '0')
					const minutes = String(date.getMinutes()).padStart(2, '0')

					return `${year}-${month}-${day} ${hours}:${minutes}`
				} catch (error) {
					console.error('时间格式化失败:', error, '原始值:', dateStr)
					return this.getCurrentTime()
				}
			},

			// 获取当前时间
			getCurrentTime() {
				const now = new Date()
				const year = now.getFullYear()
				const month = String(now.getMonth() + 1).padStart(2, '0')
				const day = String(now.getDate()).padStart(2, '0')
				const hours = String(now.getHours()).padStart(2, '0')
				const minutes = String(now.getMinutes()).padStart(2, '0')
				return `${year}-${month}-${day} ${hours}:${minutes}`
			},



		}
	}
</script>

<style scoped>
.report-container {
	min-height: 100vh;
	background: #f5f7fa;
	padding: 30rpx;
}



/* 头部区域 */
.header-section {
	background: #ffffff;
	border-radius: 24rpx;
	padding: 40rpx 30rpx;
	margin-bottom: 30rpx;
	text-align: left;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.report-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 20rpx;
	position: relative;
	display: inline-block;
	margin-right: 20rpx;
}

.test-date {
	font-size: 24rpx;
	color: #666666;
	background: #f8f9fa;
	padding: 8rpx 20rpx;
	border-radius: 10rpx;
	display: inline-block;
	margin-bottom: 16rpx;
	border: 1rpx solid #eee;
	vertical-align: middle;
}

.report-subtitle {
	font-size: 26rpx;
	color: #666666;
	line-height: 1.6;
	padding: 16rpx 24rpx;
	background: rgba(35, 174, 244, 0.05);
	border-radius: 12rpx;
	/* border-left: 4rpx solid #23aef4; */
}

/* 加载状态 */
.loading {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 0;
}

.loading-icon {
	width: 80rpx;
	height: 80rpx;
	border: 4rpx solid rgba(35, 174, 244, 0.1);
	border-top: 4rpx solid #23aef4;
	border-radius: 50%;
	animation: spin 1s cubic-bezier(0.4, 0, 0.2, 1) infinite;
	margin-bottom: 24rpx;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	font-size: 28rpx;
	color: #666666;
	letter-spacing: 2rpx;
}

/* 内容区域 */
.content-section {
	display: flex;
	flex-direction: column;
	gap: 30rpx;
}

/* 通用标题样式 */
.section-title {
	display: flex;
	align-items: center;
	margin-bottom: 24rpx;
	padding: 0 6rpx;
}

.section-bar {
	width: 6rpx;
	height: 32rpx;
	background: #23aef4;
	border-radius: 3rpx;
	margin-right: 16rpx;
}

.section-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

/* 评分区域 */
.score-section {
	background: #ffffff;
	border-radius: 24rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.score-card {
	display: flex;
	align-items: center;
	justify-content: space-between;
	background: #23aef4af;
	border-radius: 20rpx;
	padding: 40rpx;
	color: white;
	position: relative;
	overflow: hidden;
	box-shadow: 0 8rpx 25rpx rgba(35, 174, 244, 0.2);
}

.score-display {
	text-align: center;
	position: relative;
	z-index: 1;
}

.score-number {
	font-size: 120rpx;
	font-weight: 700;
	line-height: 1;
	text-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);
	margin-bottom: 8rpx;
}

.score-label {
	font-size: 28rpx;
	margin-top: 4rpx;
	opacity: 0.9;
	letter-spacing: 4rpx;
	font-weight: 500;
	background: rgba(255, 255, 255, 0.15);
	padding: 6rpx 20rpx;
	border-radius: 20rpx;
}

.score-info {
	text-align: right;
	position: relative;
	z-index: 1;
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	gap: 12rpx;
}

.severity-index {
	font-size: 26rpx;
	opacity: 0.9;
	letter-spacing: 2rpx;
	font-weight: 500;
}

.level-result {
	font-size: 32rpx;
	font-weight: 600;
	padding: 12rpx 32rpx;
	border-radius: 40rpx;
	background: rgba(255, 255, 255, 0.25);
	backdrop-filter: blur(5px);
	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
	letter-spacing: 2rpx;
	border: 1rpx solid rgba(255, 255, 255, 0.4);
}

.level-normal { 
	background: rgba(255, 255, 255, 0.3);
	border: 1rpx solid rgba(255, 255, 255, 0.5);
	color: #ffffff;
	text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.1);
	box-shadow: 0 4rpx 12rpx rgba(255, 255, 255, 0.2);
}

.level-mild { 
	background: rgba(243, 156, 18, 0.25);
	border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.level-moderate { 
	background: rgba(230, 126, 34, 0.25);
	border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.level-severe { 
	background: rgba(231, 76, 60, 0.25);
	border: 1rpx solid rgba(255, 255, 255, 0.3);
}

/* 结果解读区域 */
.interpretation-section {
	background: #ffffff;
	border-radius: 24rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.interpretation-content {
	line-height: 1.6;
}

.level-description {
	background: rgba(35, 174, 244, 0.05);
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 30rpx;
	/* border-left: 4rpx solid #23aef4; */
}

.description-text {
	font-size: 28rpx;
	color: #333333;
	line-height: 1.8;
}

.range-text {
	font-size: 28rpx;
	color: #333333;
	font-weight: 600;
	margin-bottom: 20rpx;
	display: block;
}

.range-list {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.range-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 24rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
}

.range-score {
	font-size: 26rpx;
	font-weight: 600;
	color: #23aef4;
	background: rgba(35, 174, 244, 0.1);
	padding: 6rpx 16rpx;
	border-radius: 20rpx;
}

.range-desc {
	font-size: 26rpx;
	color: #666666;
}

/* 建议区域 */
.suggestion-section {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 30rpx;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
}

.suggestion-list {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.suggestion-item {
	display: flex;
	align-items: center;
	padding: 24rpx;
	background: #ffffff;
	border-radius: 16rpx;
	border-left: none;
	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.03);
	position: relative;
	overflow: hidden;
}

.suggestion-item::before {
	content: '';
	position: absolute;
	left: 0;
	top: 0;
	bottom: 0;
	width: 6rpx;
	background: #3ec6c6;
	border-radius: 3rpx;
}

.suggestion-icon {
	font-size: 44rpx;
	margin-right: 24rpx;
	background: rgba(62, 198, 198, 0.1);
	width: 80rpx;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 40rpx;
}

.suggestion-text {
	font-size: 28rpx;
	color: #333333;
	line-height: 1.6;
	flex: 1;
}

/* 免责声明 */
.disclaimer-section {
	background: #ffffff;
	border-radius: 24rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.disclaimer-title {
	font-size: 30rpx;
	font-weight: 600;
	color: #ff6b6b;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
}

.disclaimer-bar {
	width: 6rpx;
	height: 32rpx;
	background: #ff6b6b;
	border-radius: 3rpx;
	margin-right: 16rpx;
}

.disclaimer-content {
	background: #fff8f8;
	border-radius: 16rpx;
	padding: 24rpx;
	border: 1rpx solid rgba(255, 107, 107, 0.1);
	border-left: 4rpx solid #ff6b6b;
}

.disclaimer-text {
	font-size: 26rpx;
	color: #666666;
	line-height: 1.8;
}

/* 响应式设计 */
@media (max-width: 750px) {
	.report-container {
		padding: 20rpx;
	}

	.score-card {
		flex-direction: column;
		text-align: center;
		gap: 24rpx;
		padding: 30rpx;
	}

	.score-info {
		text-align: center;
	}

	.score-number {
		font-size: 100rpx;
	}

	.range-item {
		flex-direction: column;
		text-align: center;
		gap: 12rpx;
		padding: 20rpx;
	}

	.range-score {
		margin-bottom: 8rpx;
	}
}
</style>