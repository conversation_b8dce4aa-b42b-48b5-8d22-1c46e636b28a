<template>
	<view class="address-container">
		<!-- 地址列表 -->
		<scroll-view class="address-list" scroll-y :style="{ height: 'calc(100vh - ' + (120 + safeAreaBottom) + 'rpx)' }">
			<view v-if="addressList.length === 0" class="empty-address">
				<text class="iconfontA icon-shouhuodizhi empty-image"></text>
				<text class="empty-text">暂无收货地址</text>
			</view>
			
			<view v-else class="address-item" v-for="(address, index) in addressList" :key="index" @click="selectAddress(address)">
				<view class="address-info">
					<view class="user-info">
						<text class="name">{{ address.name }}</text>
						<text class="phone">{{ address.phone }}</text>
						<text class="default-tag" v-if="address.isDefault">默认</text>
						<text class="delete-btn" @click.stop="deleteAddress(address)">删除</text>
					</view>
					<view class="address-detail">
						<text class="area">{{ getAddressArea(address) }}</text>
						<text class="detail">{{ address.detail || '' }}</text>
					</view>
				</view>
				<view class="address-actions">
					<view class="action-btn edit" @click.stop="editAddress(address)">
						<text>编辑</text>
					</view>
					<view class="action-btn copy" @click.stop="copyAddress(address)">
						<text>复制</text>
					</view>
				</view>
			</view>
		</scroll-view>

		<!-- 底部添加按钮 -->
		<view class="add-address" @click="showAddressPopup()" :style="{ paddingBottom: safeAreaBottom + 'rpx' }">
			<text>新增收货地址</text>
		</view>
		
		<!-- 地址编辑弹窗 -->
		<view v-if="showPopup" class="popup-mask" @click="closePopup">
			<view class="popup-container" @click.stop>
				<view class="popup-header">
					<text class="title">{{ isEdit ? '编辑收货地址' : '新增收货地址' }}</text>
					<text class="close" @click="closePopup">✕</text>
				</view>

				<scroll-view class="form-content" scroll-y>
					<view class="form-group">
						<view class="form-item">
							<text class="label">收货人</text>
							<input type="text" v-model="addressForm.name" placeholder="请输入收货人姓名" />
						</view>
						<view class="form-item">
							<text class="label">手机号码</text>
							<input type="number" v-model="addressForm.phone" @input="handlePhoneInput" placeholder="请输入手机号码"/>
						</view>
						<view class="form-item">
							<text class="label">所在地区</text>
							<!-- #ifdef APP-PLUS -->
							<view class="picker-view" @click="showRegionPicker">
								<text v-if="!addressForm.province">请选择所在地区</text>
								<text v-else>{{ getFormAddressArea() }}</text>
								<text class="iconfontA icon-right"></text>
							</view>
							<!-- #endif -->
							<!-- #ifndef APP-PLUS -->
							<picker mode="region" @change="onRegionChange">
								<view class="picker-view">
									<text v-if="!addressForm.province">请选择所在地区</text>
									<text v-else>{{ getFormAddressArea() }}</text>
									<text class="iconfontA icon-right"></text>
								</view>
							</picker>
							<!-- #endif -->
						</view>
						<view class="form-item">
							<text class="label">详细地址</text>
							<textarea v-model="addressForm.detail" placeholder="请输入详细地址，如街道、楼牌号等" />
						</view>
					</view>

					<view class="form-group">
						<view class="form-item switch">
							<text class="label">设为默认地址</text>
							<switch :checked="addressForm.isDefault" @change="onDefaultChange" color="#3ec6c6" />
						</view>
					</view>
				</scroll-view>

				<view class="popup-footer">
					<view class="save-btn" @click="saveAddress">
						<text>保存</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				addressList: [],
				isFromOrder: false, // 是否从订单页面跳转而来
				isEdit: false,
				showPopup: false, // 控制弹窗显示
				systemInfo: {}, // 系统信息
				safeAreaBottom: 0, // 底部安全区域高度
				addressForm: {
					id: '',
					name: '',
					phone: '',
					province: '',
					city: '',
					district: '',
					detail: '',
					isDefault: false
				}
			}
		},
		onLoad(options) {
			if (options.from === 'order') {
				this.isFromOrder = true
			}

			this.loadAddressList()
			this.getSystemInfo()
		},
		methods: {
			// 处理手机号输入，自动去除空格
			handlePhoneInput(e) {
				// 去除所有空格
				const cleanValue = e.detail.value.replace(/\s/g, '');
				this.addressForm.phone = cleanValue;
			},
			// 获取系统信息
			getSystemInfo() {
				const systemInfo = uni.getSystemInfoSync()
				this.systemInfo = systemInfo

				// 计算底部安全区域高度
				// safeAreaInsets.bottom 是底部安全区域的高度（px）
				if (systemInfo.safeAreaInsets && systemInfo.safeAreaInsets.bottom) {
					// 将px转换为rpx (1px = 2rpx on most devices)
					this.safeAreaBottom = systemInfo.safeAreaInsets.bottom * 2
				} else {
					// 如果没有安全区域信息，设置默认值
					this.safeAreaBottom = 0
				}

				console.log('系统信息:', systemInfo)
				console.log('底部安全区域高度:', this.safeAreaBottom + 'rpx')
			},

			// 安全地转换为字符串
			safeToString(value) {
				if (value === null || value === undefined) {
					return ''
				}
				if (typeof value === 'string') {
					return value
				}
				if (typeof value === 'number' || typeof value === 'boolean') {
					return String(value)
				}
				// 如果是对象，返回空字符串而不是 [object Object]
				return ''
			},

			// 获取地址区域显示文本
			getAddressArea(address) {
				if (!address) return ''
				const province = this.safeToString(address.province)
				const city = this.safeToString(address.city)
				const district = this.safeToString(address.district)
				return province + city + district
			},

			// 获取表单地址区域显示文本
			getFormAddressArea() {
				console.log('addressForm数据:', this.addressForm)
				const province = this.safeToString(this.addressForm.province)
				const city = this.safeToString(this.addressForm.city)
				const district = this.safeToString(this.addressForm.district)
				const result = province + city + district
				console.log('地区显示结果:', result)
				return result
			},

			// 加载地址列表
			loadAddressList() {
				const addresses = uni.getStorageSync('addresses') || []
				console.log('loadAddressList - 从存储读取的原始数据:', addresses)
				// 确保每个地址的数据格式正确
				this.addressList = addresses.map(address => ({
					...address,
					province: this.safeToString(address.province),
					city: this.safeToString(address.city),
					district: this.safeToString(address.district),
					detail: this.safeToString(address.detail),
					name: this.safeToString(address.name),
					phone: this.safeToString(address.phone),
					isDefault: Boolean(address.isDefault)
				}))
				console.log('loadAddressList - 处理后的addressList:', this.addressList)
			},
			
			// 选择地址
			selectAddress(address) {
				if (this.isFromOrder) {
					uni.$emit('addressSelected', address)
					uni.navigateBack()
				}
			},
			
			// 显示地址弹窗
			showAddressPopup(address = null) {
				console.log('showAddressPopup 被调用，参数:', address)
				console.log('调用前的 isEdit 状态:', this.isEdit)

				// 检查是否是事件对象（事件对象会有 type 属性）
				if (address && typeof address === 'object' && address.type) {
					address = null // 如果是事件对象，将其设为 null
					console.log('检测到事件对象，重置为 null')
				}

				if (address && address.id) { // 确保是有效的地址对象
					this.isEdit = true
					// 确保数据格式正确，安全地转换数据类型
					this.addressForm = {
						id: address.id || '',
						name: this.safeToString(address.name),
						phone: this.safeToString(address.phone),
						province: this.safeToString(address.province),
						city: this.safeToString(address.city),
						district: this.safeToString(address.district),
						detail: this.safeToString(address.detail),
						isDefault: Boolean(address.isDefault)
					}
				} else {
					this.isEdit = false
					this.addressForm = {
						id: '',
						name: '',
						phone: '',
						province: '',
						city: '',
						district: '',
						detail: '',
						isDefault: false
					}
				}
				console.log('设置后的 isEdit 状态:', this.isEdit)
				console.log('弹窗显示时的addressForm:', JSON.stringify(this.addressForm, null, 2))
				this.showPopup = true
			},

			// 关闭弹窗
			closePopup() {
				this.showPopup = false
				// 重置编辑状态，确保下次打开是新增模式
				this.isEdit = false
				console.log('弹窗关闭，重置 isEdit 为:', this.isEdit)
			},
			
			// 编辑地址
			editAddress(address) {
				this.showAddressPopup(address)
			},
			
			// 删除地址
			deleteAddress(address) {
				uni.showModal({
					title: '删除地址',
					content: '确定要删除该收货地址吗？',
					success: (res) => {
						if (res.confirm) {
							let addresses = uni.getStorageSync('addresses') || []
							addresses = addresses.filter(item => item.id !== address.id)
							uni.setStorageSync('addresses', addresses)
							this.loadAddressList()

							uni.showToast({
								title: '删除成功',
								icon: 'success'
							})
						}
					}
				})
			},

			// 复制地址文字信息
			copyAddress(address) {
				// 组装地址文字信息
				const addressText = `收货人：${this.safeToString(address.name)}
手机号：${this.safeToString(address.phone)}
地址：${this.getAddressArea(address)}${this.safeToString(address.detail)}`

				// 复制到剪贴板
				uni.setClipboardData({
					data: addressText,
					success: () => {
						uni.showToast({
							title: '地址已复制',
							icon: 'success'
						})
					},
					fail: () => {
						uni.showToast({
							title: '复制失败',
							icon: 'none'
						})
					}
				})
			},
			
			// 地区选择改变
			onRegionChange(e) {
				console.log('地区选择数据:', e.detail)
				if (e.detail && e.detail.value) {
					if (Array.isArray(e.detail.value)) {
						const [province, city, district] = e.detail.value
						this.addressForm.province = String(province || '')
						this.addressForm.city = String(city || '')
						this.addressForm.district = String(district || '')
					} else {
						console.warn('地区选择返回的数据格式不正确:', e.detail.value)
					}
				}
			},

			// App端显示地区选择器
			showRegionPicker() {
				// #ifdef APP-PLUS
				// 直接调用手动输入地区
				this.manualInputRegion()
				// #endif
			},



			// 手动输入地区
			manualInputRegion() {
				const that = this
				uni.showModal({
					title: '输入地区信息',
					editable: true,
					placeholderText: '请输入省市区，如：广东省广州市天河区',
					success: function(res) {
						if (res.confirm && res.content) {
							that.parseRegionText(res.content.trim())
						}
					}
				})
			},

			// 解析地区文本
			parseRegionText(regionText) {
				if (!regionText || regionText.length === 0) {
					uni.showToast({
						title: '请输入地区信息',
						icon: 'none'
					})
					return
				}

				// 重置地区信息
				this.addressForm.province = ''
				this.addressForm.city = ''
				this.addressForm.district = ''

				// 智能解析地区信息
				let remaining = regionText

				// 解析省份
				const provincePatterns = [
					/^(.+?省)/,
					/^(.+?自治区)/,
					/^(.+?市)(?=.*[市区县])/,  // 直辖市
					/^(北京|天津|上海|重庆)(?!.*省)/
				]

				for (let pattern of provincePatterns) {
					const match = remaining.match(pattern)
					if (match) {
						this.addressForm.province = match[1]
						if (!this.addressForm.province.endsWith('省') &&
							!this.addressForm.province.endsWith('自治区') &&
							!this.addressForm.province.endsWith('市')) {
							// 对于直辖市，添加"市"后缀
							if (['北京', '天津', '上海', '重庆'].includes(this.addressForm.province)) {
								this.addressForm.province += '市'
							}
						}
						remaining = remaining.replace(match[0], '')
						break
					}
				}

				// 解析城市
				if (remaining) {
					const cityPatterns = [
						/^(.+?市)/,
						/^(.+?州)/,
						/^(.+?盟)/,
						/^(.+?地区)/
					]

					for (let pattern of cityPatterns) {
						const match = remaining.match(pattern)
						if (match) {
							this.addressForm.city = match[1]
							if (!this.addressForm.city.endsWith('市') &&
								!this.addressForm.city.endsWith('州') &&
								!this.addressForm.city.endsWith('盟') &&
								!this.addressForm.city.endsWith('地区')) {
								this.addressForm.city += '市'
							}
							remaining = remaining.replace(match[0], '')
							break
						}
					}
				}

				// 解析区县
				if (remaining) {
					const districtPatterns = [
						/^(.+?区)/,
						/^(.+?县)/,
						/^(.+?旗)/,
						/^(.+)/  // 剩余部分作为区县
					]

					for (let pattern of districtPatterns) {
						const match = remaining.match(pattern)
						if (match) {
							this.addressForm.district = match[1]
							if (!this.addressForm.district.endsWith('区') &&
								!this.addressForm.district.endsWith('县') &&
								!this.addressForm.district.endsWith('旗')) {
								// 如果没有明确的区县后缀，根据长度判断是否添加
								if (this.addressForm.district.length <= 3) {
									this.addressForm.district += '区'
								}
							}
							break
						}
					}
				}

				// 如果没有解析出省份，将整个输入作为省份
				if (!this.addressForm.province && regionText) {
					this.addressForm.province = regionText
				}

				// 显示解析结果
				const result = this.getFormAddressArea()
				if (result) {
					uni.showToast({
						title: '地区设置完成',
						icon: 'success'
					})
					console.log('解析结果:', {
						province: this.addressForm.province,
						city: this.addressForm.city,
						district: this.addressForm.district,
						display: result
					})
				} else {
					uni.showToast({
						title: '地区格式不正确，请重新输入',
						icon: 'none'
					})
				}
			},


			
			// 切换默认地址
			onDefaultChange(e) {
				this.addressForm.isDefault = e.detail.value
			},
			
			// 保存地址
			saveAddress() {
				// 表单验证
				if (!this.addressForm.name) {
					return uni.showToast({
						title: '请输入收货人姓名',
						icon: 'none'
					})
				}
				if (!this.addressForm.phone) {
					return uni.showToast({
						title: '请输入手机号码',
						icon: 'none'
					})
				}
				if (!/^1\d{10}$/.test(this.addressForm.phone)) {
					return uni.showToast({
						title: '手机号码格式不正确',
						icon: 'none'
					})
				}
				if (!this.addressForm.province) {
					return uni.showToast({
						title: '请选择所在地区',
						icon: 'none'
					})
				}
				if (!this.addressForm.detail) {
					return uni.showToast({
						title: '请输入详细地址',
						icon: 'none'
					})
				}

				let addresses = uni.getStorageSync('addresses') || []
				console.log('保存前的地址列表:', addresses)
				console.log('当前表单数据:', this.addressForm)
				console.log('是否编辑模式:', this.isEdit)

				// 如果设置为默认地址，需要将其他地址的默认状态取消
				if (this.addressForm.isDefault) {
					addresses = addresses.map(item => ({
						...item,
						isDefault: false
					}))
				}

				if (this.isEdit) {
					// 编辑模式
					const index = addresses.findIndex(item => item.id === this.addressForm.id)
					if (index !== -1) {
						addresses[index] = this.addressForm
					}
					console.log('编辑模式 - 更新后的地址列表:', addresses)
				} else {
					// 新增模式
					this.addressForm.id = Date.now().toString()
					// 如果是第一个地址，自动设为默认
					if (addresses.length === 0) {
						this.addressForm.isDefault = true
					}
					addresses.push(this.addressForm)
					console.log('新增模式 - 添加后的地址列表:', addresses)
				}

				uni.setStorageSync('addresses', addresses)
				console.log('存储后验证:', uni.getStorageSync('addresses'))
				this.loadAddressList()
				console.log('重新加载后的addressList:', this.addressList)
				this.closePopup()
				
				uni.showToast({
					title: '保存成功',
					icon: 'success'
				})
			}
		}
	}
</script>

<style lang="scss">
	.address-container {
		min-height: 100vh;
		background-color: #f8f8f8;
		padding-bottom: 120rpx;
		box-sizing: border-box;
	}



	.empty-address {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 200rpx 0;

		.empty-image {
			font-size: 200rpx;
			margin-bottom: 30rpx;
			color: rgba(255, 107, 129, 0.7);
		}

		.empty-text {
			font-size: 30rpx;
			color: rgba(138, 138, 138, 0.8);
			letter-spacing: 2rpx;
		}
	}

	.address-item {
		background-color: #fff;
		margin: 20rpx;
		border-radius: 16rpx;
		padding: 30rpx;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);

		.address-info {
			.user-info {
				display: flex;
				align-items: center;
				margin-bottom: 16rpx;

				.name {
					font-size: 32rpx;
					color: #333;
					font-weight: 500;
					margin-right: 20rpx;
				}

				.phone {
					font-size: 28rpx;
					color: #666;
					margin-right: 20rpx;
				}

				.default-tag {
					font-size: 22rpx;
					color: #ff5555;
					border: 2rpx solid #ff5555;
					padding: 2rpx 12rpx;
					border-radius: 20rpx;
					margin-right: 20rpx;
				}

				.delete-btn {
					font-size: 26rpx;
					color: #ff5555;
					margin-left: auto;
					padding: 8rpx 16rpx;
					border: 1rpx solid #ff5555;
					border-radius: 20rpx;
					background: transparent;
				}
			}

			.address-detail {
				.area {
					font-size: 28rpx;
					color: #333;
					margin-right: 10rpx;
				}

				.detail {
					font-size: 28rpx;
					color: #333;
				}
			}
		}

		.address-actions {
			display: flex;
			justify-content: flex-end;
			margin-top: 20rpx;
			padding-top: 20rpx;
			border-top: 2rpx solid #f5f5f5;
			flex-wrap: wrap; /* 允许换行 */
			gap: 10rpx; /* 设置按钮间距 */

			.action-btn {
				display: flex;
				align-items: center;
				padding: 8rpx 16rpx;
				border-radius: 20rpx;
				border: 1rpx solid;
				min-width: 80rpx; /* 设置最小宽度 */
				justify-content: center; /* 居中对齐 */

				text {
					font-size: 26rpx;
					white-space: nowrap; /* 防止文字换行 */
				}

				&.edit {
					color: #fff;
					border-color: #3ec6c6;
					background: #3ec6c6;
				}

				&.copy {
					color: #666;
					border-color: #ddd;
					background: transparent;
				}
			}
		}
	}

	.add-address {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		min-height: 100rpx;
		background: #fff;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
		padding: 20rpx 30rpx;
		/* 支持安全区域 */
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);

		text {
			background: #3ec6c6;
			color: #fff;
			font-size: 30rpx;
			padding: 16rpx 80rpx;
			border-radius: 40rpx;
			text-align: center;
			display: inline-block;
		}
	}

	/* 弹窗样式 */
	.popup-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.5);
		z-index: 999;
		display: flex;
		align-items: flex-end;
	}

	.popup-container {
		width: 100%;
		background: #fff;
		border-radius: 24rpx 24rpx 0 0;
		overflow: hidden;
		animation: slideUp 0.3s ease-out;

		.popup-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 30rpx;
			border-bottom: 2rpx solid #f5f5f5;

			.title {
				font-size: 32rpx;
				color: #333;
				font-weight: 500;
			}

			.close {
				font-size: 40rpx;
				color: #999;
				padding: 10rpx;
			}
		}

		.form-content {
			max-height: 800rpx;
			padding-bottom: 120rpx;
		}

		.form-group {
			padding: 0 30rpx;
			background: #fff;
		}

		.form-item {
			display: flex;
			align-items: flex-start;
			padding: 30rpx 0;
			border-bottom: 2rpx solid #f5f5f5;
			
			&:last-child {
				border-bottom: none;
			}
			
			.label {
				width: 160rpx;
				font-size: 28rpx;
				color: #333;
				padding-top: 6rpx;
			}
			
			input {
				flex: 1;
				font-size: 28rpx;
				height: 48rpx;
			}
			
			textarea {
				flex: 1;
				font-size: 28rpx;
				height: 120rpx;
			}
			
			.picker-view {
				flex: 1;
				display: flex;
				justify-content: space-between;
				align-items: center;
				font-size: 28rpx;
				color: #333;
				cursor: pointer;

				text:first-child {
					color: #999;
				}

				.iconfontA {
					color: #999;
					font-size: 32rpx;
				}

				&:active {
					background-color: #f5f5f5;
				}
			}
			
			&.switch {
				align-items: center;
				
				switch {
					transform: scale(0.8);
					margin-right: -20rpx;
				}
			}
		}
		
		.popup-footer {
			position: absolute;
			bottom: 0;
			left: 0;
			right: 0;
			padding: 20rpx 30rpx;
			background: #fff;
			box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
			
			.save-btn {
				background: #3ec6c6;
				height: 80rpx;
				border-radius: 40rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				
				text {
					color: #fff;
					font-size: 30rpx;
				}
			}
		}
	}

	/* 动画效果 */
	@keyframes slideUp {
		from {
			transform: translateY(100%);
		}
		to {
			transform: translateY(0);
		}
	}

	/* 小屏幕适配 */
	@media screen and (max-width: 600rpx) {
		.add-address {
			padding: 15rpx 20rpx; /* 小屏幕减少内边距 */

			text {
				padding: 14rpx 60rpx; /* 保持合理的内边距 */
				font-size: 28rpx; /* 小屏幕稍微减小字体 */
			}
		}
	}

	/* 支持安全区域的设备 */
	@supports (bottom: constant(safe-area-inset-bottom)) or (bottom: env(safe-area-inset-bottom)) {
		.add-address {
			/* 在支持安全区域的设备上，确保按钮不会被底部指示器遮挡 */
			padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
			padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
		}
	}
</style>
