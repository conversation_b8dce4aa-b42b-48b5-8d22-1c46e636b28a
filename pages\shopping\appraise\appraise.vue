<template>
	<view class="appraise-container">
		
		<view class="media-list" v-if="mediaList.length">
			<view v-for="(item, idx) in mediaList" :key="idx" class="media-item">
				<image v-if="item.type === 'image'" :src="item.url" class="media-thumb" @click="previewMedia(idx)" />
				<video v-else :src="item.url" class="media-thumb" controls />
				<view class="media-delete" @click.stop="removeMedia(idx)"><uni-icons type="closeempty" size="20" color="#fff" /></view>
			</view>
		</view>
		<view class="score-section">
			<text class="score-label">综合评分</text>
			<view class="star-list">
				<uni-icons v-for="i in 5" :key="i" :type="score >= i ? 'star-filled' : 'star'" size="36" :color="score >= i ? '#FFD700' : '#ccc'" @click="score = i" />
			</view>
		</view>
		
		<textarea class="content-input" v-model="content" placeholder="多多描述商品和使用感受，更受欢迎哦" maxlength="300" />
		<view class="anonymous-row">
			<checkbox v-model="anonymous" color="#ff5555" />
			<text class="anonymous-label">匿名评价</text>
		</view>
		<button class="submit-btn" @click="submitAppraise">{{ isEditMode ? '更新评价' : '提交评价' }}</button>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				mediaList: [],
				score: 5, // 默认5星好评
				selectedTags: [],
				content: '',
				anonymous: true,
				orderId: '',
				productId: '',
				orderInfo: null,
				productInfo: null,
				fromPhotoPage: false, // 标记是否从拍摄页面跳转过来
				isEditMode: false, // 是否为编辑模式
				reviewId: '', // 编辑的评价ID
				originalReviewData: null // 原始评价数据
			}
		},
		onLoad(options) {
			// 检查是否为编辑模式
			if (options.edit === 'true' && options.reviewId) {
				this.isEditMode = true;
				this.reviewId = options.reviewId;
				this.loadReviewForEdit();
				return;
			}

			if (options.mediaList) {
				try {
					this.mediaList = JSON.parse(decodeURIComponent(options.mediaList));
					// 如果有mediaList参数，说明是从拍摄页面跳转过来的
					this.fromPhotoPage = true;
				} catch(e) {}
			}

			// 获取订单和商品信息
			this.orderId = options.orderId || '';
			this.productId = options.productId || '';

			if (this.orderId) {
				this.loadOrderInfo();
			}
			if (this.productId) {
				this.loadProductInfo();
			}
		},
		methods: {
			goBack() {
				uni.navigateBack();
			},
			removeMedia(idx) {
				this.mediaList.splice(idx, 1);
			},
			previewMedia(idx) {
				const item = this.mediaList[idx];
				if (item.type === 'image') {
					uni.previewImage({
						urls: this.mediaList.filter(m=>m.type==='image').map(m=>m.url),
						current: item.url
					});
				} else if (item.type === 'video') {
					uni.previewMedia({
						sources: [{url: item.url, type: 'video'}],
						current: 0
					});
				}
			},
			toggleTag(tag) {
				const idx = this.selectedTags.indexOf(tag);
				if (idx > -1) {
					this.selectedTags.splice(idx, 1);
				} else {
					this.selectedTags.push(tag);
				}
			},
			// 加载订单信息
			loadOrderInfo() {
				const orders = uni.getStorageSync('orders') || [];
				this.orderInfo = orders.find(order => order.id === this.orderId);
			},

			// 加载商品信息
			loadProductInfo() {
				// 这里可以从商品列表或API获取商品信息
				// 暂时使用模拟数据
				this.productInfo = {
					id: this.productId,
					name: '商品名称',
					image: '/static/images/product-placeholder.jpg',
					spec: '规格信息'
				};
			},

			submitAppraise() {
				if (!this.score) {
					uni.showToast({title: '请打分', icon: 'none'});
					return;
				}
				if (!this.content.trim()) {
					uni.showToast({title: '请填写评价内容', icon: 'none'});
					return;
				}

				if (this.isEditMode) {
					// 编辑模式：更新现有评价
					this.updateExistingReview();
				} else {
					// 新建模式：创建新评价
					this.createNewReview();
				}
			},

			// 创建新评价
			createNewReview() {
				// 创建评价数据
				const reviewData = {
					id: Date.now().toString(),
					orderId: this.orderId,
					productId: this.productId || (this.orderInfo && this.orderInfo.products[0].id),
					rating: Number(this.score), // 确保是数字类型
					content: this.content.trim(),
					images: this.mediaList.filter(item => item.type === 'image').map(item => item.url),
					anonymous: this.anonymous,
					createTime: this.formatDateTime(new Date()),
					isLiked: false,
					product: this.productInfo || (this.orderInfo && this.orderInfo.products[0]) || {
						id: this.productId,
						name: '商品名称',
						image: '/static/images/product-placeholder.jpg',
						spec: '默认规格'
					}
				};

				// 保存到用户评价记录
				this.saveUserReview(reviewData);

				// 同步到商品评价
				this.syncToProductReviews(reviewData);

				// 更新订单状态
				if (this.orderId) {
					this.updateOrderReviewStatus();
				}

				uni.showToast({title: '评价提交成功', icon: 'success'});

				// 立即通知订单页面刷新（不等待返回）
				setTimeout(() => {
					uni.$emit('orderUpdated', {
						orderId: this.orderId,
						action: 'reviewed'
					});
				}, 100);

				// 延迟返回，确保数据保存完成
				setTimeout(() => {
					if (this.fromPhotoPage) {
						// 如果是从拍摄页面跳转过来的，直接返回到评价中心
						uni.navigateBack({
							delta: 2 // 返回两级页面，跳过拍摄页面
						});
					} else {
						// 正常返回上一页
						uni.navigateBack();
					}
				}, 1200);
			},

			// 更新现有评价
			updateExistingReview() {
				const updatedReviewData = {
					...this.originalReviewData,
					rating: Number(this.score),
					content: this.content.trim(),
					images: this.mediaList.filter(item => item.type === 'image').map(item => item.url),
					anonymous: this.anonymous,
					updateTime: this.formatDateTime(new Date())
				};

				// 更新用户评价记录
				const userReviews = uni.getStorageSync('userReviews') || [];
				const reviewIndex = userReviews.findIndex(r => r.id === this.reviewId);
				if (reviewIndex !== -1) {
					userReviews[reviewIndex] = updatedReviewData;
					uni.setStorageSync('userReviews', userReviews);
				}

				// 更新商品评价
				this.updateProductReview(updatedReviewData);

				uni.showToast({title: '评价更新成功', icon: 'success'});

				setTimeout(() => {
					uni.navigateBack();
				}, 1200);
			},

			// 更新商品评价
			updateProductReview(reviewData) {
				const productReviews = uni.getStorageSync('productReviews') || {};
				const productId = reviewData.productId;

				if (productReviews[productId]) {
					const reviewIndex = productReviews[productId].findIndex(r => r.id === reviewData.id);
					if (reviewIndex !== -1) {
						const productReview = {
							id: reviewData.id,
							username: reviewData.anonymous ? '匿名用户' : (uni.getStorageSync('userInfo')?.nickname || '用户'),
							avatar: reviewData.anonymous ? '/static/images/profile.jpg' : (uni.getStorageSync('userInfo')?.avatar || '/static/images/profile.jpg'),
							rating: reviewData.rating,
							date: reviewData.createTime.split(' ')[0],
							content: reviewData.content,
							images: reviewData.images
						};
						productReviews[productId][reviewIndex] = productReview;
						uni.setStorageSync('productReviews', productReviews);
					}
				}
			},

			// 保存用户评价记录
			saveUserReview(reviewData) {
				const userReviews = uni.getStorageSync('userReviews') || [];
				userReviews.unshift(reviewData);
				uni.setStorageSync('userReviews', userReviews);
			},

			// 同步到商品评价
			syncToProductReviews(reviewData) {
				const productReviews = uni.getStorageSync('productReviews') || {};
				const productId = reviewData.productId;

				if (!productReviews[productId]) {
					productReviews[productId] = [];
				}

				// 添加到商品评价列表
				const productReview = {
					id: reviewData.id,
					username: reviewData.anonymous ? '匿名用户' : (uni.getStorageSync('userInfo')?.nickname || '用户'),
					avatar: reviewData.anonymous ? '/static/images/profile.jpg' : (uni.getStorageSync('userInfo')?.avatar || '/static/images/profile.jpg'),
					rating: reviewData.rating,
					date: reviewData.createTime.split(' ')[0],
					content: reviewData.content,
					images: reviewData.images
				};

				productReviews[productId].unshift(productReview);
				uni.setStorageSync('productReviews', productReviews);
			},

			// 更新订单评价状态
			updateOrderReviewStatus() {
				const orders = uni.getStorageSync('orders') || [];
				const orderIndex = orders.findIndex(order => order.id === this.orderId);

				if (orderIndex !== -1) {
					// 标记为已评价
					orders[orderIndex].isReviewed = true;
					orders[orderIndex].reviewTime = new Date().toLocaleString();

					// 更新订单状态为已完成
					orders[orderIndex].status = 'completed';
					orders[orderIndex].statusText = '已完成';
					orders[orderIndex].statusClass = 'status-completed';

					uni.setStorageSync('orders', orders);
				}
			},

			// 格式化日期时间
			formatDateTime(date) {
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');
				const seconds = String(date.getSeconds()).padStart(2, '0');

				return `${year}.${month}.${day} ${hours}:${minutes}:${seconds}`;
			},

			// 加载评价数据用于编辑
			loadReviewForEdit() {
				const userReviews = uni.getStorageSync('userReviews') || [];
				const reviewData = userReviews.find(r => r.id === this.reviewId);

				if (reviewData) {
					this.originalReviewData = reviewData;
					this.score = reviewData.rating;
					this.content = reviewData.content;
					this.anonymous = reviewData.anonymous;
					this.mediaList = reviewData.images ? reviewData.images.map(url => ({ type: 'image', url })) : [];
					this.orderId = reviewData.orderId;
					this.productId = reviewData.productId;

					// 加载商品信息
					if (reviewData.product) {
						this.productInfo = reviewData.product;
					}
				}
			}
		}
	}
</script>

<style scoped>
	.appraise-container {
		background: #fff;
		min-height: 100vh;
		display: flex;
		flex-direction: column;
		padding-bottom: 40rpx;
	}
	
	.appraise-title {
		flex: 1;
		text-align: center;
		font-size: 36rpx;
		font-weight: bold;
	}
	.media-list {
		display: flex;
		flex-wrap: wrap;
		gap: 24rpx;
		padding: 32rpx 24rpx 0 24rpx;
	}
	.media-item {
		position: relative;
	}
	.media-thumb {
		width: 180rpx;
		height: 180rpx;
		border-radius: 12rpx;
		object-fit: cover;
	}
	.media-delete {
		position: absolute;
		top: 0;
		right: 0;
		background: rgba(0,0,0,0.5);
		border-radius: 50%;
		padding: 6rpx;
	}
	.score-section {
		display: flex;
		align-items: center;
		gap: 24rpx;
		padding: 32rpx 24rpx 0 24rpx;
	}
	.score-label {
		font-size: 30rpx;
		color: #333;
		font-weight: 500;
	}
	.star-list {
		display: flex;
		gap: 8rpx;
	}
	.tags-section {
		display: flex;
		flex-wrap: wrap;
		gap: 16rpx;
		padding: 24rpx 24rpx 0 24rpx;
	}
	.tag-item {
		padding: 10rpx 24rpx;
		border-radius: 32rpx;
		background: #f5f5f5;
		color: #666;
		font-size: 26rpx;
		border: 2rpx solid #f5f5f5;
		transition: all 0.2s;
	}
	.tag-item.selected {
		background: #fff0f0;
		color: #ff5555;
		border-color: #ff5555;
	}
	.content-input {
		margin: 32rpx 24rpx 0 24rpx;
		width: 92%;
		min-height: 120rpx;
		border-radius: 16rpx;
		border: 2rpx solid #f0f0f0;
		font-size: 28rpx;
		padding: 20rpx;
		background: #fafafa;
		color: #333;
	}
	.anonymous-row {
		display: flex;
		align-items: center;
		gap: 16rpx;
		padding: 24rpx 24rpx 0 24rpx;
	}
	.anonymous-label {
		font-size: 26rpx;
		color: #666;
	}
	.submit-btn {
		width: 92%;
		margin: 40rpx auto 0 auto;
		background: #ff5555;
		color: #fff;
		border-radius: 32rpx;
		font-size: 30rpx;
		font-weight: bold;
		padding: 20rpx 0;
		border: none;
	}
</style>
