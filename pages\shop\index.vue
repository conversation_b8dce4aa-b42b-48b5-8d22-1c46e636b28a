<template>
  <view class="shop-container">
    <!-- 搜索栏 -->
    <view class="search-box">
      <view class="search-input">
        <uni-icons type="search" size="16" color="#999"></uni-icons>
        <input type="text" v-model="searchKey" placeholder="搜索商品" @confirm="handleSearch"/>
      </view>
      <view class="search-btn" @click="handleSearch">搜索</view>
    </view>

    <!-- 主体内容 -->
    <view class="main-content">
      <!-- 左侧分类列表 -->
      <scroll-view class="category-list" scroll-y="true">
        <view 
          v-for="item in categories" 
          :key="item.id"
          class="category-item"
          :class="{ active: selectedCategory === item.id }"
          @click="selectCategory(item.id)"
        >
          {{ item.name }}
        </view>
      </scroll-view>

      <!-- 右侧内容区域 -->
      <view class="right-content">
        <!-- 排序导航 -->
        <view class="sort-nav">
          <view 
            class="sort-item" 
            :class="{ active: sortType === 'comprehensive' }" 
            @click="changeSort('comprehensive')"
          >
            <uni-icons type="list" size="16" :color="sortType === 'comprehensive' ? '#3ec6c6' : '#666'"></uni-icons>
            <text>综合</text>
          </view>
          <view 
            class="sort-item" 
            :class="{ active: sortType === 'sales' }" 
            @click="changeSort('sales')"
          >
            <text>销量</text>
            <view class="sort-triangles">
              <view class="triangle up" :class="{ active: sortType === 'sales' && sortOrder === 'asc' }"></view>
              <view class="triangle down" :class="{ active: sortType === 'sales' && sortOrder === 'desc' }"></view>
            </view>
          </view>
          <view 
            class="sort-item" 
            :class="{ active: sortType === 'price' }" 
            @click="changeSort('price')"
          >
            <text>价格</text>
            <view class="sort-triangles">
              <view class="triangle up" :class="{ active: sortType === 'price' && sortOrder === 'asc' }"></view>
              <view class="triangle down" :class="{ active: sortType === 'price' && sortOrder === 'desc' }"></view>
            </view>
          </view>
        </view>

        <!-- 商品列表 -->
        <scroll-view 
          class="product-list" 
          scroll-y="true" 
          @scrolltolower="loadMore"
          :style="{ height: scrollHeight + 'px' }"
          refresher-enabled
          :refresher-triggered="isRefreshing"
          @refresherrefresh="onRefresh"
        >
          <view class="product-item" v-for="item in displayProducts" :key="item.id" @click="toProductDetail(item)">
            <image :src="item.image" mode="aspectFill" class="product-image" @error="handleImageError"/>
            <view class="product-info">
              <view class="product-name">{{ item.name }}</view>
              <view class="product-tags">
                <text class="tag" v-if="item.isPromotion">限时优惠</text>
                <text class="tag" v-if="item.freeShipping">包邮</text>
                <text class="tag stock-tag" :class="{'low-stock': item.stock < 10}">
                  库存: {{ item.stock }}
                </text>
              </view>
              <view class="price-box">
                <view class="price-info">
                  <text class="current-price">¥{{ item.price }}</text>
                  <text class="original-price" v-if="item.originalPrice">{{ item.originalPrice }}</text>
                </view>
                <view class="cart-btn" @click.stop="addToCart(item)">
                  <uni-icons type="cart" size="20" color="#fff"></uni-icons>
                </view>
              </view>
            </view>
          </view>
          
          <!-- 加载状态 -->
          <view class="loading-more" v-if="isLoading">
            <uni-icons type="spinner-cycle" size="24" color="#999"></uni-icons>
            <text>加载中...</text>
          </view>
          <view class="no-more" v-if="!hasMore && !isLoading">
            <text>没有更多商品了</text>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>
</template>

<script>
import { getProductCategories, getProductList } from '@/api/system/shop'

export default {
  data() {
    return {
      searchKey: '',
      sortType: 'comprehensive', // comprehensive, sales, price
      sortOrder: 'desc', // asc, desc
      selectedCategory: 0,
      page: 1,
      pageSize: 10,
      isLoading: false,
      hasMore: true,
      isRefreshing: false,
      scrollHeight: 0,
      categories: [
        { id: 0, name: '全部商品' }
      ],
      allProducts: [], // 所有商品数据
      displayProducts: [], // 当前显示的商品
    }
  },
  mounted() {
    // 计算滚动区域高度
    const systemInfo = uni.getSystemInfoSync();
    // 减去搜索栏高度(92rpx)和排序栏高度(80rpx)
    this.scrollHeight = systemInfo.windowHeight - uni.upx2px(172);

    // 加载分类数据
    this.loadCategories();

    // 初始加载数据
    this.loadInitialData();
  },
  methods: {
    // 解析图片数据 - 返回所有图片数组
    parseImageData(imageData) {
      try {
        if (!imageData) {
          return ['/static/images/default-product.png'];
        }

        // 如果是字符串格式的JSON数组
        if (typeof imageData === 'string') {
          // 处理特殊格式：{"url1","url2","url3"}
          if (imageData.startsWith('{') && imageData.endsWith('}')) {
            // 移除首尾的大括号
            const content = imageData.slice(1, -1);

            // 按逗号分割并清理引号
            const urls = content.split(',').map(url => {
              return url.trim().replace(/^["']|["']$/g, ''); // 移除首尾引号
            }).filter(url => url.length > 0);

            // 返回所有有效的图片URL数组
            return urls.length > 0 ? urls : ['/static/images/default-product.png'];
          }

          // 尝试解析为标准JSON数组
          try {
            const parsed = JSON.parse(imageData);
            if (Array.isArray(parsed) && parsed.length > 0) {
              return parsed;
            }
          } catch (e) {
            // 如果解析失败，检查是否是逗号分隔的URL
            if (imageData.includes(',')) {
              return imageData.split(',').map(url => url.trim()).filter(url => url.length > 0);
            }
            // 单个URL，包装成数组
            return [imageData];
          }
        }

        // 如果是数组，直接返回
        if (Array.isArray(imageData) && imageData.length > 0) {
          return imageData;
        } 
      } catch (error) {
        console.error('解析图片数据失败:', error, imageData);
      }
    },

    // 获取显示图片（列表页只显示第一张）
    getDisplayImage(imageData) {
      const images = this.parseImageData(imageData);
      return images[0];
    },

    // 解析商品描述内容
    parseDescriptionContent(content) {
      try {
        if (!content) {
          return '暂无描述';
        }
        if (typeof content === 'string') {
          // 处理特殊格式：{"内容1","内容2","内容3"} - 这不是标准JSON
          if (content.startsWith('{') && content.endsWith('}')) {
            // 移除首尾的大括号
            const innerContent = content.slice(1, -1);


            // 使用正则表达式匹配被引号包围的内容
            const regex = /"([^"]*)"/g;
            const descriptions = [];
            let match;

            while ((match = regex.exec(innerContent)) !== null) {
              descriptions.push(match[1]);
            }



            if (descriptions.length > 0) {
              return descriptions.join('\n');
            }

            // 如果正则匹配失败，尝试简单分割
            const fallbackDescriptions = innerContent
              .split(',')
              .map(item => item.trim().replace(/^["']|["']$/g, ''))
              .filter(item => item.length > 0);

            if (fallbackDescriptions.length > 0) {
              return fallbackDescriptions.join('\n');
            }
          }

          // 处理数组格式：["内容1","内容2","内容3"]
          if (content.startsWith('[') && content.endsWith(']')) {
            try {
              const parsed = JSON.parse(content);
              if (Array.isArray(parsed)) {

                return parsed.join('\n');
              }
            } catch (e) {


              // 手动解析数组格式
              const innerContent = content.slice(1, -1);
              const regex = /"([^"]*)"/g;
              const items = [];
              let match;

              while ((match = regex.exec(innerContent)) !== null) {
                items.push(match[1]);
              }

              if (items.length > 0) {
                return items.join('\n');
              }
            }
          }

          // 如果包含转义字符，尝试处理
          if (content.includes('\\')) {
            try {
              const unescaped = content.replace(/\\"/g, '"');

              return this.parseDescriptionContent(unescaped);
            } catch (e) {

            }
          }

          // 直接返回原字符串
          return content;
        }

        // 如果是数组，用换行符连接
        if (Array.isArray(content)) {
          return content.join('\n');
        }

        // 其他情况直接返回
        return content.toString();
      } catch (error) {
        console.error('解析商品描述失败:', error, content);
        return content || '暂无描述';
      }
    },

    // 加载商品分类
    async loadCategories() {
      try {

        const response = await getProductCategories();

        if (response && (response.data || response.rows)) {
          // 处理API返回的分类数据，支持不同的响应格式
          const rawData = response.data || response.rows;
          const apiCategories = rawData.map(item => ({
            id: item.id || item.typeId,
            name: item.name || item.typeName || item.title
          }));

            this.categories = [
              { id: 0, name: '全部商品' },
              ...apiCategories
            ];


        } else {
          console.warn('⚠️ 分类数据格式异常:', response);
        }
      } catch (error) {
        console.error('❌ 加载商品分类失败:', error);
      }
    },

    // 初始数据加载
    async loadInitialData() {
      this.isLoading = true;
      try {
        console.log('开始加载商品数据...');

        // 调用真实的商品列表API
        const response = await getProductList();

        if (response && (response.data || response.rows)) {
          // 处理API返回的商品数据，支持不同的响应格式
          const rawData = response.data || response.rows;
          this.allProducts = rawData.map(item => {
            const images = this.parseImageData(item.image);
            const descriptionImages = this.parseImageData(item.image_text);
            const description = this.parseDescriptionContent(item.detail_content);

            return {
              id: item.product_id,
              name: item.product_name,
              price: item.min_price,
              originalPrice: item.min_old_price,
              image: images[0], // 列表显示第一张
              images: images, // 保存所有图片数组
              sales: item.max_sale,
              rating: item.rating || 5.0,
              categoryId: item.category_id,
              description: description, // 解析后的描述内容
              descriptionImg: descriptionImages, // 解析后的描述图片数组
              stock: item.total_stock||item.stock_status,
              isPromotion: true,
              freeShipping: true
            };
          });

          console.log('✅ 商品数据加载成功:', this.allProducts.length, '个商品');
        } else {
          console.warn('⚠️ 商品数据格式异常:', response);
          this.allProducts = [];
        }

        this.filterAndSortProducts();
      } catch (error) {
        console.error('❌ 加载商品数据失败:', error);
        this.allProducts = [];
        uni.showToast({
          title: '数据加载失败',
          icon: 'none'
        });
      }
      this.isLoading = false;
    },

    // 搜索处理
    handleSearch() {
      if (!this.searchKey.trim()) {
        return;
      }
      this.page = 1;
      this.hasMore = true;
      this.filterAndSortProducts();
    },

    // 切换分类
    selectCategory(categoryId) {
      if (this.selectedCategory === categoryId) {
        return;
      }
      this.selectedCategory = categoryId;
      this.page = 1;
      this.hasMore = true;
      this.filterAndSortProducts();
    },

    // 切换排序
    changeSort(type) {
      if (this.sortType === type) {
        // 同一排序类型，切换排序顺序
        this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
      } else {
        this.sortType = type;
        this.sortOrder = 'desc';
      }
      this.filterAndSortProducts();
    },

    // 过滤和排序商品
    filterAndSortProducts() {
      let filtered = [...this.allProducts];
      
      // 分类过滤
      if (this.selectedCategory !== 0) {
        filtered = filtered.filter(item => item.categoryId === this.selectedCategory);
      }
      
      // 搜索过滤
      if (this.searchKey.trim()) {
        const keyword = this.searchKey.toLowerCase();
        filtered = filtered.filter(item => 
          item.name.toLowerCase().includes(keyword)
        );
      }
      
      // 排序
      filtered.sort((a, b) => {
        if (this.sortType === 'comprehensive') {
          return 0; // 综合排序可以根据具体需求实现
        } else if (this.sortType === 'sales') {
          return this.sortOrder === 'asc' ? 
            a.sales - b.sales : 
            b.sales - a.sales;
        } else if (this.sortType === 'price') {
          return this.sortOrder === 'asc' ? 
            parseFloat(a.price) - parseFloat(b.price) : 
            parseFloat(b.price) - parseFloat(a.price);
        }
      });
      
      // 分页处理
      const start = 0;
      const end = this.page * this.pageSize;
      this.displayProducts = filtered.slice(start, end);
      this.hasMore = filtered.length > end;
    },

    // 加载更多
    async loadMore() {
      if (this.isLoading || !this.hasMore) return;
      
      this.isLoading = true;
      this.page += 1;
      
      try {
        // 模拟加载延迟
        await new Promise(resolve => setTimeout(resolve, 500));
        this.filterAndSortProducts();
      } catch (error) {
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        });
      }
      
      this.isLoading = false;
    },

    // 下拉刷新
    async onRefresh() {
      this.isRefreshing = true;
      this.page = 1;
      await this.loadInitialData();
      this.isRefreshing = false;
    },

    // 添加到购物车
    addToCart(product) {
      // 检查登录状态
      if (!this.checkLoginStatus()) {
        return;
      }

      // 检查商品库存
      if (!product.stock || product.stock <= 0) {
        uni.showToast({
          title: '商品已售罄',
          icon: 'none'
        });
        return;
      }

      // 从本地存储获取现有购物车数据
      const cartItems = uni.getStorageSync('cartItems') || [];

      // 查找是否已存在相同商品（不考虑规格）
      const existItemIndex = cartItems.findIndex(item => item.id === product.id);

      if (existItemIndex > -1) {
        // 如果商品已存在，检查库存是否足够
        const currentQuantity = cartItems[existItemIndex].quantity;
        if (currentQuantity >= product.stock) {
          uni.showToast({
            title: '已达到商品库存上限',
            icon: 'none'
          });
          return;
        }
        // 增加数量
        cartItems[existItemIndex].quantity += 1;
        uni.showToast({
          title: '已更新购物车数量',
          icon: 'success'
        });
      } else {
        // 如果商品不存在，添加新商品
        cartItems.push({
          ...product,
          quantity: 1,
          selected: true, // 新添加的商品默认选中
          specs: {}, // 空规格，用户可在购物车中选择
          specText: '请选择规格' // 提示用户选择规格
        });
        uni.showToast({
          title: '已添加到购物车',
          icon: 'success'
        });
      }

      // 更新购物车状态
      uni.setStorageSync('cartItems', cartItems);
    },

    // 跳转到商品详情
    toProductDetail(product) {
      // 获取商品ID
      let productId;
      if (typeof product === 'object' && product.id) {
        productId = product.id;
      } else if (typeof product === 'string' || typeof product === 'number') {
        productId = product;
      } else {
        uni.showToast({
          title: '商品信息不存在',
          icon: 'none'
        });
        return;
      }

      console.log('🛍️ 跳转到商品详情页，ID:', productId);

      // 只传递商品ID，不传递完整数据
      uni.navigateTo({
        url: `/pages/shopping/detail/detail?id=${productId}`
      });
    },

    // 图片加载失败处理
    handleImageError(e) {
     			console.log('图片加载失败:', e)

    },



    // 检查登录状态
    checkLoginStatus() {
      const token = this.$store.getters.token;
      if (!token) {
        uni.showModal({
          title: '提示',
          content: '您还未登录，请先登录。',
          confirmText: '去登录',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              uni.redirectTo({
                url: '/pages/login'
              });
            }
          }
        });
        return false;
      }
      return true;
    }
  }
}
</script>

<style lang="scss">
.shop-container {
  min-height: 100vh;
  background-color: #f8f8f8;
}

.search-box {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #fff;
  
  .search-input {
    flex: 1;
    height: 72rpx;
    background: #f5f5f5;
    border-radius: 36rpx;
    display: flex;
    align-items: center;
    padding: 0 30rpx;
    margin-right: 20rpx;
    
    input {
      flex: 1;
      font-size: 28rpx;
      margin-left: 20rpx;
    }
  }
  
  .search-btn {
    width: 120rpx;
    height: 72rpx;
    line-height: 72rpx;
    text-align: center;
    background: #3ec6c6;
    color: #fff;
    border-radius: 36rpx;
    font-size: 28rpx;
  }
}

.main-content {
  display: flex;
  height: calc(100vh - 112rpx);
  
  .category-list {
    width: 180rpx;
    background: #fff;
    
    .category-item {
      height: 90rpx;
      line-height: 90rpx;
      text-align: center;
      font-size: 26rpx;
      color: #333;
      position: relative;
      
      &.active {
        color: #3ec6c6;
        background: #f8f8f8;
        font-weight: bold;
        
        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 6rpx;
          height: 36rpx;
          background: #3ec6c6;
          border-radius: 3rpx;
        }
      }
    }
  }
  
  .right-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    
    .sort-nav {
      display: flex;
      align-items: center;
      height: 80rpx;
      background-color: #fff;
      border-bottom: 1rpx solid #eee;
      
      .sort-item {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28rpx;
        color: #666;
        position: relative;
        
        &.active {
          color: #3ec6c6;
        }
        
        .sort-triangles {
          display: flex;
          flex-direction: column;
          margin-left: 4rpx;
          height: 24rpx;
          
          .triangle {
            width: 0;
            height: 0;
            border-left: 6rpx solid transparent;
            border-right: 6rpx solid transparent;
            margin: 2rpx 0;
            
            &.up {
              border-bottom: 6rpx solid #999;
              &.active {
                border-bottom-color: #3ec6c6;
              }
            }
            
            &.down {
              border-top: 6rpx solid #999;
              &.active {
                border-top-color: #3ec6c6;
              }
            }
          }
        }
      }
    }
    
    .product-list {
      flex: 1;
      padding: 20rpx;
      
      .product-item {
        background: #fff;
        border-radius: 16rpx;
        margin-bottom: 20rpx;
        padding: 20rpx;
        display: flex;
        
        .product-image {
          width: 200rpx;
          height: 200rpx;
          border-radius: 12rpx;
        }
        
        .product-info {
          flex: 1;
          padding: 0 20rpx;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          
          .product-name {
            font-size: 28rpx;
            color: #333;
            line-height: 1.4;
          }
          
          .product-tags {
            margin: 10rpx 0;
            
            .tag {
              display: inline-block;
              padding: 4rpx 12rpx;
              background: rgba(62, 198, 198, 0.1);
              color: #3ec6c6;
              font-size: 22rpx;
              border-radius: 8rpx;
              margin-right: 12rpx;
            }

            .stock-tag {
              background: rgba(153, 153, 153, 0.1);
              color: #999;
            }

            .low-stock {
              background: rgba(255, 85, 85, 0.1);
              color: #ff5555;
            }
          }
          
          .price-box {
            display: flex;
            align-items: center;
            justify-content: space-between;
            
            .price-info {
              display: flex;
              align-items: baseline;
              
              .current-price {
                font-size: 36rpx;
                color: #ff5555;
                font-weight: bold;
              }
              
              .original-price {
                font-size: 24rpx;
                color: #999;
                text-decoration: line-through;
                margin-left: 12rpx;
              }
            }
            
            .cart-btn {
              width: 60rpx;
              height: 60rpx;
              background: #3ec6c6;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
        }
      }
    }
  }
}

.loading-more, .no-more {
  text-align: center;
  padding: 20rpx 0;
  color: #999;
  font-size: 24rpx;
  
  .uni-icons {
    margin-right: 8rpx;
  }
}
</style>