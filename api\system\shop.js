import request from '@/utils/request'

// 获取商品列表
export function getProductList() {
  return request({
    url: '/system/info/list',
    method: 'get',
  })
}

// 获取商品分类
export function getProductCategories() {
  return request({
    url: '/system/info/listtype',
    method: 'get'
  })
}

// 获取商品详情
export function getProductDetail(id) {
  return request({
    url: '/system/info/listone',
    method: 'get',
    params: {
      id: id
    }
  })
}

// 获取商品规格最小单位
export function getProductSpec(variantid) {
  return request({
    url: '/system/info/listlistnewSKU',
    method: 'get',
    params: {
      variantid: variantid
    }
  })
}


// 搜索商品
export function searchProducts(params) {
  return request({
    url: '/system/product/search',
    method: 'get',
    params
  })
}

// 获取商品评价
export function getProductReviews(id, params) {
  return request({
    url: `/system/product/${id}/reviews`,
    method: 'get',
    params
  })
}

// 添加商品评价
export function addProductReview(data) {
  return request({
    url: '/system/product/review',
    method: 'post',
    data
  })
}

// 获取购物车列表
export function getCartList() {
  return request({
    url: '/system/cart/list',
    method: 'get'
  })
}

// 添加到购物车
export function addToCart(data) {
  return request({
    url: '/system/cart/add',
    method: 'post',
    data
  })
}

// 更新购物车商品数量
export function updateCartQuantity(data) {
  return request({
    url: '/system/cart/update',
    method: 'put',
    data
  })
}

// 删除购物车商品
export function removeFromCart(ids) {
  return request({
    url: '/system/cart/remove',
    method: 'delete',
    data: { ids }
  })
}

// 清空购物车
export function clearCart() {
  return request({
    url: '/system/cart/clear',
    method: 'delete'
  })
}

// 创建订单
export function createOrder(data) {
  return request({
    url: '/system/order/create',
    method: 'post',
    data
  })
}

// 获取订单列表
export function getOrderList(params) {
  return request({
    url: '/system/order/list',
    method: 'get',
    params
  })
}

// 获取订单详情
export function getOrderDetail(id) {
  return request({
    url: `/system/order/detail/${id}`,
    method: 'get'
  })
}

// 取消订单
export function cancelOrder(id) {
  return request({
    url: `/system/order/${id}/cancel`,
    method: 'put'
  })
}

// 确认收货
export function confirmOrder(id) {
  return request({
    url: `/system/order/${id}/confirm`,
    method: 'put'
  })
}

// 申请退款
export function applyRefund(data) {
  return request({
    url: '/system/order/refund',
    method: 'post',
    data
  })
}
