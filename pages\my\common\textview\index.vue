<template>
	<view class="text-view-container">

		<view class="content-wrapper">
			<!-- 问的区域 -->
			<view class="question-card">
				<view class="question-icon">问</view>
				<text class="question-text">{{ pageTitle }}</text>
			</view>

			<!-- 答的区域 -->
			<view class="answer-card" @click="copyAnswer">
				<view class="answer-icon">答</view>
				<view class="answer-content">
					<text v-for="(part, index) in parsedContent" :key="index">
						<text v-if="part.type === 'text'">{{ part.value }}</text>
						<text v-else-if="part.type === 'highlight'" class="highlight-text">{{ part.value }}</text>
					</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import uniNavBar from '@/uni_modules/uni-nav-bar/components/uni-nav-bar/uni-nav-bar.vue';

	export default {
		components: {
			uniNavBar
		},
		data() {
			return {
				pageTitle: '',
				rawContent: '',
				parsedContent: [] // 用于存储解析后的内容
			}
		},
		onLoad(options) {
			this.pageTitle = options.title || '详情';
			this.rawContent = options.content || '无内容';
			this.parseContent();
		},
		methods: {
			onClickLeft() {
				uni.navigateBack();
			},
			parseContent() {
				const content = this.rawContent;
				const regex = /(【[^】]+】)/g; // 匹配【】内的内容
				let lastIndex = 0;
				let match;
				const result = [];

				while ((match = regex.exec(content)) !== null) {
					// 添加普通文本部分
					if (match.index > lastIndex) {
						result.push({
							type: 'text',
							value: content.substring(lastIndex, match.index)
						});
					}
					// 添加高亮文本部分
					result.push({
						type: 'highlight',
						value: match[0]
					});
					lastIndex = regex.lastIndex;
				}

				// 添加剩余的普通文本部分
				if (lastIndex < content.length) {
					result.push({
						type: 'text',
						value: content.substring(lastIndex)
					});
				}
				this.parsedContent = result.filter(part => part.value.trim() !== '');
			},
			copyAnswer() {
				uni.setClipboardData({
					data: this.rawContent,
					success: function () {
						uni.showToast({
							title: '回答内容已复制',
							icon: 'success',
							duration: 1500
						});
					},
					fail: function (err) {
						console.error('复制失败', err);
						uni.showToast({
							title: '复制失败',
							icon: 'none',
							duration: 1500
						});
					}
				});
			}
		}
	}
</script>

<style lang="scss">
	page {
		background-color: #f8f8f8;
	}

	.text-view-container {
		min-height: 100vh;
		background-color: #f8f8f8;
	}

	.content-wrapper {
		padding: 40rpx;
	}

	.question-card, .answer-card {
		background-color: #fff;
		border-radius: 20rpx;
		padding: 40rpx;
		margin-bottom: 40rpx;
		box-shadow: 0 6px 15px rgba(0,0,0,0.08);
		display: flex;
		align-items: flex-start;
	}

	.question-icon, .answer-icon {
		width: 70rpx;
		height: 70rpx;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #fff;
		font-weight: bold;
		font-size: 36rpx;
		margin-right: 25rpx;
		flex-shrink: 0;
	}

	.question-icon {
		background-color: #f3a73f;
	}

	.answer-icon {
		background-color: #3ec6c6;
	}

	.question-text {
		flex: 1;
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
		line-height: 1.6;
	}

	.answer-content {
		flex: 1;
		font-size: 32rpx;
		color: #555;
		line-height: 1.8;
	}

	.highlight-text {
		font-weight: bold;
		color: #3ec6c6;
		margin-right: 8rpx;
		font-size: 32rpx;
	}
</style>
