<template>
  <view class="psychology-container">
    <!-- 主要功能区域 -->
    <view class="main-content">
      <!-- 抑郁评测卡片 -->
      <view class="function-card depression-card" @click="navigateToDepression">
        <view class="card-content">
          <view class="icon-wrapper icon-depression">
            <text class="iconfontB icon-xinlipingce card-icon"></text>
          </view>
          <view class="card-text">
            <view class="card-title">抑郁评测</view>
            <view class="card-subtitle">专业抑郁自评量表，了解心理状态</view>
          </view>
        </view>
      </view>

      <!-- 焦虑评测卡片 -->
      <view class="function-card anxiety-card" @click="navigateToAnxiety">
        <view class="card-content">
          <view class="icon-wrapper icon-anxiety">
            <text class="iconfontB icon-jiaolv card-icon"></text>
          </view>
          <view class="card-text">
            <view class="card-title">焦虑评测</view>
            <view class="card-subtitle">焦虑自评量表，评估焦虑程度</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部说明文字 -->
    <view class="bottom-description">
      <text class="description-text">
        为诊疗和科研，采集您的四诊信息。数据将匿名化处理并加密，仅用于医疗分析，助力精准诊断，提升治疗效果。
      </text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {

    }
  },
  methods: {
    // 跳转到抑郁评测页面
    navigateToDepression() {
      console.log('跳转到抑郁评测')
      uni.navigateTo({
        url: '/pages/heart/heart/heart'
      })
    },

    // 跳转到焦虑评测页面
    navigateToAnxiety() {
      console.log('跳转到焦虑评测')
      uni.navigateTo({
        url: '/pages/heart/heart/anxiety'
      })
    },

  }
}
</script>

<style lang="scss" scoped>
.psychology-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f6fcfd 60%, #eafcff 100%);
  display: flex;
  flex-direction: column;
  padding: 60rpx 30rpx 60rpx;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 50rpx;
}

.function-card {
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.function-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(90deg, #ff6b6b 0%, #ff8e8e 100%);
}

.function-card:active {
  transform: translateY(4rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.12);
}

.depression-card::before {
  background: linear-gradient(90deg, #ff6b6b 0%, #ff8e8e 100%);
}

.anxiety-card::before {
  background: linear-gradient(90deg, #ffa726 0%, #ffcc80 100%);
}

.card-content {
  display: flex;
  align-items: center;
  gap: 30rpx;
}

.icon-wrapper {
  width: 100rpx;
  height: 100rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.icon-depression {
  background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
}

.icon-anxiety {
  background: linear-gradient(135deg, #ffa726 0%, #ffcc80 100%);
}

.card-icon {
  font-size: 55rpx;
  color: #fff;
}

.card-text {
  flex: 1;
}

.card-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 12rpx;
  letter-spacing: 1rpx;
}

.card-subtitle {
  font-size: 28rpx;
  color: #7f8c8d;
  line-height: 1.5;
}

.bottom-description {
  margin-top: 60rpx;
  padding: 30rpx 20rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  backdrop-filter: blur(10rpx);
}

.description-text {
  font-size: 24rpx;
  color: #7f8c8d;
  line-height: 1.6;
  text-align: center;
  letter-spacing: 0.5rpx;
}
</style>