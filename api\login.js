import request from '@/utils/request'

// 登录方法
export function login(/* company, phonenumber, */username, password, code, uuid) {
  const data = {
	/* company,
	phonenumber, */
    username,
    password,
    code,
    uuid,
	type:1
  }
  return request({
    'url': '/login',
    headers: {
      isToken: false
    },
    'method': 'post',
    'data': data
  })
}

// 注册方法
export function register(data) {
  return request({
    url: '/register',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}

// 获取用户详细信息
export function getInfo() {
  return request({
    'url': '/getInfo',
    'method': 'get'
  })
}

// 退出方法
export function logout() {
  return request({
    'url': '/logout',
    'method': 'post'
  })
}

// 获取验证码
export function getCodeImg() {
  return request({
    'url': '/captchaImage',
    headers: {
      isToken: false
    },
    method: 'get',
    timeout: 20000
  })
}

//获取忘记密码的验证码
export function getCode(data) {
  return request({
    'url': '/login/Retrieval',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}

//验证并修改新密码
export function updatePassword(data) {
  return request({
    'url': '/login/password',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}
