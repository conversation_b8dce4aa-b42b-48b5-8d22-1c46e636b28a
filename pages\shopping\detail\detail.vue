<template>
  <view class="detail-page">
    <shop-detail :product-id="productId"></shop-detail>
  </view>
</template>

<script>
import ShopDetail from '@/pages/shopping/components/shopDetail.vue'

export default {
  components: {
    ShopDetail
  },
  data() {
    return {
      productId: ''
    }
  },
  onLoad(options) {
    this.productId = options.id || '1';
  }
}
</script>

<style lang="scss" scoped>
.detail-page {
  min-height: 100vh;
}
</style>
