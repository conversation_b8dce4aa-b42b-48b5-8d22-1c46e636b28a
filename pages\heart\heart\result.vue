<template>
	<view class="report-container">
		<!-- 头部区域 -->
		<view class="header-section">
			<view class="report-title">心理健康评测报告</view>
			<view class="test-date" v-if="testTime">测试时间：{{ formatDate(testTime) }}</view>
			<view class="report-subtitle">本报告基于焦虑自评量表(SAS)，仅供参考，如有疑虑请咨询专业医师。</view>
		</view>

		<!-- 加载状态 -->
		<view v-if="loading" class="loading">
			<view class="loading-icon"></view>
			<view class="loading-text">分析中...</view>
		</view>

		<!-- 报告内容 -->
		<view v-else class="content-section">
			<!-- 总分显示 -->
			<view class="score-section">
				<view class="section-title">
					<view class="section-bar"></view>
					<view class="section-text">评测结果</view>
				</view>
				<view class="score-card">
					<view class="score-number">{{ standardScore }}</view>
					<view class="score-label">总分</view>
					<view class="level-result" :class="levelClass">{{ level }}</view>
				</view>
			</view>

			<!-- 结果解读 -->
			<view class="interpretation-section">
				<view class="section-title">
					<view class="section-bar"></view>
					<view class="section-text">结果解读</view>
				</view>
				<view class="interpretation-content">
					<view class="level-description">
						<text class="description-text">{{ description }}{{ suggestion ? '，建议' + suggestion : '' }}</text>
					</view>
					<view class="score-range">
						<text class="range-text">评分范围说明：</text>
						<view class="range-list">
							<view class="range-item">
								<view class="range-score">小于50分</view>
								<view class="range-desc">正常范围</view>
							</view>
							<view class="range-item">
								<view class="range-score">50-59分</view>
								<view class="range-desc">轻度焦虑</view>
							</view>
							<view class="range-item">
								<view class="range-score">60-69分</view>
								<view class="range-desc">中度焦虑</view>
							</view>
							<view class="range-item">
								<view class="range-score">≥70分</view>
								<view class="range-desc">重度焦虑</view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 免责声明 -->
			<view class="disclaimer-section">
				<view class="disclaimer-title">
					<view class="disclaimer-bar"></view>
					免责声明
				</view>
				<view class="disclaimer-content">
					<text class="disclaimer-text">
						本评测结果仅供参考，不能替代专业医师的诊断。如果您感到持续的情绪困扰，建议及时寻求专业心理健康服务。
					</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				loading: false,
				standardScore: 0,
				level: '',
				description: '',
				suggestion: '',
				testTime: ''
			}
		},

		computed: {
			// 获取等级样式类
			levelClass() {
				switch (this.level) {
					case '正常范围': return 'level-normal'
					case '轻度焦虑': return 'level-mild'
					case '中度焦虑': return 'level-moderate'
					case '重度焦虑': return 'level-severe'
					default: return 'level-normal'
				}
			}
		},

		onLoad(options) {
			if (options.data) {
				try {
					const resultData = JSON.parse(decodeURIComponent(options.data))
					console.log('焦虑评测结果数据:', resultData)

					this.standardScore = resultData.standardScore || 0
					this.testTime = resultData.testTime || ''

					// 根据标准分计算焦虑程度
					this.calculateAnxietyLevel()
				} catch (error) {
					console.error('解析焦虑评测数据失败:', error)
					uni.showToast({
						title: '数据格式错误',
						icon: 'none'
					})
				}
			}
		},

		methods: {
			// 根据标准分计算焦虑程度
			calculateAnxietyLevel() {
				const score = this.standardScore;

				if (score < 50) {
					this.level = '正常范围';
					this.description = '无明显焦虑情绪，日常状态稳定';
					this.suggestion = '维持当前状态，关注压力管理';
				} else if (score >= 50 && score <= 59) {
					this.level = '轻度焦虑';
					this.description = '偶尔紧张/失眠，症状轻微且可自行缓解';
					this.suggestion = '学习放松技巧（如腹式呼吸、正念），增加运动';
				} else if (score >= 60 && score <= 69) {
					this.level = '中度焦虑';
					this.description = '持续紧张、躯体不适，影响工作学习效率';
					this.suggestion = '结合心理咨询（如认知行为疗法）';
				} else { // score >= 70
					this.level = '重度焦虑';
					this.description = '严重躯体症状（颤抖/呼吸困难/失眠），社会功能明显受损';
					this.suggestion = '立即就医，药物与心理治疗联合干预';
				}
			},

			// 格式化日期
			formatDate(dateStr) {
				if (!dateStr) return ''

				try {
					let date

					// 处理不同的时间格式
					if (typeof dateStr === 'string') {
						// 检查是否是ISO格式 (如: 2025-07-07T15:15:49.000+08:00)
						if (dateStr.includes('T') && (dateStr.includes('+') || dateStr.includes('Z'))) {
							date = new Date(dateStr)
						}
						// 检查是否已经是我们想要的格式 (如: 2025-07-07 15:15)
						else if (/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}$/.test(dateStr)) {
							return dateStr
						}
						// 其他字符串格式，尝试解析
						else {
							date = new Date(dateStr)
						}
					} else if (typeof dateStr === 'number') {
						// 时间戳
						date = new Date(dateStr)
					} else {
						// 其他类型，尝试转换
						date = new Date(dateStr)
					}

					// 检查日期是否有效
					if (isNaN(date.getTime())) {
						console.warn('无效的时间格式:', dateStr)
						return this.getCurrentTime()
					}

					// 手动格式化时间，避免兼容性问题
					const year = date.getFullYear()
					const month = String(date.getMonth() + 1).padStart(2, '0')
					const day = String(date.getDate()).padStart(2, '0')
					const hours = String(date.getHours()).padStart(2, '0')
					const minutes = String(date.getMinutes()).padStart(2, '0')

					return `${year}-${month}-${day} ${hours}:${minutes}`
				} catch (error) {
					console.error('时间格式化失败:', error, '原始值:', dateStr)
					return this.getCurrentTime()
				}
			},

			// 获取当前时间
			getCurrentTime() {
				const now = new Date()
				const year = now.getFullYear()
				const month = String(now.getMonth() + 1).padStart(2, '0')
				const day = String(now.getDate()).padStart(2, '0')
				const hours = String(now.getHours()).padStart(2, '0')
				const minutes = String(now.getMinutes()).padStart(2, '0')
				return `${year}-${month}-${day} ${hours}:${minutes}`
			}
		}
	}
</script>

<style scoped>
.report-container {
	min-height: 100vh;
	background: #f5f7fa;
	padding: 30rpx;
	padding-bottom: 120rpx;
}

/* 头部区域 */
.header-section {
	background: #ffffff;
	border-radius: 24rpx;
	padding: 40rpx 30rpx;
	margin-bottom: 30rpx;
	text-align: left;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.report-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 20rpx;
	position: relative;
	display: inline-block;
	margin-right: 20rpx;
}

.test-date {
	font-size: 24rpx;
	color: #666666;
	background: #f8f9fa;
	padding: 8rpx 20rpx;
	border-radius: 10rpx;
	display: inline-block;
	margin-bottom: 16rpx;
	border: 1rpx solid #eee;
	vertical-align: middle;
}

.report-subtitle {
	font-size: 26rpx;
	color: #666666;
	line-height: 1.6;
	padding: 16rpx 24rpx;
	background: rgba(35, 174, 244, 0.05);
	border-radius: 12rpx;
	/* border-left: 4rpx solid #23aef4; */
}

	/* 加载状态 */
	.loading {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 100rpx 0;
	}

	.loading-icon {
		width: 60rpx;
		height: 60rpx;
		border: 4rpx solid #e0e0e0;
		border-top: 4rpx solid #ffa726;
		border-radius: 50%;
		animation: spin 1s linear infinite;
		margin-bottom: 20rpx;
	}

	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}

	.loading-text {
		font-size: 28rpx;
		color: #666;
	}

	/* 内容区域 */
	.content-section {
		display: flex;
		flex-direction: column;
		gap: 30rpx;
	}

	/* 通用区域样式 */
	.score-section,
	.interpretation-section,
	.disclaimer-section {
		background: #ffffff;
		border-radius: 24rpx;
		padding: 40rpx 30rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	}

	.section-title {
		display: flex;
		align-items: center;
		margin-bottom: 30rpx;
	}

	.section-bar {
		width: 8rpx;
		height: 32rpx;
		background: linear-gradient(135deg, #ffa726 0%, #ffcc80 100%);
		border-radius: 4rpx;
		margin-right: 16rpx;
	}

	.section-text {
		font-size: 32rpx;
		font-weight: 600;
		color: #2c3e50;
	}

	/* 分数卡片 */
	.score-card {
		background: linear-gradient(135deg, #ffa726 0%, #ffcc80 100%);
		border-radius: 20rpx;
		padding: 50rpx 40rpx;
		text-align: center;
		position: relative;
	}

	.score-number {
		font-size: 120rpx;
		font-weight: 700;
		color: #fff;
		line-height: 1;
		margin-bottom: 12rpx;
	}

	.score-label {
		font-size: 28rpx;
		color: #fff;
		font-weight: 500;
		margin-bottom: 30rpx;
	}

	.level-result {
		background: rgba(255, 255, 255, 0.2);
		backdrop-filter: blur(10rpx);
		font-size: 32rpx;
		font-weight: 600;
		padding: 16rpx 32rpx;
		border-radius: 50rpx;
		color: #fff;
		display: inline-block;
	}

	/* 移除等级颜色区分，统一使用蓝色主题 */

	/* 解读内容 */
.interpretation-section {
	background: #ffffff;
	border-radius: 24rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.interpretation-content {
	line-height: 1.6;
}

.level-description {
	background: rgba(35, 174, 244, 0.05);
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 30rpx;
	/* border-left: 4rpx solid #23aef4; */
}

.description-text {
	font-size: 28rpx;
	color: #333333;
	line-height: 1.8;
}

.range-text {
	font-size: 28rpx;
	color: #333333;
	font-weight: 600;
	margin-bottom: 20rpx;
	display: block;
}

.range-list {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}
.range-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 24rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	transition: all 0.3s ease;
}

.range-item:hover {
	transform: translateX(4rpx);
	background: #f0f2f5;
}

	.range-score {
		font-size: 26rpx;
	    font-weight: 600;
	    color: #23aef4;
	    background: rgba(35, 174, 244, 0.1);
	    padding: 6rpx 16rpx;
	    border-radius: 20rpx;
	}

	.range-desc {
		font-size: 28rpx;
	    color: #666666;
	}
/* 免责声明 */
.disclaimer-section {
	background: #ffffff;
	border-radius: 24rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.disclaimer-title {
	font-size: 30rpx;
	font-weight: 600;
	color: #ff6b6b;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
}

.disclaimer-bar {
	width: 6rpx;
	height: 32rpx;
	background: #ff6b6b;
	border-radius: 3rpx;
	margin-right: 16rpx;
}

.disclaimer-content {
	background: #fff8f8;
	border-radius: 16rpx;
	padding: 24rpx;
	border: 1rpx solid rgba(255, 107, 107, 0.1);
	border-left: 4rpx solid #ff6b6b;
}

.disclaimer-text {
	font-size: 26rpx;
	color: #666666;
	line-height: 1.8;
}
/* 响应式设计 */
@media (max-width: 750px) {
	.report-container {
		padding: 20rpx;
	}

	.score-card {
		flex-direction: column;
		text-align: center;
		gap: 24rpx;
		padding: 30rpx;
	}

	.score-info {
		text-align: center;
	}

	.score-number {
		font-size: 100rpx;
	}

	.range-item {
		flex-direction: column;
		text-align: center;
		gap: 12rpx;
		padding: 20rpx;
	}

	.range-score {
		margin-bottom: 8rpx;
	}
}
</style>