<template>
	<view class="container">
		<!-- 温馨提示 -->
		<view class="slogan-text">关爱心理健康，关注焦虑情绪</view>

		<view class="description">请根据过去一周的实际体验，选择最符合您情况的选项。</view>

		<!-- 加载状态 -->
		<view v-if="loading" class="loading-container">
			<view class="loading-text">正在加载评测问题...</view>
		</view>

		<!-- 问题列表 -->
		<view v-else class="question-list">
			<view v-for="(question, index) in questions" :key="question.id" class="question-item">
				<view class="question-text">{{ question.id }}. {{ question.text }}</view>
				<radio-group @change="radioChange($event, question.id)" class="options-group">
					<label v-for="(option, optIndex) in question.options" :key="optIndex" class="uni-list-cell uni-list-cell-pd">
						<view>
							<radio :value="(option.value || 0).toString()" :checked="option.value === question.selected" />
						</view>
						<view>{{ option.label || '' }}</view>
					</label>
				</radio-group>
			</view>
		</view>

		<button @click="submitAnswers" class="submit-button">提交评测</button>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				questions: [], // 焦虑评测问题数据
				loading: true, // 数据加载状态
				questionsLoaded: false, // 题库是否加载完成
				result: null
			};
		},

		async onLoad() {
			// 加载焦虑评测问题数据
			await this.loadQuestions()
			// 页面加载时检查用户信息
			this.checkAndShowUserInfo()
		},

		methods: {
			// 加载焦虑评测问题数据
			async loadQuestions() {
				try {
					this.loading = true
					console.log('开始加载焦虑评测问题数据...')

					// 焦虑自评量表(SAS)完整20题
					const sasQuestions = [
						{
							id: 1,
							text: "我感到比平常更加紧张和焦虑",
							options: [
								{ label: "A. 没有或很少时间", value: 1 },
								{ label: "B. 有时有", value: 2 },
								{ label: "C. 大部分时间有", value: 3 },
								{ label: "D. 绝大部分或全部时间", value: 4 }
							],
							selected: 0,
							reverseScored: false
						},
						{
							id: 2,
							text: "我无缘无故地感到害怕",
							options: [
								{ label: "A. 没有或很少时间", value: 1 },
								{ label: "B. 有时有", value: 2 },
								{ label: "C. 大部分时间有", value: 3 },
								{ label: "D. 绝大部分或全部时间", value: 4 }
							],
							selected: 0,
							reverseScored: false
						},
						{
							id: 3,
							text: "我容易心里烦乱或觉得惊恐",
							options: [
								{ label: "A. 没有或很少时间", value: 1 },
								{ label: "B. 有时有", value: 2 },
								{ label: "C. 大部分时间有", value: 3 },
								{ label: "D. 绝大部分或全部时间", value: 4 }
							],
							selected: 0,
							reverseScored: false
						},
						{
							id: 4,
							text: "我觉得我可能将要发疯",
							options: [
								{ label: "A. 没有或很少时间", value: 1 },
								{ label: "B. 有时有", value: 2 },
								{ label: "C. 大部分时间有", value: 3 },
								{ label: "D. 绝大部分或全部时间", value: 4 }
							],
							selected: 0,
							reverseScored: false
						},
						{
							id: 5,
							text: "我觉得一切都很好，也不会发生什么不幸",
							options: [
								{ label: "A. 没有或很少时间", value: 1 },
								{ label: "B. 有时有", value: 2 },
								{ label: "C. 大部分时间有", value: 3 },
								{ label: "D. 绝大部分或全部时间", value: 4 }
							],
							selected: 0,
							reverseScored: true // 反向计分题
						},
						{
							id: 6,
							text: "我手脚发抖打颤",
							options: [
								{ label: "A. 没有或很少时间", value: 1 },
								{ label: "B. 有时有", value: 2 },
								{ label: "C. 大部分时间有", value: 3 },
								{ label: "D. 绝大部分或全部时间", value: 4 }
							],
							selected: 0,
							reverseScored: false
						},
						{
							id: 7,
							text: "我因为头痛、颈痛和背痛而苦恼",
							options: [
								{ label: "A. 没有或很少时间", value: 1 },
								{ label: "B. 有时有", value: 2 },
								{ label: "C. 大部分时间有", value: 3 },
								{ label: "D. 绝大部分或全部时间", value: 4 }
							],
							selected: 0,
							reverseScored: false
						},
						{
							id: 8,
							text: "我感觉容易衰弱和疲乏",
							options: [
								{ label: "A. 没有或很少时间", value: 1 },
								{ label: "B. 有时有", value: 2 },
								{ label: "C. 大部分时间有", value: 3 },
								{ label: "D. 绝大部分或全部时间", value: 4 }
							],
							selected: 0,
							reverseScored: false
						},
						{
							id: 9,
							text: "我觉得心平气和，并且容易安静坐着",
							options: [
								{ label: "A. 没有或很少时间", value: 1 },
								{ label: "B. 有时有", value: 2 },
								{ label: "C. 大部分时间有", value: 3 },
								{ label: "D. 绝大部分或全部时间", value: 4 }
							],
							selected: 0,
							reverseScored: true // 反向计分题
						},
						{
							id: 10,
							text: "我觉得心跳很快",
							options: [
								{ label: "A. 没有或很少时间", value: 1 },
								{ label: "B. 有时有", value: 2 },
								{ label: "C. 大部分时间有", value: 3 },
								{ label: "D. 绝大部分或全部时间", value: 4 }
							],
							selected: 0,
							reverseScored: false
						},
						{
							id: 11,
							text: "我因为一阵阵头晕而苦恼",
							options: [
								{ label: "A. 没有或很少时间", value: 1 },
								{ label: "B. 有时有", value: 2 },
								{ label: "C. 大部分时间有", value: 3 },
								{ label: "D. 绝大部分或全部时间", value: 4 }
							],
							selected: 0,
							reverseScored: false
						},
						{
							id: 12,
							text: "我有晕倒发作或觉得要晕倒似的",
							options: [
								{ label: "A. 没有或很少时间", value: 1 },
								{ label: "B. 有时有", value: 2 },
								{ label: "C. 大部分时间有", value: 3 },
								{ label: "D. 绝大部分或全部时间", value: 4 }
							],
							selected: 0,
							reverseScored: false
						},
						{
							id: 13,
							text: "我呼气吸气都感到很容易",
							options: [
								{ label: "A. 没有或很少时间", value: 1 },
								{ label: "B. 有时有", value: 2 },
								{ label: "C. 大部分时间有", value: 3 },
								{ label: "D. 绝大部分或全部时间", value: 4 }
							],
							selected: 0,
							reverseScored: true // 反向计分题
						},
						{
							id: 14,
							text: "我手脚麻木和刺痛",
							options: [
								{ label: "A. 没有或很少时间", value: 1 },
								{ label: "B. 有时有", value: 2 },
								{ label: "C. 大部分时间有", value: 3 },
								{ label: "D. 绝大部分或全部时间", value: 4 }
							],
							selected: 0,
							reverseScored: false
						},
						{
							id: 15,
							text: "我因为胃痛和消化不良而苦恼",
							options: [
								{ label: "A. 没有或很少时间", value: 1 },
								{ label: "B. 有时有", value: 2 },
								{ label: "C. 大部分时间有", value: 3 },
								{ label: "D. 绝大部分或全部时间", value: 4 }
							],
							selected: 0,
							reverseScored: false
						},
						{
							id: 16,
							text: "我常常要小便",
							options: [
								{ label: "A. 没有或很少时间", value: 1 },
								{ label: "B. 有时有", value: 2 },
								{ label: "C. 大部分时间有", value: 3 },
								{ label: "D. 绝大部分或全部时间", value: 4 }
							],
							selected: 0,
							reverseScored: false
						},
						{
							id: 17,
							text: "我的手常常是干燥温暖的",
							options: [
								{ label: "A. 没有或很少时间", value: 1 },
								{ label: "B. 有时有", value: 2 },
								{ label: "C. 大部分时间有", value: 3 },
								{ label: "D. 绝大部分或全部时间", value: 4 }
							],
							selected: 0,
							reverseScored: true // 反向计分题
						},
						{
							id: 18,
							text: "我脸红发热",
							options: [
								{ label: "A. 没有或很少时间", value: 1 },
								{ label: "B. 有时有", value: 2 },
								{ label: "C. 大部分时间有", value: 3 },
								{ label: "D. 绝大部分或全部时间", value: 4 }
							],
							selected: 0,
							reverseScored: false
						},
						{
							id: 19,
							text: "我容易入睡并且一夜睡得很好",
							options: [
								{ label: "A. 没有或很少时间", value: 1 },
								{ label: "B. 有时有", value: 2 },
								{ label: "C. 大部分时间有", value: 3 },
								{ label: "D. 绝大部分或全部时间", value: 4 }
							],
							selected: 0,
							reverseScored: true // 反向计分题
						},
						{
							id: 20,
							text: "我做恶梦",
							options: [
								{ label: "A. 没有或很少时间", value: 1 },
								{ label: "B. 有时有", value: 2 },
								{ label: "C. 大部分时间有", value: 3 },
								{ label: "D. 绝大部分或全部时间", value: 4 }
							],
							selected: 0,
							reverseScored: false
						}
					]

					this.questions = sasQuestions
					this.questionsLoaded = true // 标记题库加载完成
					console.log('成功加载焦虑评测问题数据:', this.questions)

				} catch (error) {
					console.error('加载焦虑评测问题数据失败:', error)
					uni.showToast({
						title: '加载问题失败，请检查网络连接',
						icon: 'none',
						duration: 3000
					})
					this.questions = []
				} finally {
					this.loading = false
				}
			},

			radioChange(evt, questionId) {
				const question = this.questions.find(q => q.id === questionId);
				if (question) {
					question.selected = parseInt(evt.detail.value);
				}
			},

			// 检查用户信息
			checkAndShowUserInfo() {
				console.log('焦虑评测页面 - 检查用户信息')
				// 检查用户信息逻辑（与抑郁评测类似）
				if (this.checkUserInfo()) {
					console.log('用户信息完整，可以开始评测')
				}
			},

			// 检查用户信息是否完善
			checkUserInfo() {
				try {
					const userInfo = uni.getStorageSync('userInfo') || {}
					const requiredFields = [
						{ key: 'name', label: '姓名' },
						{ key: 'sex', label: '性别' },
						{ key: 'dateBirth', label: '出生年月' },
						{ key: 'phonenumber', label: '手机号' },
						{ key: 'height', label: '身高' },
						{ key: 'weight', label: '体重' }
					]

					const missingFields = []
					requiredFields.forEach(field => {
						if (!userInfo[field.key] || userInfo[field.key].toString().trim() === '') {
							missingFields.push(field.label)
						}
					})

					if (missingFields.length > 0) {
						this.showUserInfoModal(missingFields)
						return false
					}

					return true
				} catch (error) {
					console.error('检查用户信息失败:', error)
					this.showUserInfoModal(['基本信息'])
					return false
				}
			},

			// 显示用户信息完善提示弹窗
			showUserInfoModal(missingFields) {
				const fieldText = missingFields.length > 3
					? '基本信息'
					: missingFields.join('、')

				uni.showModal({
					title: '信息不完整',
					content: `为了更好的焦虑评测，请先完善${fieldText}等信息`,
					confirmText: '去完善',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							uni.navigateTo({
								url: '/pages/my/healthInput/analyse?from=anxiety'
							})
						} else {
							uni.navigateBack({
								delta: 1
							})
						}
					}
				})
			},

			submitAnswers() {
				// 防止题库未加载完成时误提交
				if (!this.questionsLoaded) {
					console.log('题库未加载完成，阻止提交');
					return;
				}

				// 检查是否所有问题都已回答
				let allAnswered = true;
				for (const question of this.questions) {
					if (question.selected === 0) {
						allAnswered = false;
						break;
					}
				}

				if (!allAnswered) {
					uni.showToast({
						title: '请回答所有问题！',
						icon: 'none'
					});
					return;
				}

				// 按照SAS量表计分规则计算
				this.calculateSASScore();
			},

			// 计算SAS焦虑自评量表分数
			calculateSASScore() {
				let rawScore = 0; // 粗分

				// 反向计分题目：第5、9、13、17、19题
				const reverseQuestions = [5, 9, 13, 17, 19];

				console.log('=== SAS焦虑评测计分详情 ===');

				for (const question of this.questions) {
					let questionScore = question.selected;
					const isReverse = reverseQuestions.includes(question.id);

					// 反向计分：1->4, 2->3, 3->2, 4->1
					if (isReverse) {
						questionScore = 5 - question.selected;
					}

					console.log(`题目${question.id}: 选择=${question.selected}, ${isReverse ? '反向' : '正向'}, 得分=${questionScore}`);
					rawScore += questionScore;
				}

				// 标准分 = 粗分 × 1.25（四舍五入取整）
				const standardScore = Math.round(rawScore * 1.25);

				console.log(`粗分: ${rawScore}, 标准分: ${standardScore}`);
				console.log('=== SAS计分详情结束 ===');

				// 根据标准分判断焦虑程度
				let level = '';
				let description = '';
				let suggestion = '';

				if (standardScore < 50) {
					level = '正常范围';
					description = '无明显焦虑情绪，日常状态稳定';
					suggestion = '维持当前状态，关注压力管理';
				} else if (standardScore >= 50 && standardScore <= 59) {
					level = '轻度焦虑';
					description = '偶尔紧张/失眠，症状轻微且可自行缓解';
					suggestion = '学习放松技巧（如腹式呼吸、正念），增加运动';
				} else if (standardScore >= 60 && standardScore <= 69) {
					level = '中度焦虑';
					description = '持续紧张、躯体不适，影响工作学习效率';
					suggestion = '结合心理咨询（如认知行为疗法）';
				} else { // standardScore >= 70
					level = '重度焦虑';
					description = '严重躯体症状（颤抖/呼吸困难/失眠），社会功能明显受损';
					suggestion = '立即就医，药物与心理治疗联合干预';
				}

				this.result = {
					rawScore,
					standardScore,
					level,
					description,
					suggestion,
					testTime: this.getCurrentTime()
				};

				// 跳转到结果页面
				this.navigateToResult();
			},

			// 跳转到焦虑评测结果页面
			navigateToResult() {
				if (!this.result) {
					uni.showToast({
						title: '评测结果异常',
						icon: 'none'
					})
					return
				}

				try {
					// 只传递标准分数
					const resultData = {
						standardScore: this.result.standardScore,
						testTime: this.result.testTime
					}

					// 跳转到结果页面
					const dataStr = encodeURIComponent(JSON.stringify(resultData))
					uni.navigateTo({
						url: `/pages/heart/heart/result?data=${dataStr}`
					})
				} catch (error) {
					console.error('跳转结果页面失败:', error)
					uni.showToast({
						title: '跳转失败',
						icon: 'none'
					})
				}
			},

			// 获取当前时间
			getCurrentTime() {
				const now = new Date()
				const year = now.getFullYear()
				const month = String(now.getMonth() + 1).padStart(2, '0')
				const day = String(now.getDate()).padStart(2, '0')
				const hours = String(now.getHours()).padStart(2, '0')
				const minutes = String(now.getMinutes()).padStart(2, '0')
				return `${year}-${month}-${day} ${hours}:${minutes}`
			}
		}
	}
</script>

<style scoped>
	.container {
		padding: 15rpx;
		background-color: #f8f8f8;
		min-height: 100vh;
	}

	.slogan-text {
		font-size: 30rpx;
		color: #ffa726; /* 焦虑评测使用橙色主题 */
		text-align: center;
		margin-top: 15rpx;
		font-weight: bold;
	}

	.description {
		font-size: 28rpx;
		color: #666;
		text-align: center;
		margin-bottom: 40rpx;
		margin-top: 20rpx;
	}

	.question-list {
		background-color: #fff;
		border-radius: 20rpx;
		padding: 30rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
		margin-bottom: 40rpx;
	}

	.question-item {
		margin-bottom: 40rpx;
		border-bottom: 1rpx solid #eee;
		padding-bottom: 30rpx;
	}

	.question-item:last-child {
		border-bottom: none;
		margin-bottom: 0;
		padding-bottom: 0;
	}

	.question-text {
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 20rpx;
		color: #333;
	}

	.options-group {
		display: flex;
		flex-direction: column;
	}

	.uni-list-cell {
		display: flex;
		align-items: center;
		margin-bottom: 15rpx;
		font-size: 28rpx;
		color: #555;
	}

	.uni-list-cell:last-child {
		margin-bottom: 0;
	}

	radio {
		transform: scale(0.8);
		margin-right: 15rpx;
	}

	.submit-button {
		background-color: #ffa726; /* 焦虑评测使用橙色主题 */
		color: #fff;
		border-radius: 20rpx;
		font-size: 30rpx;
		padding: 15rpx 0;
		margin-bottom: 40rpx;
	}

	.loading-container {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 100rpx 0;
		background-color: #fff;
		border-radius: 20rpx;
		margin-bottom: 40rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
	}

	.loading-text {
		font-size: 32rpx;
		color: #666;
	}
</style>