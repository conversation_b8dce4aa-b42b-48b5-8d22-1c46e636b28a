<template>
	<view class="logistics-container">
		<view class="logistics-header">
			<view class="tracking-info">
				<text class="tracking-number">快递单号：{{ trackingNumber }}</text>
				<text class="courier-company">极兔速递</text>
			</view>
			<view class="current-status">
				<text class="status-text">{{ currentStatus }}</text>
				<text class="status-time">{{ currentTime }}</text>
			</view>
		</view>

		<view class="logistics-timeline">
			<view class="timeline-item" v-for="(item, index) in logisticsData" :key="index" :class="{ 'active': index === 0 }">
				<view class="timeline-dot"></view>
				<view class="timeline-content">
					<view class="timeline-time">{{ item.time }}</view>
					<view class="timeline-desc">{{ item.description }}</view>
					<view class="timeline-location" v-if="item.location">{{ item.location }}</view>
				</view>
			</view>
		</view>

		<view class="logistics-map">
			<text class="map-title">运输路线</text>
			<view class="map-placeholder">
				<uni-icons type="location" size="40" color="#ccc"></uni-icons>
				<text>地图功能开发中...</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				trackingNumber: '',
				currentStatus: '运输中',
				currentTime: '',
				logisticsData: []
			}
		},
		onLoad(options) {
			this.trackingNumber = options.trackingNumber || 'YT1234567890'
			this.initLogisticsData()
		},
		methods: {
			initLogisticsData() {
				const now = new Date()
				const today = now.toLocaleDateString()
				const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000).toLocaleDateString()
				
				this.currentStatus = '派件中'
				this.currentTime = `预计今天 ${now.getHours()}:${now.getMinutes().toString().padStart(2, '0')} 送达`
				
				this.logisticsData = [
					{
						time: `${today} 09:30:00`,
						description: '【广州市】快件已到达【广州天河区营业点】，正在派件中',
						location: '广州天河区营业点'
					},
					{
						time: `${today} 07:20:00`,
						description: '【广州市】快件已从【广州转运中心】发出，下一站【广州天河区营业点】',
						location: '广州转运中心'
					},
					{
						time: `${yesterday} 23:45:00`,
						description: '【广州市】快件已到达【广州转运中心】',
						location: '广州转运中心'
					},
					{
						time: `${yesterday} 20:30:00`,
						description: '【深圳市】快件已从【深圳转运中心】发出，下一站【广州转运中心】',
						location: '深圳转运中心'
					},
					{
						time: `${yesterday} 18:15:00`,
						description: '【深圳市】快件已到达【深圳转运中心】',
						location: '深圳转运中心'
					},
					{
						time: `${yesterday} 16:00:00`,
						description: '【深圳市】商家已发货，快件已交给极兔速递',
						location: '深圳南山区营业点'
					}
				]
			}
		}
	}
</script>

<style lang="scss">
	.logistics-container {
		min-height: 100vh;
		background: #f8f9fa;
		padding: 20rpx;
	}

	.logistics-header {
		background: #fff;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);

		.tracking-info {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 20rpx;

			.tracking-number {
				font-size: 28rpx;
				color: #333;
				font-weight: 500;
			}

			.courier-company {
				font-size: 24rpx;
				color: #67c23a;
				background: rgba(103, 194, 58, 0.1);
				padding: 8rpx 16rpx;
				border-radius: 20rpx;
			}
		}

		.current-status {
			.status-text {
				font-size: 32rpx;
				color: #67c23a;
				font-weight: 600;
				display: block;
				margin-bottom: 8rpx;
			}

			.status-time {
				font-size: 24rpx;
				color: #666;
			}
		}
	}

	.logistics-timeline {
		background: #fff;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);

		.timeline-item {
			position: relative;
			padding-left: 60rpx;
			padding-bottom: 40rpx;

			&:last-child {
				padding-bottom: 0;

				&::after {
					display: none;
				}
			}

			&::after {
				content: '';
				position: absolute;
				left: 20rpx;
				top: 40rpx;
				width: 2rpx;
				height: calc(100% - 20rpx);
				background: #e4e7ed;
			}

			&.active {
				.timeline-dot {
					background: #67c23a;
					border-color: #67c23a;
				}

				.timeline-time {
					color: #67c23a;
				}

				.timeline-desc {
					color: #333;
					font-weight: 500;
				}
			}

			.timeline-dot {
				position: absolute;
				left: 12rpx;
				top: 8rpx;
				width: 16rpx;
				height: 16rpx;
				border-radius: 50%;
				background: #e4e7ed;
				border: 3rpx solid #e4e7ed;
			}

			.timeline-content {
				.timeline-time {
					font-size: 24rpx;
					color: #909399;
					margin-bottom: 8rpx;
					display: block;
				}

				.timeline-desc {
					font-size: 28rpx;
					color: #606266;
					line-height: 1.5;
					margin-bottom: 8rpx;
				}

				.timeline-location {
					font-size: 24rpx;
					color: #909399;
				}
			}
		}
	}

	.logistics-map {
		background: #fff;
		border-radius: 16rpx;
		padding: 30rpx;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);

		.map-title {
			font-size: 28rpx;
			color: #333;
			font-weight: 500;
			margin-bottom: 20rpx;
			display: block;
		}

		.map-placeholder {
			height: 300rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			background: #f5f7fa;
			border-radius: 12rpx;
			gap: 20rpx;

			text {
				font-size: 24rpx;
				color: #909399;
			}
		}
	}
</style>
