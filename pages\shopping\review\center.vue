<template>
  <view class="review-center">
    <!-- 我的评价模块 -->
    <view class="my-review-section">
      <view class="section-header">
        <text class="section-title">我的评价</text>
        <view class="view-all" @click="goToMyReviews">
          <text>查看全部</text>
          <uni-icons type="right" size="14" color="#999"></uni-icons>
        </view>
      </view>
      
      <view class="review-stats">
        <image :src="userInfo.avatar || '/static/images/profile.jpg'" class="user-avatar" />
        <view class="stats-grid">
          <view class="stat-item">
            <text class="stat-number">{{ reviewStats.totalReviews }}</text>
            <text class="stat-label">已评价</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ reviewStats.likes }}</text>
            <text class="stat-label">获赞</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ reviewStats.comments }}</text>
            <text class="stat-label">获评论</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ reviewStats.views }}</text>
            <text class="stat-label">被浏览</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 待评价和已评价选项卡 -->
    <view class="tab-container">
      <view class="tab-header">
        <view 
          class="tab-item" 
          :class="{ active: activeTab === 'pending' }"
          @click="switchTab('pending')"
        >
          <text>待评价</text>
          <text class="tab-count" v-if="pendingCount > 0">({{ pendingCount }})</text>
        </view>
        <view 
          class="tab-item" 
          :class="{ active: activeTab === 'completed' }"
          @click="switchTab('completed')"
        >
          <text>已评价</text>
          <text class="tab-count" v-if="completedCount > 0">({{ completedCount }})</text>
        </view>
      </view>

      <!-- 待评价列表 -->
      <view class="tab-content" v-if="activeTab === 'pending'">
        <view v-if="pendingOrders.length === 0" class="empty-state">
          <text class="iconfontA icon-pingjia empty-icon"></text>
          <text class="empty-text">暂无待评价订单</text>
        </view>
        
        <view v-else class="order-list">
          <view class="order-item" v-for="order in pendingOrders" :key="order.id">
            <view class="order-header">
              <text class="order-time">{{ order.createTime }}</text>
              <text class="order-status">待评价</text>
            </view>
            
            <view class="product-list">
              <view class="product-item" v-for="product in order.products" :key="product.id">
                <image :src="product.image" class="product-image" />
                <view class="product-info">
                  <text class="product-name">{{ product.name }}</text>
                  <text class="product-spec">{{ product.spec }}</text>
                  <text class="product-price">¥{{ product.price }}</text>
                </view>
                <view class="product-actions">
                  <button class="review-btn" @click="goToReview(order.id, product.id)">
                    评价
                  </button>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 已评价列表 -->
      <view class="tab-content" v-if="activeTab === 'completed'">
        <view v-if="completedReviews.length === 0" class="empty-state">
          <text class="iconfontA icon-pingjia empty-icon"></text>
          <text class="empty-text">暂无评价记录</text>
        </view>
        
        <view v-else class="review-list">
          <view class="review-item" v-for="review in completedReviews" :key="review.id">
            <view class="review-header">
              <image :src="userInfo.avatar || '/static/images/profile.jpg'" class="reviewer-avatar" />
              <view class="reviewer-info">
                <text class="reviewer-name">{{ userInfo.nickname || '匿名用户' }}</text>
                <text class="review-time">{{ review.createTime }}评价</text>
              </view>
              <view class="more-actions" @click="showMoreActions(review.id)">
                <uni-icons type="more-filled" size="16" color="#999"></uni-icons>
              </view>
            </view>
            
            <view class="review-content">
              <text class="review-text">{{ review.content }}</text>
              <view class="review-rating">
                <text>综合评分: </text>
                <uni-rate :value="Number(review.rating)" readonly size="14" color="#ff9900"></uni-rate>
                <text class="rating-text">{{ review.rating }}星</text>
              </view>
              
              <!-- 评价的商品信息 -->
              <view class="reviewed-product">
                <image :src="review.product.image" class="product-thumb" />
                <view class="product-detail">
                  <text class="product-title">{{ review.product.name }}</text>
                  <text class="product-spec">{{ review.product.spec }}</text>
                </view>
                <button class="reorder-btn" @click="reorder(review.product.id)">
                  再次拼单
                </button>
              </view>
            </view>
            
            <view class="review-actions">
              <view class="action-item" @click="toggleLike(review.id)">
                <uni-icons :type="review.isLiked ? 'heart-filled' : 'heart'" size="16" :color="review.isLiked ? '#ff5555' : '#999'"></uni-icons>
                <text>赞</text>
              </view>
              <view class="action-item" @click="showComments(review.id)">
                <uni-icons type="chat" size="16" color="#999"></uni-icons>
                <text>评论</text>
              </view>
              <button class="add-review-btn" @click="addReview(review.product.id)">
                追加评价
              </button>
              <button class="share-btn" @click="shareReview(review.id)">
                分享
              </button>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      activeTab: 'pending', // pending: 待评价, completed: 已评价
      userInfo: {},
      reviewStats: {
        totalReviews: 175,
        likes: 1,
        comments: 1,
        views: 8818
      },
      pendingOrders: [],
      completedReviews: []
    }
  },
  
  computed: {
    pendingCount() {
      return this.pendingOrders.length;
    },
    
    completedCount() {
      return this.completedReviews.length;
    }
  },
  
  onLoad() {
    this.loadUserInfo();
    this.loadPendingOrders();
    this.loadCompletedReviews();
    this.loadReviewStats();
  },
  
  onShow() {
    // 页面显示时刷新数据
    this.refreshAllData();
  },
  
  methods: {
    // 加载用户信息
    loadUserInfo() {
      // 从存储中获取用户信息
      this.userInfo = uni.getStorageSync('userInfo') || {
        nickname: '用户',
        avatar: '/static/images/profile.jpg'
      };
     
    },
    
    // 切换选项卡
    switchTab(tab) {
      this.activeTab = tab;
    },
    
    // 跳转到我的评价页面
    goToMyReviews() {
      uni.navigateTo({
        url: '/pages/shopping/review/my-reviews'
      });
    },
    
    // 加载待评价订单
    loadPendingOrders() {
      const orders = uni.getStorageSync('orders') || [];
      this.pendingOrders = orders.filter(order =>
        order.status === 'received' && !order.isReviewed
      );
    },
    
    // 加载已评价记录
    loadCompletedReviews() {
      const reviews = uni.getStorageSync('userReviews') || [];
      this.completedReviews = reviews.sort((a, b) =>
        new Date(b.createTime) - new Date(a.createTime)
      );
    },
    
    // 加载评价统计
    loadReviewStats() {
      const reviews = uni.getStorageSync('userReviews') || [];
      this.reviewStats.totalReviews = reviews.length;
      // 其他统计数据可以从后端获取
    },
    
    // 刷新所有数据
    refreshAllData() {
      this.loadPendingOrders();
      this.loadCompletedReviews();
      this.loadReviewStats();
    },

    // 跳转到评价页面
    goToReview(orderId, productId) {
      uni.navigateTo({
        url: `/pages/shopping/appraise/photo?orderId=${orderId}&productId=${productId}`
      });
    },
    
    // 再次拼单
    reorder(productId) {
      uni.navigateTo({
        url: `/pages/shopping/detail/detail?id=${productId}`
      });
    },
    
    // 追加评价
    addReview(productId) {
      uni.navigateTo({
        url: `/pages/shopping/appraise/photo?productId=${productId}&type=add`
      });
    },
    
    // 分享评价
    shareReview(reviewId) {
      uni.showActionSheet({
        itemList: ['分享到微信', '分享到朋友圈', '复制链接'],
        success: (res) => {
          uni.showToast({
            title: '分享功能开发中',
            icon: 'none'
          });
        }
      });
    },
    
    // 切换点赞
    toggleLike(reviewId) {
      const reviews = uni.getStorageSync('userReviews') || [];
      const reviewIndex = reviews.findIndex(r => r.id === reviewId);
      
      if (reviewIndex !== -1) {
        reviews[reviewIndex].isLiked = !reviews[reviewIndex].isLiked;
        uni.setStorageSync('userReviews', reviews);
        this.loadCompletedReviews();
      }
    },
    
    // 显示评论
    showComments(reviewId) {
      uni.showToast({
        title: '评论功能开发中',
        icon: 'none'
      });
    },
    
    // 显示更多操作
    showMoreActions(reviewId) {
      uni.showActionSheet({
        itemList: ['删除评价', '举报'],
        success: (res) => {
          if (res.tapIndex === 0) {
            this.deleteReview(reviewId);
          }
        }
      });
    },
    
    // 删除评价
    deleteReview(reviewId) {
      uni.showModal({
        title: '删除评价',
        content: '确定要删除这条评价吗？',
        success: (res) => {
          if (res.confirm) {
            const reviews = uni.getStorageSync('userReviews') || [];
            const filteredReviews = reviews.filter(r => r.id !== reviewId);
            uni.setStorageSync('userReviews', filteredReviews);
            this.loadCompletedReviews();
            this.loadReviewStats();
            
            uni.showToast({
              title: '删除成功',
              icon: 'success'
            });
          }
        }
      });
    }
  }
}
</script>

<style scoped>
.review-center {
  background: #f5f5f5;
  min-height: 100vh;
}

/* 我的评价模块 */
.my-review-section {
  background: #fff;
  margin-bottom: 20rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 24rpx 16rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.view-all {
  display: flex;
  align-items: center;
  gap: 8rpx;
  color: #999;
  font-size: 28rpx;
}

.review-stats {
  display: flex;
  align-items: center;
  padding: 0 24rpx 32rpx;
  gap: 32rpx;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
}

.stats-grid {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16rpx;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.2;
}

.stat-label {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

/* 选项卡 */
.tab-container {
  background: #fff;
}

.tab-header {
  display: flex;
  border-bottom: 1rpx solid #eee;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 32rpx 0;
  font-size: 32rpx;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: #ff5555;
  font-weight: bold;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: #ff5555;
  border-radius: 2rpx;
}

.tab-count {
  color: #ff5555;
  font-size: 24rpx;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 0;
}

.empty-icon {
  font-size: 120rpx;
  color: #ddd;
  display: block;
  margin-bottom: 24rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 订单列表 */
.order-list {
  padding: 0 24rpx;
}

.order-item {
  background: #fff;
  border-radius: 16rpx;
  margin: 24rpx 0;
  padding: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.05);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.order-time {
  font-size: 24rpx;
  color: #999;
}

.order-status {
  font-size: 24rpx;
  color: #ff5555;
}

.product-item {
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 16rpx 0;
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
}

.product-info {
  flex: 1;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.product-spec {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-bottom: 8rpx;
}

.product-price {
  font-size: 32rpx;
  color: #ff5555;
  font-weight: bold;
}

.review-btn {
  background: #ff5555;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  padding: 16rpx 32rpx;
  font-size: 24rpx;
}

/* 评价列表 */
.review-list {
  padding: 0 24rpx;
}

.review-item {
  background: #fff;
  border-radius: 16rpx;
  margin: 24rpx 0;
  padding: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.05);
}

.review-header {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.reviewer-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
}

.reviewer-info {
  flex: 1;
}

.reviewer-name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  display: block;
}

.review-time {
  font-size: 24rpx;
  color: #999;
  margin-top: 4rpx;
}

.review-content {
  margin-bottom: 24rpx;
}

.review-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  display: block;
  margin-bottom: 16rpx;
}

.review-rating {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 24rpx;
  font-size: 24rpx;
  color: #666;
}

.rating-text {
  color: #ff9900;
}

.reviewed-product {
  display: flex;
  align-items: center;
  gap: 16rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
  padding: 16rpx;
}

.product-thumb {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
}

.product-detail {
  flex: 1;
}

.product-title {
  font-size: 26rpx;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.reorder-btn {
  background: transparent;
  color: #ff5555;
  border: 2rpx solid #ff5555;
  border-radius: 8rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
}

.review-actions {
  display: flex;
  align-items: center;
  gap: 32rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #f0f0f0;
}

.action-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #999;
}

.add-review-btn,
.share-btn {
  background: transparent;
  color: #666;
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  margin-left: auto;
}

.add-review-btn {
  margin-left: auto;
  margin-right: 16rpx;
}
</style>
