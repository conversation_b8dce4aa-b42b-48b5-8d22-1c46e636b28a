<template>
	<view class="search-page">
			<view class="secondary-search-bar" :style="{ paddingTop: (statusBarHeight + 10) + 'px' }">
			<uni-icons type="left" size="24" color="#333" @click="goBack" class="back-icon"></uni-icons>
			<view class="input-uni-searchbar">
				<text class="cuIcon-search search-icon"></text>
				<input class="uni-input" confirm-type="search"  @confirm="search" v-model="searchKeyword" />
			</view>
			<text class="search-btn" @click="search">搜索</text>
		</view>

		<view class="content">
			<!-- 历史搜索 -->
			<view class="history-section" v-if="historyList.length > 0">
				<view class="section-header">
					<text class="title">历史</text>
					<text class="cuIcon-delete" @click="clearHistory"></text>
				</view>
				<view class="history-tags">
					<text class="cu-tag round" v-for="(item, index) in historyList" :key="index" @click="clickHistory(item)">{{ item }}</text>
				</view>
			</view>

			<!-- 热门搜索 -->
			<view class="hot-section">
				<view class="section-header">
					<text class="title">热门</text>
					<text class="cuIcon-firefill hot-icon"></text>
				</view>
				<view class="hot-list grid col-2">
					<view class="hot-item" v-for="(item, index) in hotList" :key="index">
						<text class="index" :class="{'top-index': index < 3}">{{ index + 1 }}</text>
						<text class="text" @click="clickHot(item.text)">{{ item.text }}</text>
					</view>
				</view>
			</view>

			<!-- 搜索结果 -->
			<view class="search-results-section" v-if="searchResults.length > 0">
				<view class="section-header">
					<text class="title">搜索结果</text>
					<text class="result-count">共 {{ searchResults.length }} 条</text>
				</view>
				<view class="results-list">
					<view class="result-item" v-for="(result, index) in searchResults" :key="index" @click="goToVideoDetail(result)">
						<!-- 如果是对象格式的视频数据 -->
						<view v-if="result && result.title" class="video-result">
							<image :src="getImageSrc(result)" class="video-thumb" mode="heightFix"></image>
						<view class="video-info">
								<text class="video-title">{{ getTitle(result) }}</text>
								<text class="video-author">作者：{{ getAuthor(result) }}</text>
								<text class="video-school" v-if="result.school">来源：{{ result.school }}</text>
								<text class="video-views">观看：{{ formatViews(result.views) }}</text>
							</view>
						</view>
						<!-- 如果是字符串格式（兼容旧数据） -->
						<text v-else class="simple-result">{{ result }}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import uniIcons from '@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue'
	import { videoViews } from '@/api/system/video.js'

	export default {
		components: {
			uniIcons
		},
		data() {
			return {
				searchKeyword: '',
				historyList: [],
				hotList: [], // 将从传递的数据中生成
				allCourses: [], // 存储完整的视频数据对象
				searchResults: [], // 搜索结果
				statusBarHeight: 0 
			}
		},
		onLoad() {
			this.loadHistory();
			this.loadVideoData();

			// 获取系统信息，设置状态栏高度
			uni.getSystemInfo({
				success: (res) => {
					this.statusBarHeight = res.statusBarHeight;
				}
			});
		},

		onShow() {
			// 页面显示时的处理
		},

		onHide() {
			// 页面隐藏时的处理
		},
		methods: {
			// 从API加载视频数据
			async loadVideoData() {
				console.log('搜索页面 - 从API加载视频数据');

				try {
					const { videoList } = require('@/api/system/video.js');
					const response = await videoList();

					if (response && response.rows && response.rows.length > 0) {
						console.log('搜索页面 - 成功从API获取数据，数量:', response.rows.length);

						// 按观看次数排序（降序）
						const sortedData = this.sortVideosByViews(response.rows);
						this.allCourses = sortedData;

						// 生成热门搜索数据
						this.generateHotList(sortedData);
					} else {
						console.log('搜索页面 - API返回数据为空');
						this.allCourses = [];
						this.hotList = [];
					}
				} catch (error) {
					console.error('搜索页面 - 从API加载数据失败:', error);
					this.allCourses = [];
					this.hotList = [];
				}
			},

			// 按观看次数排序视频数据
			sortVideosByViews(videoData) {
				return videoData.sort((a, b) => {
					const viewsA = this.parseViewsToNumber(a.views || 0);
					const viewsB = this.parseViewsToNumber(b.views || 0);
					return viewsB - viewsA; // 降序排列
				});
			},

			// 解析观看次数为数字
			parseViewsToNumber(views) {
				if (typeof views === 'number') {
					return views
				}

				if (typeof views === 'string') {
					const numStr = views.replace(/[^\d]/g, '')
					return parseInt(numStr) || 0
				}

				return 0
			},

			// 格式化观看次数显示
			formatViews(views) {
				const num = this.parseViewsToNumber(views)

				if (num >= 100000000) {
					return (num / 100000000).toFixed(1) + '亿次观看'
				} else if (num >= 10000) {
					return (num / 10000).toFixed(1) + '万次观看'
				} else if (num >= 1000) {
					return (num / 1000).toFixed(1) + 'k次观看'
				} else {
					return num + '次观看'
				}
			},

			// 优化标题显示（提取最后一个-后面的内容）
			formatTitle(title) {
				if (!title) return '';

				// 按 - 分割标题，取最后一段
				const parts = title.split('-');
				if (parts.length >= 3) {
					// 如果有3段或更多，取最后一段
					return parts[parts.length - 1].trim();
				}

				// 如果分割后少于3段，返回原标题
				return title;
			},

			// 生成热门搜索数据
			generateHotList(videoData) {
				console.log('搜索页面 - 生成热门搜索数据');

				if (!videoData || videoData.length === 0) {
					this.hotList = [];
					return;
				}

				// 从视频标题中提取关键词作为热门搜索
				const keywords = [];
				videoData.slice(0, 10).forEach(video => { // 取前8个视频
					if (video.title) {
						const formattedTitle = this.formatTitle(video.title);
						if (formattedTitle && formattedTitle !== video.title) {
							keywords.push(formattedTitle);
						}
					}
				});

				// 去重并生成热门搜索列表
				const uniqueKeywords = [...new Set(keywords)];
				this.hotList = uniqueKeywords.slice(0, 10).map(text => ({ text })); // 最多10个热门搜索

				console.log('搜索页面 - 生成的热门搜索:', this.hotList);
			},

			goBack() {
				uni.navigateBack({
					fail: () => {
						// If there's no previous page, navigate to the home page
						uni.switchTab({
							url: '/pages/index'
						});
					}
				});
			},
			loadHistory() {
				const history = uni.getStorageSync('searchHistory');
				if (history) {
					this.historyList = JSON.parse(history);
				}
			},
			saveHistory(keyword) {
				if (keyword && !this.historyList.includes(keyword)) {
					this.historyList.unshift(keyword);
					if (this.historyList.length > 10) { // Limit history to 10 items
						this.historyList.pop();
					}
					uni.setStorageSync('searchHistory', JSON.stringify(this.historyList));
				}
			},
			clearHistory() {
				uni.removeStorageSync('searchHistory');
				this.historyList = [];
			},
			clickHistory(item) {
				this.searchKeyword = item;
				this.search();
			},
			clickHot(item) {
				this.searchKeyword = item;
				this.search();
			},
			search() {
				if (!this.searchKeyword) {
					uni.showToast({
						title: '请输入搜索内容',
						icon: 'none'
					});
					return;
				}
				this.saveHistory(this.searchKeyword);
				
				// 根据title进行搜索（优化后只搜索标题）
				const keyword = this.searchKeyword.toLowerCase();
				const results = this.allCourses.filter(course => {
					// 如果allCourses是字符串数组（兼容旧数据）
					if (typeof course === 'string') {
						return course.toLowerCase().includes(keyword);
					}

					// 如果allCourses是对象数组（新的API数据）
					if (typeof course === 'object' && course !== null) {
						const title = (course.title || '').toLowerCase();
						return title.includes(keyword);
					}

					return false;
				});

				// 按观看次数排序搜索结果（降序）
				const sortedResults = this.sortVideosByViews(results);

				console.log('搜索关键词:', this.searchKeyword, '结果数量:', sortedResults.length);
				
				uni.showToast({
					title: `搜索到 ${sortedResults.length} 条结果`,
					icon: 'none'
				});
				this.searchResults = sortedResults;
			},

			// 格式化观看次数
			formatViews(views) {
				if (!views || views === 0) return '0次观看';

				const num = typeof views === 'string' ? parseInt(views) || 0 : views;

				if (num >= 100000000) {
					return (num / 100000000).toFixed(1) + '亿次观看';
				} else if (num >= 10000) {
					return (num / 10000).toFixed(1) + '万次观看';
				} else if (num >= 1000) {
					return (num / 1000).toFixed(1) + 'k次观看';
				} else {
					return num + '次观看';
				}
			},

			// 获取图片源
			getImageSrc(result) {
				return result && result.image ? result.image : '/static/images/default-video.jpg';
			},

			// 获取标题（优化显示格式）
			getTitle(result) {
				if (!result || !result.title) return '未知标题';
				return this.formatTitle(result.title.trim()); // 去除前后空格
			},

			// 获取作者
			getAuthor(result) {
				return result && result.author ? result.author : '未知作者';
			},

			// 跳转到视频详情页面（支持观看次数自增）
			async goToVideoDetail(videoData) {
				console.log('🔍 搜索页面 - 点击视频:', videoData);
				console.log('🔍 搜索页面 - 视频ID:', videoData?.id, '当前观看次数:', videoData?.views);

				// 如果没有id属性，不处理跳转
				if (!videoData || !videoData.id) {
					console.error('❌ 搜索页面 - 视频数据无效，无法跳转');
					uni.showToast({
						title: '暂不支持跳转',
						icon: 'none'
					});
					return;
				}

				try {
					console.log('🚀 搜索页面 - 开始调用观看次数自增');
					// 1. 先调用观看次数自增
					await this.incrementVideoViews(videoData.id, videoData.views || 0);
					console.log('✅ 搜索页面 - 观看次数自增完成');

					// 2. 更新videoData中的观看次数为自增后的值
					const numericViews = this.parseViewsToNumber(videoData.views || 0);
					const newViews = numericViews + 1;
					const updatedVideoData = {
						...videoData,
						views: newViews
					};
					console.log('🔄 搜索页面 - 更新videoData观看次数:', videoData.views, '->', newViews);

					// 3. 跳转到详情页（使用更新后的数据）
					const encodedData = encodeURIComponent(JSON.stringify(updatedVideoData));
					console.log('🔗 搜索页面 - 准备跳转到详情页，URL参数:', {
						videoId: updatedVideoData.id,
						views: updatedVideoData.views,
						encodedDataLength: encodedData.length
					});

					uni.navigateTo({
						url: `/pages/gather/video_a/detail?videoId=${updatedVideoData.id}&courseData=${encodedData}`,
						success: function(res) {
							console.log('✅ 从搜索页面跳转成功:', res);
						},
						fail: function(err) {
							console.error('❌ 从搜索页面跳转失败:', err);
							uni.showToast({
								title: '页面跳转失败',
								icon: 'none'
							});
						}
					});
				} catch (error) {
					console.error('❌ 搜索页面观看次数自增失败:', error);
					// 即使观看次数自增失败，也允许跳转（使用原始数据）
					const encodedData = encodeURIComponent(JSON.stringify(videoData));
					uni.navigateTo({
						url: `/pages/gather/video_a/detail?videoId=${videoData.id}&courseData=${encodedData}`
					});
				}
			},

			// 视频观看次数自增
			async incrementVideoViews(videoId, currentViews) {
				try {
					console.log('🎬 搜索页面 - 自增视频观看次数，ID:', videoId, '当前次数:', currentViews);

					const numericViews = this.parseViewsToNumber(currentViews);
					const newViews = numericViews + 1;

					// 调用API更新观看次数
					const updateData = {
						id: videoId,
						views: newViews
					};

					await videoViews(updateData);
					console.log('✅ 搜索页面 - 观看次数自增成功，新次数:', newViews);

					// 立即更新本地显示数据
					this.updateLocalVideoViews(videoId, newViews);

				} catch (error) {
					console.error('❌ 搜索页面 - 视频观看次数自增失败:', error);
					console.error('❌ 搜索页面 - 错误详情:', error.message || error);
				}
			},

			// 立即更新本地显示的播放次数
			updateLocalVideoViews(videoId, newViews) {
				

				// 更新搜索结果中的播放次数
				const resultIndex = this.searchResults.findIndex(result => result.id == videoId);
				if (resultIndex !== -1) {
					this.$set(this.searchResults, resultIndex, {
						...this.searchResults[resultIndex],
						views: newViews
					});
				}

				// 更新完整课程数据中的播放次数
				const courseIndex = this.allCourses.findIndex(course => course.id == videoId);
				if (courseIndex !== -1) {
					this.$set(this.allCourses, courseIndex, {
						...this.allCourses[courseIndex],
						views: newViews
					});
				}

				// 强制更新视图
				this.$forceUpdate();
			}
		}
	}
</script>

<style lang="scss">
	page {
		background-color: #ffffff;
	}

	.search-page {
		padding-top: 0;
	}

	/* Removed .nav-bar CSS rules as the component is commented out */

	.secondary-search-bar {
		display: flex;
		align-items: center;
		padding: 5px 10px;
		background-color: #fff;
		border-bottom: 1px solid #eee;
		width: 100%;
		box-sizing: border-box;

		.back-icon {
			font-size: 25px;
			color: #333;
			margin-right: 8px;
			padding-left: 0;
		}

		.input-uni-searchbar {
			flex: 1;
			display: flex;
			align-items: center;
			height: 40px;
			background-color: #f0f0f0;
			border-radius: 20px;
			padding: 0 10px;
		}

		.search-icon {
			font-size: 22px;
			color: #999;
			margin-right: 5px;
		}

		.uni-input {
			flex: 1;
			font-size: 16px;
			color: #333;
		}

		.search-btn {
			font-size: 18px;
			color: #00d5ffd1;
			margin-left: 10px;
			margin-right: 0;
		}
	}

	.content {
		padding: 10px 15px;
		position: relative;
		background-color: #ffffff;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

		.section-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 10px;

			.title {
				font-size: 16px;
				font-weight: bold;
				color: #333;
			}

			.cuIcon-delete {
				font-size: 18px;
				color: #999;
			}

			.hot-icon {
				font-size: 18px;
				color: #ff5500; /* Hot red color */
				margin-left: 5px;
			}
		}

		.history-tags {
			display: flex;
			flex-wrap: wrap;

			.cu-tag {
				margin-right: 8px;
				margin-bottom: 8px;
				background-color: #eee;
				color: #666;
				font-size: 13px;
				padding: 5px 12px;
				border-radius: 15px;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}
		}

		.hot-section {
			margin-top: 15px;
		}

		.hot-list {
			display: flex;
			flex-wrap: wrap;

			.hot-item {
				display: flex;
				align-items: center;
				width: 50%;
				margin-bottom: 10px;

				.index {
					font-size: 15px;
					font-weight: bold;
					margin-right: 10px;
					color: #999;

					&.top-index {
						color: #ff5500; /* Hot red for top 3 */
					}
				}

				.text {
					font-size: 15px;
					color: #333;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
					flex: 1;
				}
			}
		}
	}

	.search-results-section {
		margin-top: 20px;

		.section-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 10px;

			.title {
				font-size: 16px;
				font-weight: bold;
				color: #333;
			}

			.result-count {
				font-size: 14px;
				color: #999;
			}
		}

		.results-list {
			background-color: #fff;
			border-radius: 10px;
			padding: 10px 15px;
			box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

			.result-item {
				padding: 10px 0;
				border-bottom: 1px solid #f0f0f0;
				cursor: pointer;

				&:last-child {
					border-bottom: none;
				}

				.video-result {
					display: flex;
					align-items: flex-start;

					.video-thumb {
						width: 60px;
						height: 90px;
						border-radius: 8px;
						margin-right: 12px;
						flex-shrink: 0;
					}

					.video-info {
						flex: 1;
						display: flex;
						flex-direction: column;

						.video-title {
							font-size: 16px;
							font-weight: bold;
							color: #333;
							margin-bottom: 4px;
							line-height: 1.3;
							display: -webkit-box;
							-webkit-line-clamp: 2;
							-webkit-box-orient: vertical;
							overflow: hidden;
							text-overflow: ellipsis;
						}

						.video-author,
						.video-school,
						.video-views {
							font-size: 12px;
							color: #666;
							margin-bottom: 2px;
						}

						.video-author {
							color: #007aff;
						}
					}
				}

				.simple-result {
					font-size: 14px;
					color: #333;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
				}

				&:active {
					background-color: #f5f5f5;
				}
			}
		}
	}
</style>
